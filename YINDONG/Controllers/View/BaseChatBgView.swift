//
//  BaseChatBgView.swift
//  YINDONG
//
//  Created by jj on 2025/6/25.
//

import UIKit

class BaseChatBgView: UIView {
    
    var leftSize = CGSizeMake(20, 20)
    var rightSize = CGSize(width: 25, height: 20)
    
    var roomSize = CGSize(width: 50, height: 20)
    
    lazy var bgImgV: UIImageView = {
        let view = UIImageView()
        view.layer.cornerRadius = 8
        view.backgroundColor = UIColor.white.withAlphaComponent(0.5)
        view.isHidden = true
        return view
    }()
    
    lazy var minXMinYImgV: SVGAnimationPlayer = {
        let view = SVGAnimationPlayer(frame: CGRectMake(0, 0, 40, 18))
        view.contentMode = .scaleAspectFill
        view.isHidden = true
        return view
    }()
    
    lazy var minXMaxYImgV: SVGAnimationPlayer = {
        let view = SVGAnimationPlayer(frame: CGRectMake(0, 0, 40, 18))
        view.contentMode = .scaleAspectFill
        view.isHidden = true
        return view
    }()
    
    lazy var maxXMinYImgV: SVGAnimationPlayer = {
        let view = SVGAnimationPlayer(frame: CGRectMake(0, 0, 40, 18))
        view.contentMode = .scaleAspectFill
        view.isHidden = true
        return view
    }()
    lazy var maxXMaxYImgV: SVGAnimationPlayer = {
        let view = SVGAnimationPlayer(frame: CGRectMake(0, 0, 40, 18))
        view.contentMode = .scaleAspectFill
        view.isHidden = true
        return view
    }()
    
    func reloadBg(){
        bgNomalStyle()
        //        if withType == .heisebeijing {
        bgImgV.isHidden = false
        bgImgV.backgroundColor = UIColor.white.withAlphaComponent(0.5)
        gradientLayer.isHidden = true
        //        }
    }
    func bgNomalStyle(){
        bgImgV.isHidden = true
        bgImgV.backgroundColor = .clear
        bgImgV.image = nil
        
        minXMinYImgV.isHidden = true
        minXMaxYImgV.isHidden = true
        maxXMinYImgV.isHidden = true
        maxXMaxYImgV.isHidden = true
    }
    
    func clear() {
        minXMinYImgV.isHidden = true
        minXMaxYImgV.isHidden = true
        maxXMinYImgV.isHidden = true
        maxXMaxYImgV.isHidden = true
        bgImgV.isHidden = true
    }

    /// Cell 重用时调用，彻底重置状态
    func resetForReuse() {
        // 停止所有动画
        stopAllAnimations()

        // 清除所有视图状态
        clear()

        // 重置背景图片
        bgImgV.image = nil
        bgImgV.backgroundColor = .clear

        // 重置渐变层
        gradientLayer.isHidden = true
        gradientLayer.colors = nil
    }

    /// 停止所有 SVGA 动画
    private func stopAllAnimations() {
        minXMinYImgV.clearResources()
        minXMaxYImgV.clearResources()
        maxXMinYImgV.clearResources()
        maxXMaxYImgV.clearResources()
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupGradientLayer()
        
        self.addSubview(bgImgV)
        self.addSubview(minXMinYImgV)
        self.addSubview(minXMaxYImgV)
        self.addSubview(maxXMinYImgV)
        self.addSubview(maxXMaxYImgV)
        
        bgImgV.snp.makeConstraints({ (make) in
            make.edges.equalToSuperview()
        })
        
        minXMinYImgV.snp.makeConstraints({ (make) in
            make.left.equalToSuperview()
            make.top.equalToSuperview()
            make.size.equalTo(leftSize)
        })
        minXMaxYImgV.snp.makeConstraints({ (make) in
            make.left.equalToSuperview()
            make.bottom.equalToSuperview()
            make.size.equalTo(leftSize)
        })
        
        maxXMinYImgV.snp.makeConstraints({ (make) in
            make.right.equalToSuperview()
            make.top.equalToSuperview()
            make.size.equalTo(rightSize)
        })
        
        maxXMaxYImgV.snp.makeConstraints({ (make) in
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.size.equalTo(rightSize)
        })
        
        reloadBg()
    }
    
    func creatRoom() {
        
        minXMinYImgV.snp.updateConstraints({ (make) in
            make.left.equalToSuperview().offset(-10)
            make.top.equalToSuperview().offset(-5)
            make.size.equalTo(roomSize)
        })
        minXMaxYImgV.snp.updateConstraints({ (make) in
            make.left.equalToSuperview().offset(-10)
            make.bottom.equalToSuperview().offset(5)
            make.size.equalTo(roomSize)
        })
        
        maxXMinYImgV.snp.updateConstraints({ (make) in
            make.right.equalToSuperview().offset(10)
            make.top.equalToSuperview().offset(-5)
            make.size.equalTo(roomSize)
        })
        
        maxXMaxYImgV.snp.updateConstraints({ (make) in
            make.right.equalToSuperview().offset(10)
            make.bottom.equalToSuperview().offset(5)
            make.size.equalTo(roomSize)
        })
        
    }
    
    
    override func layoutSubviews() {
        super.layoutSubviews()
        addGradientLayer(withCorner: bgImgV)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    ///边框图片
    func play(withMinXMinY:String?,minXMaxY:String?,maxXminY:String?,maxXMaxY:String?) {
        // 先停止之前的动画，避免重叠
        stopAllAnimations()

        reloadBg()

        // 使用无动画的方式设置，避免闪烁
        UIView.performWithoutAnimation {
            if let url = withMinXMinY, isValid(url) {
                minXMinYImgV.loadAnimation(named: url)
                minXMinYImgV.isHidden = false
            }
            if let url = minXMaxY, isValid(url) {
                minXMaxYImgV.loadAnimation(named: url)
                minXMaxYImgV.isHidden = false
            }
            if let url = maxXminY, isValid(url) {
                maxXMinYImgV.loadAnimation(named: url)
                maxXMinYImgV.isHidden = false
            }
            if let url = maxXMaxY, isValid(url) {
                maxXMaxYImgV.loadAnimation(named: url)
                maxXMaxYImgV.isHidden = false
            }
        }
    }
    let maskLayer = CAShapeLayer()
    let gradientLayer = CAGradientLayer()
    let lineWidth:CGFloat = 0.7
    
    func setupGradientLayer(){
        gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        gradientLayer.cornerRadius = layerCornerRadius
        maskLayer.lineWidth = lineWidth
        gradientLayer.mask = maskLayer
        maskLayer.fillColor = UIColor.clear.cgColor
        maskLayer.strokeColor = UIColor.blue.cgColor
        bgImgV.layer.addSublayer(gradientLayer)
    }
    func addGradientLayer(withCorner view: UIView) {
        let mapRect = CGRect(x: lineWidth/2, y: lineWidth/2, width: view.frame.size.width-lineWidth, height: view.frame.size.height-lineWidth)
        gradientLayer.frame = CGRect(x: 0, y: 0, width: view.frame.size.width, height: view.frame.size.height)
        let path = UIBezierPath(roundedRect: mapRect, cornerRadius: 8)
        maskLayer.path = path.cgPath
    }
    
}
