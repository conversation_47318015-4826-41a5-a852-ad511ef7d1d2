//
//  LWJumpManger.swift
//  YINDONG
//
//  Created by jj on 2025/3/12.
//

import UIKit

protocol Routable {
    static var routePath: String { get }
}

class LWJumpManger: NSObject {

    static func goTopIcDetail(m: TopicModel? = nil, code: Int? = nil, city: String? = nil) {
        
        ///如果是话题详情，不给跳转了，防止套娃
        var isHave = false
        if let navController = AppTool.getCurrentViewController().navigationController {
            for vc in navController.viewControllers {
                if vc is TopicDetailVC { // 替换 TargetViewController 为你要检查的 VC 类型
                    let newVV = vc as? TopicDetailVC
                    if newVV?.model?.id == m?.id || newVV?.locCode == code {
                        isHave = true
                    }
                    continue // 找到后可以提前退出循环
                }
            }
        }
        
        if isHave {
            return
        }
        
        let vc = TopicDetailVC()
        if let m = m {
            vc.model = m
        }
        if let code = code {
            vc.locCode = code
            vc.locCity = city
        }
        AppTool.getCurrentViewController().navigationController?.pushViewController(vc, animated: true)
    }
    
    static func goUserPage(id: Int? = kuser.imNumber) {
        guard let id = id else { return }
        
        let userPage = UserPageMainVC()
        userPage.imId = id.string
        AppTool.getCurrentViewController().navigationController?.pushViewController(userPage, animated: true)

    }
    
    ///跳转消息
    static func goChat(id: String?) {
        guard let id = id else { return }
        
        ///如果是话题详情，不给跳转了，防止套娃
        var isHave = false
        if let navController = AppTool.getCurrentViewController().navigationController {
            for vc in navController.viewControllers {
                if vc is LwChatDetailVC { // 替换 TargetViewController 为你要检查的 VC 类型
                    isHave = true
                    AppTool.getCurrentViewController().navigationController?.popToViewController(vc, animated: true)
                    break // 找到后可以提前退出循环
                }
            }
        }
        
        if isHave {
            return
        }
        
        let vc = LwChatDetailVC()
        vc.uid = id
        AppTool.getCurrentViewController().navigationController?.pushViewController(vc)
    }
    
    static func goUrl(_ url: String?) {
        guard let url = url else { return }
        let vc = LwBaseWebVC()
        vc.urlString = url
        AppTool.getCurrentViewController().navigationController?.pushViewController(vc)
    }
    
}
