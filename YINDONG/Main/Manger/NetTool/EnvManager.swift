//
//  EnvManager.swift
//  YINDONG
//
//  Created by jj on 2025/2/24.
//

import Foundation
import UIKit
import CryptoSwift


@_exported import Moya
@_exported import SmartCodable
@_exported import SwiftyJSON
@_exported import Async
@_exported import AttributedString
@_exported import RxSwift
@_exported import RxCocoa
@_exported import YYText
@_exported import ImSDK_Plus_Swift

enum EnvType: Int, CaseIterable {
    case test
    case dis
    case custom

    var displayName: String {
        switch self {
        case .test: return "测试"
        case .dis: return "正式"
        case .custom: return "自定义"
        }
    }
}

var kenv: EnvManager {
    return EnvManager.shared
}

// MARK: - Environment Configuration Models
struct APIConfig: Codable {
    var host: String
    var port: String?
    var description: String
    var timestamp: Date

    var fullURL: String {
        if let port = port, !port.isEmpty {
            return "http://\(host):\(port)"
        }
        return host.hasPrefix("http") ? host : "http://\(host)"
    }

    init(host: String, port: String? = nil, description: String) {
        self.host = host
        self.port = port
        self.description = description
        self.timestamp = Date()
    }
}

struct GameAPIConfig: Codable {
    var host: String
    var port: String?
    var description: String
    var timestamp: Date

    var fullURL: String {
        if let port = port, !port.isEmpty {
            return "http://\(host):\(port)"
        }
        return host.hasPrefix("http") ? host : "http://\(host)"
    }

    init(host: String, port: String? = nil, description: String) {
        self.host = host
        self.port = port
        self.description = description
        self.timestamp = Date()
    }
}

struct H5Config: Codable {
    var host: String
    var port: String?
    var description: String
    var timestamp: Date

    var fullURL: String {
        if let port = port, !port.isEmpty {
            return "http://\(host):\(port)"
        }
        return host.hasPrefix("http") ? host : "http://\(host)"
    }

    init(host: String, port: String? = nil, description: String) {
        self.host = host
        self.port = port
        self.description = description
        self.timestamp = Date()
    }
}

class EnvManager: NSObject {

    static var getAesKey: String {
        let key = AppTool.appBundleIdentifier + AppTool.appVersion + "ajJb2gUeLgV3ZacbKPFj"
        TLog("当前加密的 key--------\(key)----md5后----\(key.md5())")
        return key.md5()
    }

    static let shared = EnvManager()

    var isProduction: Bool = false

    // MARK: - Storage Keys
    private let environmentKey = "environmentKey"
    private let customAPIConfigKey = "customAPIConfig"
    private let customGameAPIConfigKey = "customGameAPIConfig"
    private let customH5ConfigKey = "customH5Config"
    private let apiHistoryKey = "apiConfigHistory"
    private let gameApiHistoryKey = "gameApiConfigHistory"
    private let h5HistoryKey = "h5ConfigHistory"

    override init() {
        super.init()
        configureEnvironment()
    }

    var apiURL = "http://api.tongzhuocp.com"
    var webApiURL = "http://webactvt.tongzhuocp.com"
    var gameApiURL = "http://webactvt.tongzhuocp.com/game"
    var ossEndPoint = "https://oss-cn-shenzhen.aliyuncs.com"
    var ossBucket = "houtaiproject"
    var environmentDescription = "正式服"
    var imAppid = "1400383941"

    var ossFullPath = "https://houtaiproject.oss-cn-shenzhen.aliyuncs.com/"
    var groupLiveID = "@TGS#aEIXXUTMG"
    
    func configureEnvironment() {
        switch currentEnvironment {
        case .test:
            apiURL = "http://dev.api.tongzhuocp.com"
            environmentDescription = "测试服"
            imAppid = "1400439096"
            groupLiveID = "@TGS#aKFJWUTMZ"
            webApiURL = "http://***********:8080"
            gameApiURL = "http://***********:5173"
        case .dis:
            break
        case .custom:
            loadCustomEnvironment()
        }
    }

    // MARK: - Custom Environment Management
    private func loadCustomEnvironment() {
        // 先设置为测试服的默认值，保证其他配置不为空
        imAppid = "1400439096"
        groupLiveID = "@TGS#aKFJWUTMZ"
        environmentDescription = "自定义配置"

        // 加载自定义API配置
        if let data = UserDefaults.standard.data(forKey: customAPIConfigKey),
           let config = try? JSONDecoder().decode(APIConfig.self, from: data) {
            apiURL = config.fullURL
            environmentDescription = config.description
        }

        // 加载自定义游戏API配置
        if let data = UserDefaults.standard.data(forKey: customGameAPIConfigKey),
           let config = try? JSONDecoder().decode(GameAPIConfig.self, from: data) {
            gameApiURL = config.fullURL
        }

        // 加载自定义H5配置
        if let data = UserDefaults.standard.data(forKey: customH5ConfigKey),
           let config = try? JSONDecoder().decode(H5Config.self, from: data) {
            webApiURL = config.fullURL
        }
    }

    func saveCustomAPIConfig(_ config: APIConfig) {
        guard let data = try? JSONEncoder().encode(config) else { return }
        UserDefaults.standard.set(data, forKey: customAPIConfigKey)

        // 保存到历史记录
        saveAPIToHistory(config: config)

        // 应用配置
        apiURL = config.fullURL
        environmentDescription = config.description

        UserDefaults.standard.synchronize()
    }

    func saveCustomGameAPIConfig(_ config: GameAPIConfig) {
        guard let data = try? JSONEncoder().encode(config) else { return }
        UserDefaults.standard.set(data, forKey: customGameAPIConfigKey)

        // 保存到历史记录
        saveGameAPIToHistory(config: config)

        // 应用配置（实时生效）
        gameApiURL = config.fullURL

        UserDefaults.standard.synchronize()
    }

    func saveCustomH5Config(_ config: H5Config) {
        guard let data = try? JSONEncoder().encode(config) else { return }
        UserDefaults.standard.set(data, forKey: customH5ConfigKey)

        // 保存到历史记录
        saveH5ToHistory(config: config)

        // 应用配置（实时生效）
        webApiURL = config.fullURL

        UserDefaults.standard.synchronize()
    }
    
    // MARK: - History Management
    private func saveAPIToHistory(config: APIConfig) {
        var history = getAPIHistory()

        // 避免重复添加相同配置
        history.removeAll { $0.host == config.host && $0.port == config.port }

        // 添加到开头
        history.insert(config, at: 0)

        // 限制历史记录数量
        if history.count > 10 {
            history = Array(history.prefix(10))
        }

        guard let data = try? JSONEncoder().encode(history) else { return }
        UserDefaults.standard.set(data, forKey: apiHistoryKey)
    }

    private func saveGameAPIToHistory(config: GameAPIConfig) {
        var history = getGameAPIHistory()

        // 避免重复添加相同配置
        history.removeAll { $0.host == config.host && $0.port == config.port }

        // 添加到开头
        history.insert(config, at: 0)

        // 限制历史记录数量
        if history.count > 10 {
            history = Array(history.prefix(10))
        }

        guard let data = try? JSONEncoder().encode(history) else { return }
        UserDefaults.standard.set(data, forKey: gameApiHistoryKey)
    }

    private func saveH5ToHistory(config: H5Config) {
        var history = getH5History()

        // 避免重复添加相同配置
        history.removeAll { $0.host == config.host && $0.port == config.port }

        // 添加到开头
        history.insert(config, at: 0)

        // 限制历史记录数量
        if history.count > 10 {
            history = Array(history.prefix(10))
        }

        guard let data = try? JSONEncoder().encode(history) else { return }
        UserDefaults.standard.set(data, forKey: h5HistoryKey)
    }

    func getAPIHistory() -> [APIConfig] {
        guard let data = UserDefaults.standard.data(forKey: apiHistoryKey),
              let configs = try? JSONDecoder().decode([APIConfig].self, from: data) else {
            return []
        }
        return configs
    }

    func getGameAPIHistory() -> [GameAPIConfig] {
        guard let data = UserDefaults.standard.data(forKey: gameApiHistoryKey),
              let configs = try? JSONDecoder().decode([GameAPIConfig].self, from: data) else {
            return []
        }
        return configs
    }

    func getH5History() -> [H5Config] {
        guard let data = UserDefaults.standard.data(forKey: h5HistoryKey),
              let configs = try? JSONDecoder().decode([H5Config].self, from: data) else {
            return []
        }
        return configs
    }

    func clearAPIHistory() {
        var history = getAPIHistory()
        // 保留最新的一条记录（第一个）
        if !history.isEmpty {
            history = [history[0]]
        }
        
        if history.isEmpty {
            UserDefaults.standard.removeObject(forKey: apiHistoryKey)
        } else {
            guard let data = try? JSONEncoder().encode(history) else { return }
            UserDefaults.standard.set(data, forKey: apiHistoryKey)
        }
        UserDefaults.standard.synchronize()
    }

    func clearGameAPIHistory() {
        var history = getGameAPIHistory()
        // 保留最新的一条记录（第一个）
        if !history.isEmpty {
            history = [history[0]]
        }
        
        if history.isEmpty {
            UserDefaults.standard.removeObject(forKey: gameApiHistoryKey)
        } else {
            guard let data = try? JSONEncoder().encode(history) else { return }
            UserDefaults.standard.set(data, forKey: gameApiHistoryKey)
        }
        UserDefaults.standard.synchronize()
    }

    func clearH5History() {
        var history = getH5History()
        // 保留最新的一条记录（第一个）
        if !history.isEmpty {
            history = [history[0]]
        }
        
        if history.isEmpty {
            UserDefaults.standard.removeObject(forKey: h5HistoryKey)
        } else {
            guard let data = try? JSONEncoder().encode(history) else { return }
            UserDefaults.standard.set(data, forKey: h5HistoryKey)
        }
        UserDefaults.standard.synchronize()
    }

    func saveEnvironment(_ environment: EnvType) {
        UserDefaults.standard.setValue(environment.rawValue, forKey: environmentKey)
        UserDefaults.standard.synchronize()
        exit(0)
    }

    func saveEnvironmentWithoutRestart(_ environment: EnvType) {
        UserDefaults.standard.setValue(environment.rawValue, forKey: environmentKey)
        UserDefaults.standard.synchronize()
    }

    var currentEnvironment: EnvType {
        if isProduction { return .dis }
        return EnvType(rawValue: UserDefaults.standard.integer(forKey: environmentKey)) ?? .test
    }
    
}


struct TwKey {
    static let appid = "default"
    static let organizationID = "QW8oEG8YtTmu4wjCBafR"
    static let publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCdfQ3vXpdxjY4BI3u0uqrF8nIgtuYHn+FrxoIPcAphIdWxBTktN5MSD5tDAUOfTR+iI5YKaORfBbrEUDkOf6bG0hV4rJlaU60dNwwb0SpWeQWh1/BJVPTPKPD3p94zOL5rm1aev3OARRjUpf0odFYyzjjFh46BfuMOXya8MTmO5QIDAQAB"
    static func fetchDeviceID() -> String {
        return SmAntiFraud.shareInstance().getDeviceId()
    }
}
