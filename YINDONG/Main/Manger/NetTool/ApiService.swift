//
//  ApiService.swift
//  YINDONG
//
//  Created by jj on 2025/2/24.
//

import UIKit
import Alamofire

enum ApiService {
    
    case appInfoBegain([String: Any])
    case getUserFunction
    
    case loginUser([String: Any])
    case commonOss
    case randomName
    case registerUser([String: Any])
    case sendCode([String: Any])
    case userNumLogin([String: Any])
    case ImSign
    case userInfo
    case loginOut
    case loginSetPas([String: Any])
    case loginPass([String: Any])
    case bindPhone([String: Any])
    case bindPhoneCode([String: Any])
    case codeSureLogin([String: Any])
    
    //装扮
    case userCountInfo
    case userPropInfo
    case userDrRingList([String: Any])
    case userHotGoodsList
    case userBuyEffets([String: Any])
    case userKnapsackList
    case userRingList
    case userDecorateList
    case userUninstallDress([String: Any])
    case userUpDress([String: Any])
    case userBuyRing([String: Any])
    case userGetPre([String: Any])
    
    ///动态
    case topicList([String: Any])
    case topicList2([String: Any])
    case bannerList([String: Any])
    case dynamicInfoPag([String: Any])
    case newDyListPage([String: Any], Int)
    case likeUserList([String: Any])
    case commentList([String: Any])
    case topicDetail([String: Any])
    case likeDy([String: Any])
    case userDynamicList([String: Any])
    case meDynamicList([String: Any])
    
    case recommendUserF
    case dynamicFollow(page: Int)
    case dynamicLoc(page: Int, code: String)
    case appVersion
    case commentReply([String: Any])
    case commentAdd([String: Any])
    case commentDel([String: Any])
    case commentReplayDel([String: Any])
    
    case inviteFriendsList([String: Any])
    case postDynamic([String: Any])
    case reportDynamic([String: Any])
    case dynamicNotice([String: Any])
    case dynamicDetail([String: Any])
    case getDynamicCount([String: Any])
    case batchFollow([String: Any])
    case dynamicDelted([String: Any])
    
    
    ///用户个人中心
    case userInterestSourceList([String: Any])
    case userInterestList
    case userFollow([String: Any])
    case userCancelFollow([String: Any])
    case updateUserInfo([String: Any])
    case updateInterest(body: Data)
    case updateInterestList(body: Data)
    case userInfoDetail([String: Any])
    case userDelInterest([String: Any])
    case userAddPhoto([String: Any])
    case userDelPhoto([String: Any])
    case userOnline([String: Any])
    case userHello([String: Any])
    case getRechargeInfo([String: Any])
    case userLevelInfo
    case rechargeListInfo
    case rechargeVerify([String: Any])
    case chatUserInfo([String: Any])
    case submitUserIdAuthentication([String: Any])
    case getUserIdAuthenticationRecord
    case userCheckcode([String: Any])
    case userChangePhone([String: Any])
    case addBlack([String: Any])
    case removeBlack([String: Any])
    case userBlackList
    case cheakUserDelete
    case removeConfim([String: Any])
    case loginUserAccount
    case changeUser([String: Any])
    case getUserAccountInfo([String: Any])
    case submitReport([String: Any])
    case clearPiPei
    case invterFollower([String: Any])
    
    ///聊天
    case mqanswerMsg([String: Any])
    case quesionList([String: Any])
    case chatQuestionsAnswer([String: Any])
    case myFriends([String: Any])
    case myFollows([String: Any])
    case myFans([String: Any])
    case hobbyList([String: Any])
    case addFriend([String: Any])
    case receivedGiftList([String: Any])
    case sytesmList([String: Any])
    case giftThanks([String: Any])
    case userStaus([String: Any])
    case searchFriend([String: Any])
    case feedback([String: Any])
    case giftList
    case userKnapsack
    case userDaoJu
    case sendGift([String: Any])
    case sendMBox([String: Any])
    case updateIntimacy([String: Any])
    case removeFriend([String: Any])
    
    ///房间
    case hotListRoom(Int)
    case liveFollowList(Int)
    case liveFriendsList(Int)
    case liveMusicList(Int)
    case liveGameList(Int)
    case getTRTCSign
    case liveOut([String: Any])
    case voiceUserInfo([String: Any])
    case getRoomType([String: Any])
    case joinRoom([String: Any])
    case roomRank([String: Any])
    case roomInfo([String: Any])
    case roomTypeInfo
    case cheakRealName
    case openRoom
    case closeRoom
    case anchorHeartbeat([String: Any])
    case roomDurationTime([String: Any])
    case roomRecord([String: Any])
    case roomUserInfo([String: Any])
    case roomUserList([String: Any])
    case activityList([String: Any])
    case kickRoom([String: Any])
    case kickMangerRoom([String: Any])
    case roomEditInfo([String: Any])
    case roomCancelAdmin([String: Any])
    case roomAddAdmin([String: Any])
    case roomConfig
    case speekNumUp([String: Any])
    case roomBgList([String: Any])
    case roomSetBg([String: Any])
    case roomOpenCount([String: Any])
    case roomGiftsCount([String: Any])
    case roomLikeSong
    case roomSongList
    case roomCheck([String: Any])
    case roomFollow([String: Any])
    case roomCallFans([String: Any])
    case roomIVUserList
    case roomLock([String: Any])
    case roomGiftList
    case roomUserpropKnap
    case roomSendGift([String: Any])
    case roomMeGiftCount([String: Any])
    case changeRoomInfo([String: Any])
    case roomUploadSeats([String: Any])
    case roomTenUpPeiB([String: Any])
    case roomCompanyTask
    case roomAccompany([String: Any])
    case roomDianLiang([String: Any])
    case roomTaskAdd
    case roomSongLike([String: Any])
    case roomSongDel([String: Any])
    case roomFzTask
    case roomYkTask
    case roomFzLinq([String: Any])
    case roomYkLinq([String: Any])
    case roomAdvert([String: Any])
    case roomRedBagList([String: Any])
    case roomLiveRed([String: Any])
    case roomRedEnvelopesUser([String: Any])
    case roomSkillInfo([String: Any])
    case roomSkillText([String: Any])
    
    ///boss
    case bossRotationNew
    case bossBaseInfo
    case bossUpdate
    case bossMyBlind
    case bossMyluck
    case buyList
    case bossBuy([String: Any])
    case bossTackle
    case bossAdvancedAttacks
    case jiangpingList
    case daJiangList
    case bossChangeGoods
    case bossChangeUp([String: Any])
    case bossOpenBox
    case pointsBossInfo
    
    //屠龙
    case tuLongInfo
    case tuLongAccountInfo
    case tuLongDInfo
    case tuLongEnvelopes([String: Any])
    case tLongGrabRedPacket([String: Any])
    case tLongRecord([String: Any])
    case tLongUserRank
    case tLongRoomRank([String: Any])
    case tLongLunbo
    /// 技能
    case userSkillInfo([String: Any])
    case upgradeSkill([String: Any])
    case skillDaojuList([String: Any])
    case skillPay([String: Any])
    case skillGoodsList([String: Any])
    case skillInShop([String: Any])
    case skillRate([String: Any])
    case skillToUser([String: Any])
    case skillMansionsInfo
    case skillMansionsQuer([String: Any])
    case skillMansionsStart([String: Any])
    case skillMansionsTerminate([String: Any])
    case startMansionsUser([String: Any])
    case endMansionsUser([String: Any])
    case redemptionMansions
    case relieveUserMansions([String: Any])
    case hireMansionsSystemWorker
    case hireMansionsHistory([String: Any])
    
    ///配置文件
    case common_config
    case userDisguise
    case userTitleList
    case userNob
    case getNetTime
}

extension ApiService: TargetType {
    
    var baseURL: URL {
        switch self {
        case .bannerList:
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=\("1")&PageSize=\("20")")!
        case .dynamicInfoPag:
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=\("1")")!
        case .newDyListPage(_, let id):
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=1&footerId=\(id)")!
        case .dynamicFollow(let page):
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=\(page)")!
        case .dynamicLoc(let page, _):
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=\(page)&footerId=0")!
        case .hotListRoom(let page):
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=\(page)")!
        case .liveFollowList(let page):
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=\(page)")!
        case .liveFriendsList(let page):
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=\(page)")!
        case .liveMusicList(let page):
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=\(page)")!
        case .liveGameList(let page):
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=\(page)")!
        case .roomAdvert:
            return URL(string: EnvManager.shared.apiURL + "\(self.urlPath)?page=\("1")&PageSize=\("20")")!
        case .common_config:
            return URL(string: EnvManager.shared.ossFullPath)!
        default:
            return URL(string: EnvManager.shared.apiURL + "/")!
        }
    }
    
    ///url 携带参数的
    var urlPath: String {
        switch self {
        case .bannerList:
            return "/banner/bannerPagination.do"
        case .dynamicInfoPag:
            return  "/community/queryCommunityDynamicInfoPaginationRecommend299.do"
        case .newDyListPage:
            return "/community/queryCommunityDynamicInfoRepeat299.do"
        case .dynamicFollow:
            return "/community/queryCommunityDynamicInfoPaginationFollow.do"
        case .dynamicLoc:
            return "/community/queryCommunityDynamicInfoPaginationNearby.do"
            
        case .hotListRoom:
            return "/voice/voiceLiveHotList.do"
        case .liveFollowList:
            return "/community/queryFollowHomepageList.do"
        case .liveFriendsList:
            return "/voice/voiceLiveMakeFriendsList.do"
        case .liveMusicList:
            return "/voice/voiceLiveMusicList.do"
        case .liveGameList:
            return "/voice/voiceLiveGameList.do"
        case .roomAdvert:
            return "/banner/bannerPagination.do"
        default:
            return ""
        }
    }
    
    var path: String {
        switch self {
        case .loginUser:
            return "authenticationLogin/doLogin.do"
        case .commonOss:
            return "common/ossumeRole.do"
        case .randomName:
            return "common/querySysNameBasicDataName.do"
        case .registerUser:
            return "authenticationLogin/doRegister.do"
        case .sendCode:
            return "common/sendCode.do"
        case .userNumLogin:
            return "authenticationLogin/authenticationUserNumberLogin.do"
        case .userInfo:
            return "login/getUserInfo.do"
        case .ImSign:
            return "penpal/genSig.do"
        case .loginOut:
            return "login/logout.do"
        case .loginSetPas:
            return "login/setUpPw.do"
        case .loginPass:
            return "authenticationLogin/doLoginPw.do"
        case .bindPhone:
            return "authenticationLogin/unboundUser.do"
        case .bindPhoneCode:
            return "personal/unboundUser.do"
        case .topicList:
            return "community/getCommunityTopicListHot.do"
        case .topicList2:
            return "community/getCommunityTopicListPagination.do"
        case .bannerList,
                .dynamicInfoPag,
                .newDyListPage,
                .dynamicFollow,
                .dynamicLoc,
                .hotListRoom,
                .liveFollowList,
                .liveFriendsList,
                .liveMusicList,
                .liveGameList,
                .roomAdvert:
            return ""
        case .likeUserList:
            return "community/getByThumbsUpUserPagination.do"
        case .commentList:
            return "community/querCommunityCommentByDynamicPagination.do"
        case .topicDetail:
            return "community/getCommunityTopic.do"
        case .recommendUserF:
            return "community/recommendUserFollow.do"
        case .appVersion:
            return "common/getChannelBusiness.do"
        case .commentReply:
            return "community/addCommunityCommentReply.do"
        case .inviteFriendsList:
            return "active/inviteFriendsList.do"
        case .postDynamic:
            return "community/releaseDynamicInfo.do"
        case .likeDy:
            return "community/addThumbsUp.do"
        case .commentAdd:
            return "community/addCommunityComment.do"
        case .commentDel:
            return "community/deleteCommunityComment.do"
        case .reportDynamic:
            return "personal/saveSysReport.do"
        case .commentReplayDel:
            return "community/deleteCommunityCommentReply.do"
        case .dynamicNotice:
            return "community/queryCommunityUserMsgPagination.do"
        case .dynamicDetail:
            return "community/getDynamicInfo.do"
        case .userInterestSourceList:
            return "personal/userInterestSourceList.do"
        case .userFollow:
            return "community/followOperation.do"
        case .userCancelFollow:
            return "community/cancelFollowOperation.do"
        case .updateUserInfo:
            return "personal/updatUserInfo.do"
        case .updateInterest:
            return "personal/saveInterestSourceList.do"
        case .updateInterestList:
            return "personal/saveInterestList.do"
        case .userInterestList:
            return "personal/userInterestList.do"
        case .userInfoDetail:
            return "penpal/getFriendsUserDetails.do"
        case .userDelInterest:
            return "personal/deleteInterestSourceList.do"
        case .userAddPhoto:
            return "personal/saveUserAlbum.do"
        case .userDelPhoto:
            return "personal/delUserAlbum.do"
        case .userOnline:
            return "penpal/queryImState.do"
        case .getDynamicCount:
            return "community/queryCommunityDynamicInfoPaginationCount.do"
        case .userHello:
            return "socialContact/userOperationHobby.do"
        case .appInfoBegain:
            return "common/saveappUserEquipment.do"
        case .getRechargeInfo:
            return "power/getUserPowerAccountInfo.do"
        case .userLevelInfo:
            return "power/getPersonLevelInfo.do"
        case .rechargeListInfo:
            return "goods/queryGoodsInfoPoListByIos.do"
        case .rechargeVerify:
            return "pay/iosNotify.do"
        case .chatUserInfo:
            return "penpal/getFriendsRelation.do"
        case .mqanswerMsg:
            return "socialContact/answerMsgPrivateChatQuestionsAnswersResult.do"
        case .quesionList:
            return "common/questionBankList.do"
        case .myFriends:
            return "penpal/queryUserRequestListPagination.do"
        case .myFollows:
            return "community/queryFollowListPagination.do"
        case .myFans:
            return "community/queryFansListPagination.do"
        case .hobbyList:
            return "socialContact/getUserHobbyPagination.do"
        case .addFriend:
            return "penpal/addFriendSerive.do"
        case .receivedGiftList:
            return "socialContact/queryUserGiftMsgPagination.do"
        case .sytesmList:
            return "personal/messagesList.do"
        case .getUserFunction:
            return "personal/getUserFunctionSwitch.do"
        case .giftThanks:
            return "penpal/answerThanksGiftMsg.do"
        case .userStaus:
            return "personal/saveUserFunctionSwitch.do"
        case .searchFriend:
            return "penpal/searchFriends.do"
        case .feedback:
            return "personal/saveSysReport.do"
        case .submitUserIdAuthentication:
            return "personal/submitUserIdAuthentication.do"
        case .getUserIdAuthenticationRecord:
            return "personal/getUserIdAuthenticationRecord.do"
        case .userCheckcode:
            return "common/sendUserCheckcode.do"
        case .userChangePhone:
            return "personal/unboundUser.do"
        case .addBlack:
            return "mainScene/addUserToBlackList.do"
        case .userBlackList:
            return "mainScene/queryUserBlackList.do"
        case .removeBlack:
            return "mainScene/delUserFromBlackList.do"
        case .cheakUserDelete:
            return "personalBusiness/querLogoffConditionInfo.do"
        case .removeConfim:
            return "personalBusiness/saveUserLogoffRecord.do"
        case .loginUserAccount:
            return "authenticationLogin/replaceAssociatedAccountList.do"
        case .changeUser:
            return "authenticationLogin/replaceUserNumberLogin.do"
        case .batchFollow:
            return "community/batchFollowOperation.do"
        case .giftList:
            return "goods/goodsGiftInfoSingleChatList.do"
        case .userKnapsack:
            return "goods/queryUserKnapsackGoodsGiftList.do"
        case .userDaoJu:
            return "goods/userpropKnapsackList.do"
        case .sendGift:
            return "account/purchaseGiveGift.do"
        case .sendMBox:
            return "account/purchaseBlindbox.do"
        case .getUserAccountInfo:
            return "account/getaccountInfo.do"
        case .chatQuestionsAnswer:
            return "socialContact/sendMsgPrivateChatQuestionsAnswers.do"
        case .codeSureLogin:
            return "authenticationLogin/replaceMsgCodeLogin.do"
        case .submitReport:
            return "personal/saveSysReport.do"
        case .userDynamicList:
            return "community/queryPersonalDynamicInfoList299.do"
        case .meDynamicList:
            return "community/queryUserDynamicInfoList299.do"
        case .updateIntimacy:
            return "socialContact/sendLetter.do"
        case .clearPiPei:
            return "socialContact/delUserHobbybydel.do"
        case .invterFollower:
            return "community/inviteFollowOperationMsg.do"
        case .dynamicDelted:
            return "community/deleteDynamicInfo.do"
        case .getTRTCSign:
            return "penpal/genSigNew.do"
        case .liveOut:
            return "voice/outVoiceLive.do"
        case .voiceUserInfo:
            return "voice/portraitGetService.do"
        case .getRoomType:
            return "voice/querVoiceLiveRoomRoomTypeByLiveRoomNo.do"
        case .joinRoom:
            return "voice/intoVoiceLive.do"
        case .roomRank:
            return "voice/queryVoiceLiveWealthRanking.do"
        case .roomInfo:
            return "voice/querVoiceLiveRoomByRoomNo.do"
        case .roomTypeInfo:
            return "voice/queryLiveThemeLabelList.do"
        case .cheakRealName:
            return "voice/getInspectUserIdAuthenticationNow.do"
        case .openRoom:
            return "voice/beginVoiceLiveHome.do"
        case .anchorHeartbeat:
            return "voice/voiceLiveHeartbeatMonitorNews.do"
        case .roomDurationTime:
            return "voice/roomDurationTime.do"
        case .roomRecord:
            return "voice/saveUserRoomDetailRecord.do"
        case .roomUserInfo:
            return "voice/getUserDetailsVoiceLive.do"
        case .common_config:
            return "social/system/common_config.json"
        case .roomUserList:
            return "voice/queryVoiceLiveRoomMemberList.do"
        case .closeRoom:
            return "voice/closeVoiceLiveRoom.do"
        case .activityList:
            return "banner/querySysActivityInfoList.do"
        case .removeFriend:
            return "penpal/deleteFriendSerive.do"
        case .bossRotationNew:
            return "powerBossActivity/getAllUserBossRecordRotationNew.do"
        case .bossBaseInfo:
            return "powerBossActivity/getbossConfigureInfoNews.do"
        case .buyList:
            return "powerBossActivity/getGoodsMoFaShuList.do"
        case .bossBuy:
            return "bossActivity/purchaseGiveGift.do"
        case .bossUpdate:
            return "powerBossActivity/getBossSurprisedValue.do"
        case .bossMyBlind:
            return "powerBossActivity/getUserBossBlindBoxProgress.do"
        case .bossMyluck:
            return "powerBossActivity/getUserLargePrizeGiftBusiness.do"
        case .bossTackle:
            return "powerBossActivity/initiateBossActivityNews.do"
        case .bossAdvancedAttacks:
            return "powerBossActivity/initiateBossActivitySenior.do"
        case .jiangpingList:
            return "powerBossActivity/queryBossGiftInfobyAllList.do"
        case .daJiangList:
            return "powerBossActivity/getBossPowerUserRecordDetailsListNew.do"
        case .bossChangeGoods:
            return "goods/querGoodsFragmentList.do"
        case .bossChangeUp:
            return "account/purchaseFragmentGoodsPropIn.do"
        case .bossOpenBox:
            return "powerBossActivity/getALLBossBlindBoxProgress.do"
        case .userSkillInfo:
            return "power/getUserSkillInfo.do"
        case .upgradeSkill:
            return "power/upgradeSkills.do"
        case .skillDaojuList:
            return "goods/querGoodsGameListByPayTypeType.do"
        case .skillPay:
            return "account/purchaseGameGoodsPropInService.do"
        case .skillGoodsList:
            return "goods/querGoodsGameList.do"
        case .kickRoom:
            return "voice/voiceLiveRoomKick.do"
        case .kickMangerRoom:
            return "voice/voiceLiveRoomAdminKick.do"
        case .roomEditInfo:
            return "voice/editVoiceLiveRoomInfo.do"
        case .roomCancelAdmin:
            return "voice/cancelVoiceLiveAdmin.do"
        case .roomAddAdmin:
            return "voice/setVoiceLiveAdmin.do"
        case .roomConfig:
            return "common/getBusinessConfig.do"
        case .speekNumUp:
            return "voiceAnchor/voiceLiveMemberSpeechemploy.do"
        case .roomBgList:
            return "common/querySysDictionariesList.do"
        case .roomSetBg:
            return "voice/setRoomBackground.do"
        case .roomOpenCount:
            return "voice/setVoiceLiveRoomInfoIsCounter.do"
        case .roomGiftsCount:
            return "voice/queryLiveRoomUserReceiveGiftsCountBatch.do"
        case .roomSongList:
            return "voice/queryVoiceLiveMusicList.do"
        case .roomLikeSong:
            return "voice/queryVoiceLiveAdminListUser.do"
        case .roomCheck:
            return "penpal/getUserHeelRoomLiveRoomNo.do"
        case .roomFollow:
            return "community/liveRoomfollowOperation.do"
        case .roomCallFans:
            return "voice/callFans.do"
        case .roomIVUserList:
            return "penpal/queryTemporaryUserRequestList.do"
        case .roomLock:
            return "voice/setVoiceLiveRoomInfoPassword.do"
        case .userDisguise:
            return "memberSystem/getUserMemberInfo.do"
        case .userTitleList:
            return "personal/queryUserTitleList.do"
        case .userNob:
            return "nobleConsumeRecharge/getUserNobleInfo.do"
        case .roomGiftList:
            return "goods/queryVoiceLiveGoodsGiftInfoList.do"
        case .roomUserpropKnap:
            return "goods/userpropKnapsackList.do"
        case .roomSendGift:
            return "account/purchaseGiveGiftBatch.do"
        case .pointsBossInfo:
            return "powerBossActivity/getUserFightingPointsBossInfo.do"
        case .roomMeGiftCount:
            return "voice/queryLiveRoomUserReceiveGiftsCount.do"
        case .changeRoomInfo:
            return "voice/changeVoiceLiveRoomType.do"
        case .roomUploadSeats:
            return "voice/saveSynchronizationVoiceLiveWheatNowPosition.do"
        case .roomTenUpPeiB:
            return "voice/accompanyLiveRoomOnWheat.do"
        case .roomCompanyTask:
            return "voice/querAccompanyRecordTask.do"
        case .getNetTime:
            return "common/getDate.do"
        case .roomAccompany:
            return "voice/saveUserVoiceLiveAccompanyRecord.do"
        case .roomDianLiang:
            return "voice/saveUserVoiceLiveAccompanyRecord.do"
        case .roomTaskAdd:
            return "voice/receiveAccompanyRecordTask.do"
        case .roomSongLike:
            return "voice/addvoiceLiveMusicUser.do"
        case .roomSongDel:
            return "voice/delVoiceLiveMusicUser.do"
        case .tuLongInfo:
            return "bossActivity/querDragonGameInfoSimplified.do"
        case .roomFzTask:
            return "dragonActive/getLiveRoomAnchorTask.do"
        case .roomYkTask:
            return "dragonActive/getLiveRoomVisitorTask.do"
        case .tuLongAccountInfo:
            return "bossActivity/queryDragonGameAccountInfo.do"
        case .tuLongDInfo:
            return "bossActivity/querDragonGameInfo.do"
        case .tuLongEnvelopes:
            return "bossActivity/getOptimumRedEnvelopesUser.do"
        case .tLongGrabRedPacket:
            return "bossActivity/receiveVoiceLiveRedEnvelopes.do"
        case .tLongRecord:
            return "bossActivity/queryUserKillDragonRecordByList.do"
        case .tLongUserRank:
            return "bossActivity/queryUserSurprisedRecordListLimit.do"
        case .tLongRoomRank:
            return "bossActivity/queryUserKillDragonRecordLiveRoomHourList.do"
        case .tLongLunbo:
            return "bossActivity/queryUserKillDragonRecordCarouselList.do"
        case .roomFzLinq:
            return "dragonActive/receiveLiveRoomAnchorTaskPriceService.do"
        case .roomYkLinq:
            return "dragonActive/receiveLiveRoomVisitorTaskPriceService.do"
        case .roomRedBagList:
            return "voice/getRedEnvelopesRain.do"
        case .roomLiveRed:
            return "bossActivity/receiveVoiceLiveRedEnvelopes.do"
        case .roomRedEnvelopesUser:
            return "bossActivity/getOptimumRedEnvelopesUser.do"
        case .skillInShop:
            return "power/getSkillsInShop.do"
        case .skillRate:
            return "power/getSkillSuccessRate.do"
        case .skillToUser:
            return "power/useSkillsByUser.do"
        case .roomSkillInfo:
            return "power/checkSkillUseStatus.do"
        case .roomSkillText:
            return "power/getHuyanluanyu.do"
        case .skillMansionsInfo:
            return "power/mansionsInfoNew.do"
        case .skillMansionsQuer:
            return "power/querTaskInDetails.do"
        case .skillMansionsStart:
            return "power/startTask.do"
        case .skillMansionsTerminate:
            return "power/terminateTask.do"
        case .startMansionsUser:
            return "power/dispatchMansions.do"
        case .endMansionsUser:
            return "power/collectMansions.do"
        case .redemptionMansions:
            return "power/redemptionMansions.do"
        case .relieveUserMansions:
            return "power/relievePowerUserMansionsAttendant.do"
        case .hireMansionsSystemWorker:
            return "power/robotHire.do"
        case .hireMansionsHistory:
            return "power/queryUserAttendantTaskRecordList.do"
        case .userCountInfo:
            return "personal/userInfoCollection.do"
        case .userPropInfo:
            return "goods/queryGoodsPropInfoPoList.do"
        case .userDrRingList:
            return "goods/goodsGiftInfoList.do"
        case .userHotGoodsList:
            return "goods/popularGoodsList.do"
        case .userBuyEffets:
            return "account/purchaseGoodsPropIn.do"
        case .userKnapsackList:
            return "goods/userMyPropKnapsackListService.do"
        case .userRingList:
            return "goods/queryUserRingGiftList.do"
        case .userDecorateList:
            return "goods/queryUserDecorateList.do"
        case .userUninstallDress:
            return "goods/userDecorateUseRemove.do"
        case .userUpDress:
            return "goods/userDecorateUseWear.do"
        case .userBuyRing:
            return "account/purchaseRingGift.do"
        case .userGetPre:
            return "penpal/queryUserDecorateByGoodsTypeByImNumber.do"
        }
    }
    
    var method: Moya.Method {
        switch self {
        case .common_config:
            return .get
        default:
            return .post
        }
    }
    
    var task: Moya.Task {
        switch self {
        case .sendCode(let pp),
                .topicList(let pp),
                .likeUserList(let pp),
                .commentList(let pp),
                .topicDetail(let pp),
                .topicList2(let pp),
                .inviteFriendsList(let pp),
                .likeDy(let pp),
                .commentDel(let pp),
                .commentReplayDel(let pp),
                .dynamicNotice(let pp),
                .dynamicDetail(let pp),
                .userInterestSourceList(let pp),
                .userFollow(let pp),
                .userCancelFollow(let pp),
                .userInfoDetail(let pp),
                .userDelInterest(let pp),
                .userOnline(let pp),
                .getDynamicCount(let pp),
                .getRechargeInfo(let pp),
                .chatUserInfo(let pp),
                .mqanswerMsg(let pp),
                .quesionList(let pp),
                .myFriends(let pp),
                .myFans(let pp),
                .myFollows(let pp),
                .hobbyList(let pp),
                .addFriend(let pp),
                .receivedGiftList(let pp),
                .sytesmList(let pp),
                .giftThanks(let pp),
                .userStaus(let pp),
                .searchFriend(let pp),
                .submitUserIdAuthentication(let pp),
                .userCheckcode(let pp),
                .addBlack(let pp),
                .removeBlack(let pp),
                .removeConfim(let pp),
                .batchFollow(let pp),
                .getUserAccountInfo(let pp),
                .chatQuestionsAnswer(let pp),
                .userDynamicList(let pp),
                .meDynamicList(let pp),
                .invterFollower(let pp),
                .dynamicDelted(let pp),
                .liveOut(let pp),
                .voiceUserInfo(let pp),
                .getRoomType(let pp),
                .joinRoom(let pp),
                .roomRank(let pp),
                .roomInfo(let pp),
                .anchorHeartbeat(let pp),
                .roomDurationTime(let pp),
                .roomUserInfo(let pp),
                .roomUserList(let pp),
                .activityList(let pp),
                .removeFriend(let pp),
                .bossChangeUp(let pp),
                .userSkillInfo(let pp),
                .skillDaojuList(let pp),
                .skillPay(let pp),
                .skillGoodsList(let pp),
                .kickRoom(let pp),
                .kickMangerRoom(let pp),
                .roomCancelAdmin(let pp),
                .roomAddAdmin(let pp),
                .speekNumUp(let pp),
                .roomBgList(let pp),
                .roomSetBg(let pp),
                .roomGiftsCount(let pp),
                .roomCheck(let pp),
                .roomFollow(let pp),
                .roomCallFans(let pp),
                .roomMeGiftCount(let pp),
                .roomTenUpPeiB(let pp),
                .roomDianLiang(let pp),
                .roomSongLike(let pp),
                .roomSongDel(let pp),
                .tuLongEnvelopes(let pp),
                .tLongGrabRedPacket(let pp),
                .tLongRecord(let pp),
                .tLongRoomRank(let pp),
                .roomFzLinq(let pp),
                .roomYkLinq(let pp),
                .roomRedBagList(let pp),
                .roomLiveRed(let pp),
                .roomRedEnvelopesUser(let pp),
                .skillInShop(let pp),
                .skillRate(let pp),
                .skillToUser(let pp),
                .roomSkillInfo(let pp),
                .roomSkillText(let pp),
                .skillMansionsQuer(let pp),
                .skillMansionsStart(let pp),
                .skillMansionsTerminate(let pp),
                .startMansionsUser(let pp),
                .endMansionsUser(let pp),
                .relieveUserMansions(let pp),
                .hireMansionsHistory(let pp),
                .userDrRingList(let pp),
                .userUninstallDress(let pp),
                .userUpDress(let pp),
                .userGetPre(let pp):
            return .requestParameters(parameters: pp, encoding: URLEncoding.queryString)
        case .loginUser(let pp),
                .registerUser(let pp),
                .userNumLogin(let pp),
                .loginSetPas(let pp),
                .loginPass(let pp),
                .bindPhone(let pp),
                .bindPhoneCode(let pp),
                .bannerList(let pp),
                .newDyListPage(let pp, _),
                .commentReply(let pp),
                .postDynamic(let pp),
                .commentAdd(let pp),
                .reportDynamic(let pp),
                .updateUserInfo(let pp),
                .userAddPhoto(let pp),
                .userDelPhoto(let pp),
                .userHello(let pp),
                .appInfoBegain(let pp),
                .rechargeVerify(let pp),
                .feedback(let pp),
                .userChangePhone(let pp),
                .changeUser(let pp),
                .sendGift(let pp),
                .sendMBox(let pp),
                .codeSureLogin(let pp),
                .updateIntimacy(let pp),
                .roomRecord(let pp),
                .bossBuy(let pp),
                .upgradeSkill(let pp),
                .roomEditInfo(let pp),
                .roomOpenCount(let pp),
                .roomLock(let pp),
                .roomSendGift(let pp),
                .changeRoomInfo(let pp),
                .roomUploadSeats(let pp),
                .roomAdvert(let pp),
                .userBuyEffets(let pp),
                .userBuyRing(let pp):
            return .requestParameters(parameters: pp, encoding: JSONEncoding.default)
        
        case .updateInterest(let data),
                .updateInterestList(let data):
            return .requestParameters(parameters: ["jsonArray": data], encoding: JSONArrayEncoding.default)
            
        case .dynamicLoc(_, let code):
            return .requestParameters(parameters: ["provinceCode": code], encoding: JSONEncoding.default)
        case .common_config:
            return .requestPlain
        default:
            return .requestParameters(parameters: [:], encoding: JSONEncoding.default)
        }
    }
    
    var headers: [String: String]? {
        switch self {
        case .common_config:
            return ["Content-Type": "application/json"]
        default:
            var headers: [String: String] = [:]
            headers["version"] = AppTool.appVersion
            headers["versionCode"] = AppTool.appBuildVersion
            headers["channel"] = AppTool.appChannel
            headers["equipment"] = TwKey.fetchDeviceID()
            headers["packName"] = AppTool.appBundleIdentifier
            headers["sourceScene"] = "0"
            headers["device"] = ""
            
            if let token = LWUserManger.getToken() {
                headers["token"] = token
            }
            
            if let token = QuickManger.shared.tempToken {
                headers["token"] = token
            }
            return headers
        }
    }
    
    var isShowLoading: Bool {
        switch self {
        case .loginUser,
                .registerUser,
                .sendCode,
                .updateInterestList,
                .userInterestSourceList,
                .userHello,
                .rechargeVerify,
                .mqanswerMsg,
                .giftThanks,
                .changeUser,
                .submitUserIdAuthentication,
                .addFriend,
                .clearPiPei,
                .bossBuy,
                .bossChangeUp,
                .roomOpenCount,
                .roomCallFans,
                .roomLock,
                .roomTaskAdd,
                .roomFzLinq,
                .roomYkLinq,
                .roomSkillText,
                .skillMansionsQuer,
                .skillMansionsStart,
                .skillMansionsTerminate,
                .userBuyEffets,
                .userUninstallDress,
                .userUpDress:
            return true
        default:
            return false
        }
    }
    
}


struct JSONArrayEncoding: ParameterEncoding {
    static let `default` = JSONArrayEncoding()

    func encode(_ urlRequest: URLRequestConvertible, with parameters: Parameters?) throws -> URLRequest {
        var request = try urlRequest.asURLRequest()

        guard let data = parameters?["jsonArray"] as? Data else {
            return request
        }

        if request.value(forHTTPHeaderField: "Content-Type") == nil {
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        }

        request.httpBody = data

        return request
    }
}
