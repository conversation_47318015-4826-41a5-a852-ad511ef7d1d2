//
//  T_IMHelper.swift
//  YINDONG
//
//  Created by jj on 2025/2/27.
//

import UIKit
import ImSDK_Plus_Swift
import LSTPopView

enum SystemNotification: Int {
    case system = 0
    case meet
    case harmonious
    case gift
    case maskLevel
    case liveMuteGM
    case community
    case cpOfficial
    case confession
    case sysNotice
    case nobleNotice
    case giftBookCompleteNotice
    case hudongNotice
    case gameNotice
    
    /// 映射表：枚举 -> key
    private static let keyMapping: [SystemNotification: String] = [
        .system: "sySsystem",
        .meet: "sysRequest",
        .harmonious: "sysharmonious",
        .gift: "sysRequestGift",
        .maskLevel: "masqueradeLevel",
        .liveMuteGM: "sysInvitationCodeNotice",
        .community: "dynamicMsg",
        .cpOfficial: "sysCphelper",
        .confession: "sysDistributionNotice",
        .sysNotice: "sysUserNotice",
        .nobleNotice: "sysNobleNotice",
        .giftBookCompleteNotice: "sysAtlasNotice",
        .hudongNotice: "sysInterActivec",
        .gameNotice: "sysGameNotice"
    ]
    
    
    
    /// 获取 key（字符串值）
    var key: String {
        return SystemNotification.keyMapping[self] ?? ""
    }
    
    /// 通过 key 获取枚举值
    static func fromKey(_ key: String) -> SystemNotification? {
        return keyMapping.first { $0.value == key }?.key
    }
    
    static func isValidNotificationKey(_ key: String) -> Bool {
        return SystemNotification.keyMapping.values.contains(key)
    }
    
    func isAdd(type: SystemNotification) -> Bool {
        switch self {
        case .system, .meet, .gift, .cpOfficial, .hudongNotice:
            return true
        default:
            return false
        }
    }
    
    /// 获取展示标题
       var title: String {
           switch self {
           case .system:
               return "系统通知"
           case .meet:
               return "邂逅消息"
           case .harmonious:
               return "和谐信息"
           case .gift:
               return "礼物消息"
           case .maskLevel:
               return "酒馆升级消息"
           case .liveMuteGM:
               return "邀请码消息"
           case .community:
               return "社区消息"
           case .cpOfficial:
               return "CP小助手"
           case .confession:
               return "组CP消息"
           case .sysNotice:
               return "系统吐司"
           case .nobleNotice:
               return "贵族通知"
           case .hudongNotice:
               return "互动消息"
           
           case .giftBookCompleteNotice:
               return ""
           case .gameNotice:
               return ""
           }
       }
       
       /// 获取图标名称，可用于 UIImage(named:)
       var iconName: String {
           switch self {
           case .system:
               return "icon_syteam"
           case .meet:
               return "icon_pipei"
           case .harmonious:
               return "icon_harmonious"
           case .gift:
               return "icon_gifItem"
           case .maskLevel:
               return "icon_masquerade"
           case .liveMuteGM:
               return "icon_invitation"
           case .community:
               return "icon_community"
           case .cpOfficial:
               return "icon_cp"
           case .confession:
               return "icon_confession"
           case .sysNotice:
               return "icon_system_notice"
           case .nobleNotice:
               return "icon_noble"
           case .hudongNotice:
               return "icon_meetChat"
           case .giftBookCompleteNotice:
               return ""
           case .gameNotice:
               return ""
           }
       }
}


class T_IMHelper {
    
    static let shared = T_IMHelper()
    
    var isFish: Bool = false
    
    var toUserPre: DressBaseModel?

    
    // 使用 NSHashTable 来存储弱引用的 delegates
    private lazy var weakDelegates = NSHashTable<AnyObject>.weakObjects()
    
    // 为了兼容现有代码，保留 delegates 计算属性，但返回弱引用数组中的对象
    var delegates: [MessageHandlerDelegate] {
        return weakDelegates.allObjects.compactMap { $0 as? MessageHandlerDelegate }
    }
    
    var totalCount: UInt = 0
    var noticeCount: Int = 0
    
    var directMessageCount: Int = 0
    var chatSessionCount: Int = 0
    
    var userSign: String?

    func initializeChatSDK() {
        
        let sdkConfig = V2TIMSDKConfig()
        sdkConfig.logLevel = .V2TIM_LOG_ERROR
        sdkConfig.logListener = { logLevel, logContent in
            TLog("💥--\(logLevel)---\(logContent)")
        }
        
        V2TIMManager.shared.initSDK(sdkAppID: Int32(kenv.imAppid.int ?? 0), config: sdkConfig)
        V2TIMManager.shared.addIMSDKListener(listener: self)
        V2TIMManager.shared.addConversationListener(listener: self)
        V2TIMManager.shared.addAdvancedMsgListener(listener: self)
        V2TIMManager.shared.addSimpleMsgListener(listener: self)
    }
    
    
    func loginToChat() {
        
        NetworkUtility.request(target: .ImSign) { result in
            if result.isError { return }
            
            guard let sign = result.dataJson?.rawString(), sign.count > 10 else { return }
            self.userSign = sign
            V2TIMManager.shared.login(userID: kuser.imNumber.string, userSig: sign) { [weak self] in
                AppTool.tx_logAdd("💥Chat--登录成功")
                Async.main(after: 0.01) {
                    self?.getAllUnred()
                    self?.getNoticeUnread()
//                    self?.configureAPNS()
//                    self?.setChatUserInfo()
                    self?.joinGroup()
                }
            } fail: { code, desc in
                AppTool.tx_logAdd("💥Chat--\(code)---\(desc)")
            }
        }
    }
    
    /// 未读数获取
    func getAllUnred() {
        
//        let filter = V2TIMConversationListFilter()
//        filter.type = .V2TIM_C2C
//        
//        V2TIMManager.shared.getUnreadMessageCountByFilter(filter: filter) { totalCount in
//            TLog("getTotalUnreadMessageCount succ, \(totalCount)")
//            self.totalCount = totalCount
//            
//
//            
//            
//            NotificationCenter.default.post(name: .tabUnreadNotification, object: 3)
//        } fail: { code, desc in
//            TLog("getTotalUnreadMessageCount fail, \(code), \(desc)")
//        }
        var totalUnreadCount = 0
        let filter = V2TIMConversationListFilter()
        filter.type = .V2TIM_C2C
        //[.system, .meet, .gift, .cpOfficial, .hudongNotice]
        V2TIMManager.shared.getConversationListByFilter(filter: filter, nextSeq: 0, count: 500) { list, nextSeq, isFinished in
            
               list.forEach { vv in
                   
                   if let id = vv.userID?.int {
                       totalUnreadCount += vv.unreadCount
                   } else {
                       switch vv.userID {
                       case SystemNotification.system.key:
                           totalUnreadCount += vv.unreadCount
                       case SystemNotification.meet.key:
                           totalUnreadCount += vv.unreadCount
                       case SystemNotification.gift.key:
                           totalUnreadCount += vv.unreadCount
                       case SystemNotification.cpOfficial.key:
                           totalUnreadCount += vv.unreadCount
                       case SystemNotification.hudongNotice.key:
                           totalUnreadCount += vv.unreadCount
                       default:
                           break
                       }
                   }
               }
            self.totalCount = UInt(totalUnreadCount)
            TLog("--------过滤的未读数----\(totalUnreadCount)")
            NotificationCenter.default.post(name: .tabUnreadNotification, object: 3)

        } fail: { code, desc in
            print("getConversationList fail, \(code), \(desc)")
        }
    }
    
    func getNoticeUnread() {
        
        V2TIMManager.shared.getConversationList(conversationIDList: ["c2c_\(SystemNotification.community.key)"]) { list in
            if let a = list.first, a.unreadCount > 0 {
                self.noticeCount = a.unreadCount
            } else {
                self.noticeCount = 0
            }
            TLog("动态未读数-----\(list.first?.unreadCount ?? 0)")
            NotificationCenter.default.post(name: .tabUnreadNotification, object: 1)
        } fail: { code, desc in
            TLog("getConversationList fail, \(code), \(desc)")
        }
        
    }
    
    
    func loginOut() {
        
        V2TIMManager.shared.logout {
            
        } fail: { code, desc in
            ProgressHUDManager.showTextMessage(desc)
        }
        
    }
    
    func clearAllUnread(uid: String?, callBack: (() -> Void)? = nil) {
        if let uid = uid  {
            
            V2TIMManager.shared.cleanConversationUnreadMessageCount(conversationID: "c2c_\(uid)", cleanTimestamp: 0, cleanSequence: 0) {
                callBack?()
            } fail: { code, msg in
                AppTool.tx_logAdd("未读消息数已读错误：code:\(code),err:\(String(describing: msg))")
            }
            
            return
        }
        
        V2TIMManager.shared.cleanConversationUnreadMessageCount(conversationID: "c2c", cleanTimestamp: 0, cleanSequence: 0, succ: {
            // 清空所有单聊会话未读消息数成功
        }) { code, desc in
            // 清空所有单聊会话未读消息数失败
        }
    }
    
    
    func joinGroup() {
        V2TIMManager.shared.joinGroup(groupID: EnvManager.shared.groupLiveID, msg: nil) {
            TLog("加入大群成功")
        } fail: { code, desc in
            TLog("加入大群失败------\(code)----\(desc)")
        }
    }
    
    
}

extension T_IMHelper: V2TIMSDKListener {
    
    ///被踢了
    func onKickedOffline() {
        LWUserManger.shared.logoutUser(shouldReinitialize: false)
        let ct = "您的账号\(Date().string(withFormat: "yyyy-MM-dd mm:hh:ss"))于在其他设备上登录。当前登录已退出"
        let alert = MessagePopupView(title: "账号下线通知", content: ct)
        alert.confirmButton.setTitle("我知道了", for: .normal)
        alert.onAction = { isC, vv in
            if isC { return }
            LWUserManger.shared.logoutUser()
        }
        alert.updateOne()
        alert.show()
        alert.popupController?.isClickBgDismiss = false
    }
    
    func onConnectSuccess() {
        directMessageCount = 0
    }
    
    func onConnectFailed(code: Int32, err: String) {
        
        if directMessageCount == 20 {
            directMessageCount = 0
            return
        }
        directMessageCount += 1
        
        if directMessageCount != 1 {
            return
        }
        
        AppTool.tx_logAdd("Im 消息链接失败\n原因:\(code)-\(err)")
        
        if code == 9512 {
            ProgressHUDManager.showTextMessage( "当前网络异常，请检查网络")
            return
        }
        
        if chatSessionCount >= 3 {
            ProgressHUDManager.showTextMessage( "已经重试了三次，请退出登录试试！")
            return
        }
        
        LSTPopView.remove(forKey: "TIM_pop")
        let alert = MessagePopupView(title: "温馨提示", content: "Im 消息链接失败\n原因:\(code)-\(err)")
        alert.confirmButton.setTitle("重试一下", for: .normal)
        alert.onAction = { [weak self] isc, vv in
            self?.loginToChat()
        }
        alert.show()
        alert.popupController?.isClickBgDismiss = false
        alert.popupController?.identifier = "TIM_pop"
        chatSessionCount += 1
        
    }
    
    func onUserSigExpired() {
        AppTool.tx_logAdd("💥IM-票据过期")
    }
    
}

extension T_IMHelper: V2TIMAPNSListener {
    func onSetAPPUnreadCount() -> UInt {
        0
    }
}

extension T_IMHelper: V2TIMAdvancedMsgListener {
    
    func onRecvNewMessage(msg: V2TIMMessage) {
        ///群信息过来的
        if let groupId = msg.groupID, groupId.count > 0 {
            guard let data = msg.customElem?.data else { return }
            let jsonData = try? JSON(data: data)
            TLog("zy_tx:收到群组自定义消息---\(groupId)------\(jsonData?.rawString() ?? "")")
            
            let model = RoomBaseMsgModel.deserialize(from: jsonData?.dictionaryObject ?? [:]) ?? RoomBaseMsgModel()
            model.roomBaseMsg = msg
            delegates.forEach({
                $0.groupNewMsg(dict: [groupId: model])
            })
            if groupId == EnvManager.shared.groupLiveID {
                TLog("收到大群消息------")
                return
            }
            
            TLog("\(groupId)------收到 直播群消息")
            return
        }
        
        TLog("im来新消息了-----\(msg.description)")
        ///单聊
        if !SystemNotification.isValidNotificationKey(msg.sender ?? "") {
            let msgModel = MsgBaseModel(msg: msg)
            delegates.forEach({
                $0.onRecvNewMessage(dict: ["\(msg.sender ?? "")": msgModel])
            })
            return
        }
        
    }
    
    //收到已读回执
    func onRecvMessageReadReceipts(receiptList: Array<V2TIMMessageReceipt>) {
    }
    
    
}

extension T_IMHelper: V2TIMSimpleMsgListener {
    
    func onRecvC2CTextMessage(msgID: String, sender: V2TIMUserInfo, text: String?) {
        TLog("zy_tx:收到 C2C 文本消息---\(msgID)--\(text)")
    }
    
    func onRecvC2CCustomMessage(msgID: String, sender: V2TIMUserInfo, customData: Data?) {
        
        let json = JSON(customData)
        let dd = json.dictionaryValue
        TLog("IM_onRecvC2CCustomMessage-------\(dd)")
        
        delegates.forEach({
            $0.refeshCustomSyteam()
        })
    }
    
    func onRecvGroupCustomMessage(msgID: String, groupID: String, sender: V2TIMGroupMemberInfo, customData: Data?) {
        
//        guard let data = customData else { return }
//        let jsonData = try? JSON(data: data)
//
    }
    
    
    
    
}

extension T_IMHelper: V2TIMSignalingListener {
    
}

extension T_IMHelper: V2TIMConversationListener {
    func onSyncServerStart() {
        // 同步服务器会话开始
        TLog("im_同步服务器会话开始")
    }
    
    func onSyncServerFinish() {
        // 同步服务器会话结束
        TLog("im_同步服务器会话结束")
        isFish = true
        NotificationCenter.default.post(name: .reloadMsgNotification, object: nil)
        
    }
    
    func onSyncServerFailed() {
        // 同步服务器会话失败
        TLog("im_同步服务器会话失败")
    }
    
    func onNewConversation(conversationList: Array<V2TIMConversation>) {
        ///除了固定的接受四五条，其他的系统消息 全部过滤掉
        var arr = conversationList.compactMap({ LwSessionModel(session: $0) })
        arr.forEach { model in
            if let a = SystemNotification.fromKey(model.session?.userID ?? "") {
                if !a.isAdd(type: a) {
                    arr.removeAll(where: { $0.session?.userID == model.session?.userID })
                }
            }
        }
        self.delegates.forEach({
            $0.newConversationList(arr: arr)
        })
    }
    
    func onConversationChanged(conversationList: Array<V2TIMConversation>) {
        // 收到会话变更通知，conversationList 中为变更后的会话对象
        var dict: [String: V2TIMConversation] = [:]
        conversationList.forEach { model in
            dict["\(model.userID ?? "")"] = model
        }
        
        delegates.forEach({
            $0.updateConversation(dict: dict)
        })
        TLog("im_update收到会话变更通知-------\(dict)")
        if let a = dict[SystemNotification.community.key] {
            noticeCount = a.unreadCount
            NotificationCenter.default.post(name: .tabUnreadNotification, object: 1)
        }
        
        getAllUnred()
    }
    
    func onConversationDeleted(conversationIDList: Array<String>) {
        // 会话被删除
        TLog("im_会话删除")
        
        delegates.forEach({
            $0.onRecvDelSession(userids: conversationIDList)
        })
    }
    
    func onTotalUnreadMessageCountChanged(totalUnreadCount: UInt) {
        // 会话未读总数变化
        TLog("im_会话未读总数变化---\(totalUnreadCount)")
//        self.totalCount = totalUnreadCount
//     
//        delegates.forEach({
//            $0.refeshUnread()
//        })
        
//        NotificationCenter.default.post(name: .tabUnreadNotification, object: 3)
       
    }
    
    func onUnreadMessageCountChangedByFilter(filter: V2TIMConversationListFilter, totalUnreadCount: UInt) {
        // 过滤条件下的会话未读总数变化
        TLog("im_过滤条件下的会话未读总数变化\(filter), count:\(totalUnreadCount)")
    }
}

extension T_IMHelper {
    
    func addDelagete(_ delegate: MessageHandlerDelegate)  {
        removeDelegate(delegate)
        weakDelegates.add(delegate as AnyObject)
    }
    
    func removeDelegate(_ delegate: MessageHandlerDelegate?) {
        if let delegate = delegate {
            weakDelegates.remove(delegate as AnyObject)
        }
    }
    
    func removeAllDelegates() {
        weakDelegates.removeAllObjects()
    }
    
}
