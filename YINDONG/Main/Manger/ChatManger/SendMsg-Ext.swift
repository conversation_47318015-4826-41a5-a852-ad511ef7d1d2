//
//  SendMsg-Ext.swift
//  YINDONG
//
//  Created by jj on 2025/3/27.
//

import UIKit

enum IMMsgType {
    case text(content: String)
    case photo(imagePath: String)
    case voice(path: String, dd: Int)
    
    // 计算属性返回 V2TIMMessage
    var v2TIMMessage: V2TIMMessage? {
        switch self {
        case .text(let content):
            return V2TIMManager.shared.createTextMessage(text: content)
        case .photo(let imagePath):
            return V2TIMManager.shared.createImageMessage(imagePath: imagePath)
        case .voice(let path, let dd):
            return V2TIMManager.shared.createSoundMessage(audioFilePath: path, duration: dd)
        }
    }
}

extension T_IMHelper {
    
    func sendV2TIMMessage(type: IMMsgType, toUser: V2TIMFriendInfo?, msgCallBack: ((Bool, String) -> Void)?) -> V2TIMMessage? {
        
        // 提前解包 toUser
        guard let user = toUser else {
            TLog("IM-----无效的用户信息")
            msgCallBack?(false, "Invalid User")
            return nil
        }
        
        // 直接获取 V2TIMMessage，减少 switch 代码
        guard var message = type.v2TIMMessage else {
            TLog("IM-----消息创建失败")
            msgCallBack?(false, "Message creation failed")
            return nil
        }
        
        //有装扮 并且有气泡的情况
        if let model = UserDisguiseModel.load(), var bluue = model.getBluueInfo() {
            message.cloudCustomData = bluue.toDictionary()?.jsonData()
        }
    
        // 发送消息
        let msgId = V2TIMManager.shared.sendMessage(
            message: message,
            receiver: user.userID,
            groupID: nil,
            priority: .V2TIM_PRIORITY_DEFAULT,
            onlineUserOnly: false,
            offlinePushInfo: nil,
            progress: nil
        ) { [weak self] in
            TLog("IM-----消息发送成功---\(message.status)")
            msgCallBack?(true, message.msgID)
        } fail: { [weak self] code, description in
            self?.handleIMSendError(code: code, description: description, user: user)
            msgCallBack?(false, message.msgID)
        }
        
        
        
        TLog("IM-----发送结果: \(message.msgID ?? "nil")-----\(msgId)----\(message.status)")
        return message
    }
    
    /// 统一处理 IM 发送错误
    private func handleIMSendError(code: Int, description: String, user: V2TIMFriendInfo?) {
        TLog("IM-----错误代码: \(code)---\(description)")
        
        AppTool.tx_logAdd("IM-----错误代码: \(code)---\(description)")
        
        switch code {
        case 120010, 120100:
            ProgressHUDManager.showTextMessage(description)
        default:
            ProgressHUDManager.showTextMessage("IM-发送失败-\(code)--\(description)")
        }
    }
    
    
    ///清理指定消息的未读数
    func clearUnreadMessage(_ conversationID: String?) {
        guard let conversationID = conversationID else { return }
        V2TIMManager.shared.cleanConversationUnreadMessageCount(conversationID: conversationID, cleanTimestamp: 0, cleanSequence: 0, succ: {
            
        }) { code, desc in
            
        }
    }
    
    func clearAllUnread() {
        
        V2TIMManager.shared.cleanConversationUnreadMessageCount(conversationID: "", cleanTimestamp: 0, cleanSequence: 0) {
            ProgressHUDManager.showTextMessage("清理完成")
        } fail: { code, desc in
            ProgressHUDManager.showTextMessage(desc)
        }

    }
    
    ///会话置顶
    func sessionTop(conversationID: String, isPinned: Bool, pinnedCallBack: (() -> Void)?) {
        V2TIMManager.shared.pinConversation(conversationID: conversationID, isPinned: isPinned) {
            pinnedCallBack?()
        } fail: { code, desc in
            TLog("im---pinConversation fail \(isPinned), \(code), \(desc)")
            ProgressHUDManager.showTextMessage("置顶异常，请重试-\(desc)")
        }
    }
    
    ///会话删除
    func sessionRemove(conversationID: String, removeCallBack: (() -> Void)?) {
        V2TIMManager.shared.deleteConversation(conversation: conversationID) {
            TLog("deleteConversation succ")
            removeCallBack?()
        } fail: { code, desc in
            TLog("im---deleteConversation fail, \(code), \(desc)")
            ProgressHUDManager.showTextMessage("会话删除失败，请重试-\(desc)")
        }
    }
    
    ///清空聊天记录
    func sessionRemoveMessage(uid: String?, removeCallBack: (() -> Void)?) {
        guard let uid = uid else { return }
        V2TIMManager.shared.clearC2CHistoryMessage(userID: uid) {
            ProgressHUDManager.showTextMessage("清除完成")
            NotificationCenter.default.post(name: .removeMessageNotification, object: nil)

        } fail: { code, desc in
            TLog("clearC2CHistoryMessage fail, \(code), \(desc)")
        }
    }
    
    ///发送已读回执
    func sendMessageRead(messageList: [V2TIMMessage]) {
        V2TIMManager.shared.sendMessageReadReceipts(messageList: messageList, succ: {
               // 发送消息已读回执成功
            TLog("发送消息已读回执成功")
        }, fail: { code, desc in
            // 发送消息已读回执失败
            TLog("发送消息已读回执失败，code: \(code), desc: \(desc)")
        })
    }
    
    
    
    ///发送房间自定义消息
    func sendRoomCustomMsg(_ cmd: String?, message: Data?, callback: ((_ code: Int, _ msg: String) -> Void)?) {
        guard let cmd = cmd, let message = message else { return }
        V2TIMManager.shared.sendGroupCustomMessage(customData: message, to: cmd, priority: .V2TIM_PRIORITY_NORMAL) {
            callback?(0,"发送成功")
        } fail: { code, desc in
            callback?(Int(code), desc)
        }
    }
    
    ///发送IM 自定义消息 {
//     "roomId": "90018608",
//     "roomCover": "https:\/\/houtaiproject.oss-cn-shenzhen.aliyuncs.com\/social\/system\/avatar\/avatar7.png",
//     "roomType": 1,
//     "type": 1015,
//     "roomTitle": "交友聊天室热热热热热热热热挺有"
// } 发送私聊的自定义消息 不是房间的

    func sendRoomCustomMsg(uid: String?, roomModel: RoomInviteModel, callback: ((_ code: Int, _ msg: String) -> Void)?) {
    
        guard let data = try? roomModel.toJSONString()?.data(using: .utf8) else { return }
        
        V2TIMManager.shared.sendC2CCustomMessage(customData: data, to: uid ?? "") {
            callback?(0, "发送成功")
        } fail: { code, desc in
            callback?(Int(code), desc)
        }
    }

    
}
