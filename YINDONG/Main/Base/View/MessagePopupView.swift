//
//  MessagePopupView.swift
//  YINDONG
//
//  Created by jj on 2025/3/12.
//

import UIKit
import LSTPopView

class MessagePopupView: CustomPopupView {

    // MARK: - Properties
    private let maxContentHeight: CGFloat = 275
    private let horizontalPadding: CGFloat = 24
    private let titleSpacing: CGFloat = 16
    private let buttonHeight: CGFloat = 44
    private let buttonSpacing: CGFloat = 24
    
    lazy var topImgV: UIImageView = {
        let imgV = UIImageView()
        return imgV
    }()
    
    lazy var bgImgV: UIImageView = {
        let imgV = UIImageView()
        return imgV
    }()
    
    lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layerCornerRadius = 20
        return view
    }()
    
    lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .textColor3
        label.font = .medium(17)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    lazy var contentLabel: UILabel = {
        let label = UILabel()
        label.textColor = .textColor6
        label.font = .regular(14)
        label.numberOfLines = 0
        return label
    }()
    
    lazy var buttonsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 15
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    lazy var cancelButton: UIButton = {
        let button = UIButton()
        button.setTitle("取消", for: .normal)
        button.setTitleColor(.init(hex: 0x363853), for: .normal)
        button.titleLabel?.font = .bold(14)
        button.backgroundColor = .init(hex: 0xF6F6FB)
        button.layer.cornerRadius = 22
        button.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        return button
    }()
    
    lazy var confirmButton: UIButton = {
        let button = UIButton(title: "确定", font: .bold(13))
        button.backgroundColor = .themColor
        button.setTitleColor(.white, for: .normal)
        button.layerCornerRadius = 22
        button.addTarget(self, action: #selector(confirmButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    convenience init(title: String, content: String) {
        self.init(frame: .zero)
        titleLabel.text = title
        contentLabel.text = content
        contentLabel.textAlignment = .center
        setupUI()
        setupConstraints()
        calculateAndUpdateHeight()
    }
    
    
    convenience init(topImgName: String?, title: String, content: String, bottomT: String?) {
        self.init(frame: .zero)
        titleLabel.text = title
        contentLabel.text = content
        setupUI()
        topImgV.image = UIImage(named: topImgName ?? "")
        setupImgConstraints()
        
        contentLabel.textAlignment = .center
        var m = 80.0
        if let t = bottomT {
            let button = UIButton(title: t, font: .regular(14), color: .textColor6)
            containerView.addSubview(button)
            button.snp.makeConstraints { make in
                make.top.equalTo(buttonsStackView.snp.bottom).offset(14)
                make.centerX.equalToSuperview()
            }
            m += 45
        }
        calculateAndUpdateHeight(topMagrin: m)
    }
    
    func updateOne() {
        cancelButton.isHidden = true
        confirmButton.backgroundColor = .themColor
        confirmButton.setTitleColor(.white, for: .normal)
    }
    
    // MARK: - UI Setup
    override func configureUI() {
        super.configureUI()
        backgroundColor = .clear
        
    }
    
    private func setupUI() {
        addSubview(containerView)
        addSubview(topImgV)
        containerView.addSubview(bgImgV)
        containerView.addSubview(titleLabel)
        containerView.addSubview(scrollView)
        scrollView.addSubview(contentLabel)
        containerView.addSubview(buttonsStackView)
        
        buttonsStackView.addArrangedSubview(cancelButton)
        buttonsStackView.addArrangedSubview(confirmButton)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(30)
            make.left.right.equalToSuperview()
        }
        
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(titleSpacing)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(40)
        }
        
        contentLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
        }
        
        buttonsStackView.snp.makeConstraints { make in
            make.top.equalTo(scrollView.snp.bottom).offset(buttonSpacing)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(buttonHeight)
        }
        
        bgImgV.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(100)
        }
    }
    
    private func setupImgConstraints() {
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalToSuperview().inset(35)
        }
        
        topImgV.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(topImgV.snp.bottom).offset(10)
            make.left.right.equalToSuperview()
        }
        
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(titleSpacing)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(40)
        }
        
        contentLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
        }
        
        buttonsStackView.snp.makeConstraints { make in
            make.top.equalTo(scrollView.snp.bottom).offset(buttonSpacing)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(buttonHeight)
        }
    }
    
    private func calculateAndUpdateHeight(topMagrin: CGFloat = 0) {
        let availableWidth = 305 - (horizontalPadding * 2) // Account for both container and popup padding
        
        // Calculate title height
        let titleSize = titleLabel.sizeThatFits(CGSize(width: availableWidth, height: .greatestFiniteMagnitude))
        // Calculate content height
        let contentSize = contentLabel.sizeThatFits(CGSize(width: availableWidth, height: .greatestFiniteMagnitude))
        let contentHeight = min(contentSize.height, maxContentHeight)
        
        // Calculate total height
        let totalHeight = horizontalPadding * 2 + // Top and bottom padding
        titleSize.height +        // Title height
        titleSpacing +            // Spacing between title and content
        contentHeight +           // Content height (capped at maxContentHeight)
        buttonSpacing +           // Spacing between content and buttons
        buttonHeight              // Button height
        
        // Update scroll view height
        scrollView.snp.updateConstraints { make in
            make.height.equalTo(contentHeight)
        }
        
        // Update content size for scrolling
        scrollView.contentSize = CGSize(width: availableWidth, height: contentSize.height)
        
        // Set frame size
        frame = CGRect(x: 0, y: 0, width: 305, height: totalHeight + topMagrin)
        
//        bgImgV.image = UIImage(dvt: [.init(hex: 0xFFEFC4), .white], size: CGSize(width: 305, height: 100), direction: .top2bottom)
    }
    
    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismiss()
    }
    
    @objc private func confirmButtonTapped() {
        if isNotAnim {
            self.popupController?.dismiss(with: .NO, duration: 0)
        } else {
            dismiss()
        }
        
        onAction?(false, self)
    }

    var isNotAnim: Bool = false
}





// 枚举：定义不同优先级的弹窗等级
enum PopupPriority: CGFloat {
    case defaultLevel = 0
    case updateApp = 100
}

class CustomPopupView: UIView {
    
    // 属性：弹窗的优先级、弹窗控制器和回调事件
    var priority: PopupPriority = .defaultLevel
    var popupController: LSTPopView?
    var onAction: ((_ isDismissed: Bool, _ view: CustomPopupView) -> Void)?
    
    // 初始化器
    convenience init() {
        self.init(frame: .zero)
        configureUI()
    }
    
    // 配置UI样式
    func configureUI() {
        backgroundColor = .white
        layer.cornerRadius = 16
        // 这里可以继续添加其他样式和布局代码
    }
    
    // 显示弹窗的方法，支持自定义父视图、背景透明度和动画样式
    func show(in parentView: UIView? = nil, backgroundAlpha: CGFloat = 0.3, animationStyle: LSTPopStyle = .scale) {
        guard let popController = LSTPopView.initWithCustomView(self, parentView: parentView, popStyle: .springFromTop, dismissStyle: .smoothToTop) else { return }
        self.popupController = popController
        popController.isClickBgDismiss = true
        popController.priority = priority.rawValue
        popController.bgAlpha = backgroundAlpha
        popController.pop()
    }
    
    // 隐藏弹窗的方法
    @objc
    func dismiss() {
        popupController?.dismiss()
        onAction?(true, self)
    }
    
}

