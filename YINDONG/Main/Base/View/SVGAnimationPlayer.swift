//
//  SVGAnimationPlayer.swift
//  YINDONG
//
//  Created by jj on 2025/3/5.
//

import UIKit
import SVGAPlayer

class SVGAnimationPlayer: SVGAPlayer {
 
    var isAutoPlayEnabled: Bool = true
    var isEventPierceEnabled: Bool = true
    
    lazy var imgV: UIImageView = {
        let imgV = UIImageView(image: UIImage(named: ""))
        imgV.isHidden = true
        return imgV
    }()
    
    var resourceName: String = "" {
        didSet {
            loadAnimation(named: resourceName)
        }
    }
    
    lazy var svgaParser: SVGAParser = {
        let parser = SVGAParser()
        parser.enabledMemoryCache = true
        return parser
    }()
    
    func loadAnimation(named name: String) {
        if name.hasPrefix("http") || name.hasPrefix("https") {
            if let url = URL(string: name) {
                parseAndStartAnimation(from: url)
            }
        } else {
            parseAndStartAnimation(named: name)
        }
    }
    
    func parseAndStartAnimation(from url: URL?) {
        guard let url = url else { return  }
        svgaParser.parse(with: url) { [weak self] videoItem in
            guard let self = self, let videoItem = videoItem else { return }
            self.setAndPlayAnimation(videoItem)
        }
    }
    
    func parseAndStartAnimation(named name: String) {
        svgaParser.parse(withNamed: name, in: .main) { [weak self] videoItem in
            guard let self = self else { return }
            self.setAndPlayAnimation(videoItem)
        }
    }
    
    private func setAndPlayAnimation(_ videoItem: SVGAVideoEntity) {
        self.videoItem = videoItem
        if isAutoPlayEnabled {
            startAnimation()
        }
    }
    
    //清除特殊 svga 的背景
    func setImgClear() {
        self.setImage(UIImage(color: .clear, size: CGSizeMake(100, 100)), forKey: "200452w91lfr4q4o7fzupl")
    }
    
    override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        let view = super.hitTest(point, with: event)
        if view == self {
            return isEventPierceEnabled ? nil : view
        }
        return view
    }
    
    private func decrypt(_ text: String) -> String {
        // Your decryption logic here
        return text
    }
}
