//
//  APIHistorySheetView.swift
//  YINDONG
//
//  Created by jj on 2025/6/23.
//

import UIKit
import HWPanModal

protocol APIHistoryDelegate: AnyObject {
    func didSelectAPIConfig(_ config: APIConfig)
    func didSelectGameAPIConfig(_ config: GameAPIConfig)
    func didSelectH5Config(_ config: H5Config)
}

class APIHistorySheetView: HWPanModalContentView {
    
    enum HistoryType {
        case api
        case gameApi
        case h5
        
        var title: String {
            switch self {
            case .api: return "API历史记录"
            case .gameApi: return "游戏API历史记录"
            case .h5: return "H5历史记录"
            }
        }
    }
    
    // MARK: - Properties
    weak var delegate: APIHistoryDelegate?
    var historyType: HistoryType
    private var historyData: [Any] = []
    
    // MARK: - UI Components
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(18)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private lazy var clearButton: UIButton = {
        let button = UIButton()
        button.setTitle("清除记录", for: .normal)
        button.setTitleColor(.systemRed, for: .normal)
        button.titleLabel?.font = .medium(14)
        button.addTarget(self, action: #selector(clearHistoryTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.backgroundColor = .clear
        table.separatorStyle = .none
        table.delegate = self
        table.dataSource = self
        table.register(APIHistoryCell.self, forCellReuseIdentifier: "APIHistoryCell")
        table.showsVerticalScrollIndicator = false
        return table
    }()
    
    private lazy var emptyLabel: UILabel = {
        let label = UILabel()
        label.text = "暂无历史记录"
        label.font = .regular(16)
        label.textColor = UIColor.white.withAlphaComponent(0.6)
        label.textAlignment = .center
        label.isHidden = true
        return label
    }()
    
    // MARK: - Initialization
    convenience init(type: HistoryType) {
        self.init(frame: .zero)
        self.historyType = type
        setupUI()
        loadHistoryData()
    }
    
    override init(frame: CGRect) {
        self.historyType = .api
        super.init(frame: frame)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        backgroundColor = .clear
        
        // 圆角背景
        let backgroundView = UIView()
        backgroundView.backgroundColor = UIColor(hex: 0x1F1C45)
        backgroundView.layer.cornerRadius = 18
        addSubview(backgroundView)
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 标题和清除按钮
        let headerView = UIView()
        backgroundView.addSubview(headerView)
        headerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(60)
        }
        
        titleLabel.text = historyType.title
        headerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        headerView.addSubview(clearButton)
        clearButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-16)
        }
        
        // 分割线
        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor.white.withAlphaComponent(0.1)
        backgroundView.addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(1)
        }
        
        // 表格视图
        backgroundView.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(separatorLine.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(300)
        }
        
        // 空状态标签
        backgroundView.addSubview(emptyLabel)
        emptyLabel.snp.makeConstraints { make in
            make.centerX.equalTo(tableView)
            
        }
    }
    
    // MARK: - Data Loading
    private func loadHistoryData() {
        switch historyType {
        case .api:
            historyData = EnvManager.shared.getAPIHistory()
        case .gameApi:
            historyData = EnvManager.shared.getGameAPIHistory()
        case .h5:
            historyData = EnvManager.shared.getH5History()
        }
        
        updateUI()
    }
    
    private func updateUI() {
        let isEmpty = historyData.isEmpty
        emptyLabel.isHidden = !isEmpty
        tableView.isHidden = isEmpty
        clearButton.isHidden = isEmpty
        
        tableView.reloadData()
    }
    
    // MARK: - Actions
    @objc private func clearHistoryTapped() {
        let alert = UIAlertController(
            title: "清除历史记录",
            message: "确定要清除所有历史记录吗？\n最新的一条配置将会保留",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "确定", style: .destructive) { _ in
            self.clearHistory()
        })
        
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        AppTool.getCurrentViewController().present(alert, animated: true)
    }
    
    private func clearHistory() {
        switch historyType {
        case .api:
            EnvManager.shared.clearAPIHistory()
        case .gameApi:
            EnvManager.shared.clearGameAPIHistory()
        case .h5:
            EnvManager.shared.clearH5History()
        }
        
        // 强制同步数据
        UserDefaults.standard.synchronize()
        
        // 重新加载数据
        loadHistoryData()
        
        ProgressHUDManager.showTextMessage("历史记录已清除")
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate
extension APIHistorySheetView: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return historyData.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "APIHistoryCell", for: indexPath) as! APIHistoryCell
        
        let item = historyData[indexPath.row]
        
        if let apiConfig = item as? APIConfig {
            cell.configure(with: apiConfig)
        } else if let gameApiConfig = item as? GameAPIConfig {
            cell.configure(with: gameApiConfig)
        } else if let h5Config = item as? H5Config {
            cell.configure(with: h5Config)
        }
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let item = historyData[indexPath.row]
        
        if let apiConfig = item as? APIConfig {
            delegate?.didSelectAPIConfig(apiConfig)
        } else if let gameApiConfig = item as? GameAPIConfig {
            delegate?.didSelectGameAPIConfig(gameApiConfig)
        } else if let h5Config = item as? H5Config {
            delegate?.didSelectH5Config(h5Config)
        }
        
        dismiss(animated: true) {
            
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 70
    }
}

// MARK: - APIHistoryCell
class APIHistoryCell: UITableViewCell {
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.05)
        view.layer.cornerRadius = 8
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.white.withAlphaComponent(0.1).cgColor
        return view
    }()
    
    private lazy var urlLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(16)
        label.textColor = .white
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(12)
        label.textColor = UIColor.white.withAlphaComponent(0.7)
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var arrowImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = UIColor.white.withAlphaComponent(0.3)
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(4)
            make.left.right.equalToSuperview()
        }
        
        containerView.addSubview(arrowImageView)
        arrowImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().offset(-16)
            make.size.equalTo(16)
        }
        
        containerView.addSubview(urlLabel)
        urlLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalTo(arrowImageView.snp.left).offset(-8)
        }
        
        containerView.addSubview(descriptionLabel)
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(urlLabel.snp.bottom).offset(4)
            make.left.equalToSuperview().offset(16)
            make.right.equalTo(arrowImageView.snp.left).offset(-8)
            make.bottom.equalToSuperview().offset(-12)
        }
    }
    
    func configure(with config: APIConfig) {
        urlLabel.text = config.fullURL
        descriptionLabel.text = config.description
    }
    
    func configure(with config: GameAPIConfig) {
        urlLabel.text = config.fullURL
        descriptionLabel.text = config.description
    }
    
    func configure(with config: H5Config) {
        urlLabel.text = config.fullURL
        descriptionLabel.text = config.description
    }
}



// MARK: - 面板设置
extension APIHistorySheetView {
    override func anchorModalToLongForm() -> Bool {
        return false
    }
    
    override func cornerRadius() -> CGFloat {
        return 18
    }
    
    override func allowsDragToDismiss() -> Bool {
        return true
    }
    
    override func showDragIndicator() -> Bool {
        return false
    }
    
    override func longFormHeight() -> PanModalHeight {
        // 固定面板高度，内容通过滚动展示
        return PanModalHeightMake(.content, 380 + AppTool.safeAreaBottomHeight)
    }
    
    override func shortFormHeight() -> PanModalHeight {
        return longFormHeight()
    }
    
    override func shouldRespond(toPanModalGestureRecognizer panGestureRecognizer: UIPanGestureRecognizer) -> Bool {
        let loc = panGestureRecognizer.location(in: self)
        return !CGRectContainsPoint(self.frame, loc);
    }
    
    
}
