//
//  EnvConfigVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/18.
//

import UIKit
import SnapKit

class EnvConfigVC: YinDBaseVC {
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    // 请求地址选择
    private lazy var apiSectionLabel: UILabel = {
        let label = UILabel()
        label.text = "请求地址"
        label.font = .medium(16)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var apiButtonsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    // 家族打怪、符石寻宝地址选择
    private lazy var gameSectionLabel: UILabel = {
        let label = UILabel()
        label.text = "家族打怪、符石寻宝地址"
        label.font = .medium(16)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var gameButtonsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    // APP内常用H5地址
    private lazy var h5SectionLabel: UILabel = {
        let label = UILabel()
        label.text = "APP内常用H5地址(无法更改Banner上配置的)"
        label.font = .medium(16)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var h5ButtonsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    // 自定义配置按钮
    private lazy var customConfigButton: UIButton = {
        let button = UIButton()
        button.setTitle("自定义配置", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor(hex: 0x007AFF)
        button.layer.cornerRadius = 25
        button.titleLabel?.font = .medium(16)
        button.addTarget(self, action: #selector(openCustomConfig), for: .touchUpInside)
        return button
    }()
    
    // 自定义H5地址输入
    private lazy var customH5Label: UILabel = {
        let label = UILabel()
        label.text = "打开特定H5"
        label.font = .medium(16)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var customH5InputView: UIView = {
        let view = UIView()
        view.backgroundColor = .f8Color
        view.layer.cornerRadius = 8
        return view
    }()
    
    private lazy var customH5TextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "输入链接地址"
        textField.font = .regular(14)
        textField.textColor = .textColor3
        return textField
    }()
    
    private lazy var openH5Button: UIButton = {
        let button = UIButton()
        button.setTitle("打开", for: .normal)
        button.setTitleColor(.themColor, for: .normal)
        button.titleLabel?.font = .medium(14)
        button.addTarget(self, action: #selector(openCustomH5), for: .touchUpInside)
        return button
    }()
    
    // 主场景渠道缓存
    private lazy var channelLabel: UILabel = {
        let label = UILabel()
        label.text = "主场景渠道缓存"
        label.font = .medium(16)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var channelInputView: UIView = {
        let view = UIView()
        view.backgroundColor = .f8Color
        view.layer.cornerRadius = 8
        return view
    }()
    
    private lazy var channelTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "ALI"
        textField.font = .regular(14)
        textField.textColor = .textColor3
        return textField
    }()
    
    private lazy var saveChannelButton: UIButton = {
        let button = UIButton()
        button.setTitle("保存", for: .normal)
        button.setTitleColor(.themColor, for: .normal)
        button.titleLabel?.font = .medium(14)
        button.addTarget(self, action: #selector(saveChannel), for: .touchUpInside)
        return button
    }()
    
    // 复制Token按钮
    private lazy var copyTokenButton: UIButton = {
        let button = UIButton()
        button.setTitle("复制Token", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .themColor
        button.layer.cornerRadius = 25
        button.titleLabel?.font = .medium(16)
        button.addTarget(self, action: #selector(copyToken), for: .touchUpInside)
        return button
    }()

    // MARK: - Version Section

    /// 版本号标签
    private lazy var versionLabel: UILabel = {
        let label = UILabel()
        label.text = "版本号配置"
        label.font = .medium(16)
        label.textColor = .textColor6
        return label
    }()

    /// 版本号输入容器
    private lazy var versionInputView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 8
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.lightGray.cgColor
        return view
    }()

    /// 版本号输入框
    private lazy var versionTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "输入自定义版本号"
        textField.font = .regular(14)
        textField.textColor = .textColor6
        textField.text = AppTool.appVersion
        return textField
    }()

    /// 版本号历史按钮
    private lazy var versionHistoryButton: UIButton = {
        let button = UIButton()
        button.setTitle("历史", for: .normal)
        button.setTitleColor(.themColor, for: .normal)
        button.titleLabel?.font = .medium(14)
        button.addTarget(self, action: #selector(showVersionHistory), for: .touchUpInside)
        return button
    }()

    /// 保存版本号按钮
    private lazy var saveVersionButton: UIButton = {
        let button = UIButton()
        button.setTitle("保存", for: .normal)
        button.setTitleColor(.themColor, for: .normal)
        button.titleLabel?.font = .medium(14)
        button.addTarget(self, action: #selector(saveVersion), for: .touchUpInside)
        return button
    }()

    /// 重置版本号按钮
    private lazy var resetVersionButton: UIButton = {
        let button = UIButton()
        button.setTitle("重置", for: .normal)
        button.setTitleColor(.red, for: .normal)
        button.titleLabel?.font = .medium(14)
        button.addTarget(self, action: #selector(resetVersion), for: .touchUpInside)
        return button
    }()

    /// 当前版本号显示标签
    private lazy var currentVersionLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(12)
        label.textColor = .gray
        label.text = "当前版本号: \(AppTool.appVersion)"
        return label
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        navigationTitle = "测试配置项"
        view.backgroundColor = .f9Color
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        setupApiSection()
        setupGameSection()
        setupH5Section()
        setupCustomConfigButton()
        setupCustomH5Section()
        setupChannelSection()
        setupVersionSection()
        setupCopyTokenButton()
    }
    
    private func setupApiSection() {
        contentView.addSubview(apiSectionLabel)
        contentView.addSubview(apiButtonsStackView)
        
        apiSectionLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.equalToSuperview().offset(16)
        }
        
        apiButtonsStackView.snp.makeConstraints { make in
            make.top.equalTo(apiSectionLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(40)
        }
        
        // 创建API环境按钮
        let apiEnvironments: [(String, String)] = [
            ("正式", "dis"),
            ("测试", "test"),
            ("2", "192.168.3.2"),
            ("13", "192.168.3.13"),
            ("18", "192.168.3.18")
        ]

        for (title, value) in apiEnvironments {
            let button = createPresetButton(title: title, value: value, type: .api)
            apiButtonsStackView.addArrangedSubview(button)
        }
    }
    
    private func setupGameSection() {
        contentView.addSubview(gameSectionLabel)
        contentView.addSubview(gameButtonsStackView)
        
        gameSectionLabel.snp.makeConstraints { make in
            make.top.equalTo(apiButtonsStackView.snp.bottom).offset(30)
            make.left.equalToSuperview().offset(16)
        }
        
        gameButtonsStackView.snp.makeConstraints { make in
            make.top.equalTo(gameSectionLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(40)
        }
        
        // 创建游戏环境按钮（家族打怪、符石寻宝）
        let gameEnvironments = [
            ("正式", "webactvt.tongzhuocp.com/game"),
            ("4", "192.168.3.4:5173"),
            ("223", "192.168.3.223:5173"),
            ("252", "192.168.3.252:5173")
        ]
        
        for (title, value) in gameEnvironments {
            let button = createPresetButton(title: title, value: value, type: .game)
            gameButtonsStackView.addArrangedSubview(button)
        }
    }
    
    private func setupH5Section() {
        contentView.addSubview(h5SectionLabel)
        contentView.addSubview(h5ButtonsStackView)
        
        h5SectionLabel.snp.makeConstraints { make in
            make.top.equalTo(gameButtonsStackView.snp.bottom).offset(30)
            make.left.equalToSuperview().offset(16)
        }
        
        h5ButtonsStackView.snp.makeConstraints { make in
            make.top.equalTo(h5SectionLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(40)
        }
        
        // 创建H5环境按钮（APP内常用H5）
        let h5Environments = [
            ("正式", "webactvt.tongzhuocp.com"),
            ("4", "192.168.3.4:8080"),
            ("223", "192.168.3.223:8080"),
            ("252", "192.168.3.252:8080")
        ]
        
        for (title, value) in h5Environments {
            let button = createPresetButton(title: title, value: value, type: .h5)
            h5ButtonsStackView.addArrangedSubview(button)
        }
    }
    
    private func setupCustomConfigButton() {
        contentView.addSubview(customConfigButton)

        customConfigButton.snp.makeConstraints { make in
            make.top.equalTo(h5ButtonsStackView.snp.bottom).offset(30)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
        }
    }

    private func setupCustomH5Section() {
        contentView.addSubview(customH5Label)
        contentView.addSubview(customH5InputView)

        customH5InputView.addSubview(customH5TextField)
        customH5InputView.addSubview(openH5Button)

        customH5Label.snp.makeConstraints { make in
            make.top.equalTo(customConfigButton.snp.bottom).offset(30)
            make.left.equalToSuperview().offset(16)
        }

        customH5InputView.snp.makeConstraints { make in
            make.top.equalTo(customH5Label.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        customH5TextField.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.right.equalTo(openH5Button.snp.left).offset(-12)
        }

        openH5Button.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.equalTo(50)
        }
    }

    private func setupChannelSection() {
        contentView.addSubview(channelLabel)
        contentView.addSubview(channelInputView)

        channelInputView.addSubview(channelTextField)
        channelInputView.addSubview(saveChannelButton)

        channelLabel.snp.makeConstraints { make in
            make.top.equalTo(customH5InputView.snp.bottom).offset(30)
            make.left.equalToSuperview().offset(16)
        }

        channelInputView.snp.makeConstraints { make in
            make.top.equalTo(channelLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        channelTextField.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.right.equalTo(saveChannelButton.snp.left).offset(-12)
        }

        saveChannelButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.equalTo(50)
        }
    }

    private func setupVersionSection() {
        contentView.addSubview(versionLabel)
        contentView.addSubview(versionInputView)
        contentView.addSubview(currentVersionLabel)

        versionInputView.addSubview(versionTextField)
        versionInputView.addSubview(versionHistoryButton)
        versionInputView.addSubview(saveVersionButton)
        versionInputView.addSubview(resetVersionButton)

        versionLabel.snp.makeConstraints { make in
            make.top.equalTo(channelInputView.snp.bottom).offset(30)
            make.left.equalToSuperview().offset(16)
        }

        versionInputView.snp.makeConstraints { make in
            make.top.equalTo(versionLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }

        versionTextField.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.right.equalTo(versionHistoryButton.snp.left).offset(-8)
        }

        versionHistoryButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(40)
            make.right.equalTo(saveVersionButton.snp.left).offset(-8)
        }

        saveVersionButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(40)
            make.right.equalTo(resetVersionButton.snp.left).offset(-8)
        }

        resetVersionButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(40)
            make.right.equalToSuperview().offset(-12)
        }

        currentVersionLabel.snp.makeConstraints { make in
            make.top.equalTo(versionInputView.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
        }
    }

    private func setupCopyTokenButton() {
        contentView.addSubview(copyTokenButton)

        copyTokenButton.snp.makeConstraints { make in
            make.top.equalTo(currentVersionLabel.snp.bottom).offset(30)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
            make.bottom.equalToSuperview().offset(-30)
        }
    }

    // MARK: - Helper Methods
    enum ConfigType {
        case api, game, h5
    }

    private func createPresetButton(title: String, value: String, type: ConfigType) -> UIButton {
        let button = UIButton()
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = .medium(14)
        button.layer.cornerRadius = 20

        // 存储配置信息
        button.accessibilityIdentifier = "\(type)_\(title)"

        // 设置初始状态
        updateButtonAppearance(button, isSelected: false)

        button.addTarget(self, action: #selector(presetButtonTapped(_:)), for: .touchUpInside)

        return button
    }

    private func updateButtonAppearance(_ button: UIButton, isSelected: Bool) {
        if isSelected {
            button.backgroundColor = .themColor
            button.setTitleColor(.white, for: .normal)
        } else {
            button.backgroundColor = UIColor(hex: 0xFFB6C1, transparency: 0.3)
            button.setTitleColor(.textColor6, for: .normal)
        }
    }

     override func loadData() {
        updateButtonStates()
        updateVersionDisplay()
    }

    private func updateVersionDisplay() {
        versionTextField.text = AppTool.appVersion
        updateCurrentVersionLabel()
    }

    private func updateButtonStates() {
        let currentEnv = EnvManager.shared.currentEnvironment
        let currentAPI = EnvManager.shared.apiURL
        let currentGameAPI = EnvManager.shared.gameApiURL
        let currentH5 = EnvManager.shared.webApiURL

        // 更新所有按钮状态
        [apiButtonsStackView, gameButtonsStackView, h5ButtonsStackView].forEach { stackView in
            stackView.arrangedSubviews.compactMap { $0 as? UIButton }.forEach { button in
                let isSelected = checkButtonSelected(button, currentEnv: currentEnv, currentAPI: currentAPI, currentGameAPI: currentGameAPI, currentH5: currentH5)
                updateButtonAppearance(button, isSelected: isSelected)
            }
        }
    }

    private func checkButtonSelected(_ button: UIButton, currentEnv: EnvType, currentAPI: String, currentGameAPI: String, currentH5: String) -> Bool {
        guard let identifier = button.accessibilityIdentifier else { return false }

        let components = identifier.split(separator: "_")
        guard components.count >= 2 else { return false }

        let type = String(components[0])
        let title = String(components[1])

        switch type {
        case "api":
            if title == "正式" && currentEnv == .dis { return true }
            if title == "测试" && currentEnv == .test { return true }
            if currentAPI.contains(title) { return true }
        case "game":
            if title == "正式" && currentEnv == .dis { return true }
            if currentGameAPI.contains(title) { return true }
        case "h5":
            if title == "正式" && currentEnv == .dis { return true }
            if currentH5.contains(title) { return true }
        default:
            break
        }

        return false
    }

    // MARK: - Actions
    @objc private func presetButtonTapped(_ sender: UIButton) {
        guard let identifier = sender.accessibilityIdentifier else { return }

        let components = identifier.split(separator: "_")
        guard components.count >= 2 else { return }

        let type = String(components[0])
        let title = String(components[1])

        switch type {
        case "api":
            handleAPIButtonTap(title: title)
        case "game":
            handleGameButtonTap(title: title)
        case "h5":
            handleH5ButtonTap(title: title)
        default:
            break
        }
    }

    private func handleAPIButtonTap(title: String) {
        if title == "正式" {
            switchToEnvironment(.dis)
        } else if title == "测试" {
            switchToEnvironment(.test)
        } else {
            // 预设IP地址
            let host = "192.168.3.\(title)"
            let port = "8080"
            let config = APIConfig(host: host, port: port, description: "本地\(title)号")
            switchToAPIConfig(config)
        }
    }

    private func handleGameButtonTap(title: String) {
        if title == "正式" {
            let config = GameAPIConfig(host: "webactvt.tongzhuocp.com", port: nil, description: "正式服游戏")
            switchToGameAPIConfig(config)
        } else {
            let host = "192.168.3.\(title)"
            let port = "5173"
            let config = GameAPIConfig(host: host, port: port, description: "本地\(title)号游戏")
            switchToGameAPIConfig(config)
        }
    }

    private func handleH5ButtonTap(title: String) {
        if title == "正式" {
            let config = H5Config(host: "webactvt.tongzhuocp.com", port: nil, description: "正式服H5")
            switchToH5Config(config)
        } else {
            let host = "192.168.3.\(title)"
            let port = "5173"
            let config = H5Config(host: host, port: port, description: "本地\(title)号H5")
            switchToH5Config(config)
        }
    }

    private func switchToEnvironment(_ envType: EnvType) {
        let alert = UIAlertController(title: "切换环境", message: "确定要切换到 \(envType.displayName) 环境吗？\n\n切换后应用将重启", preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            LWUserManger.shared.logoutUser()
            EnvManager.shared.saveEnvironment(envType)
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        present(alert, animated: true)
    }

    private func switchToAPIConfig(_ config: APIConfig) {
        let alert = UIAlertController(title: "切换API地址", message: "确定要切换到 \(config.description) 吗？\n\n地址: \(config.fullURL)\n\n切换后应用将重启", preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            EnvManager.shared.saveCustomAPIConfig(config)
            LWUserManger.shared.logoutUser()
            EnvManager.shared.saveEnvironment(.custom)
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        present(alert, animated: true)
    }

    private func switchToGameAPIConfig(_ config: GameAPIConfig) {
        let alert = UIAlertController(title: "切换游戏API地址", message: "确定要切换到 \(config.description) 吗？\n\n地址: \(config.fullURL)\n\n✅ 实时生效，无需重启", preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            EnvManager.shared.saveCustomGameAPIConfig(config)
            // 切换到自定义环境（如果当前不是）
            if EnvManager.shared.currentEnvironment != .custom {
                EnvManager.shared.saveEnvironmentWithoutRestart(.custom)
            }
            self.updateButtonStates()
            ProgressHUDManager.showTextMessage("游戏API地址已更新")
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        present(alert, animated: true)
    }

    private func switchToH5Config(_ config: H5Config) {
        let alert = UIAlertController(title: "切换H5地址", message: "确定要切换到 \(config.description) 吗？\n\n地址: \(config.fullURL)\n\n✅ 实时生效，无需重启", preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            EnvManager.shared.saveCustomH5Config(config)
            // 切换到自定义环境（如果当前不是）
            if EnvManager.shared.currentEnvironment != .custom {
                EnvManager.shared.saveEnvironmentWithoutRestart(.custom)
            }
            self.updateButtonStates()
            ProgressHUDManager.showTextMessage("H5地址已更新")
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        present(alert, animated: true)
    }

    @objc private func openCustomConfig() {
        let customConfigVC = CustomConfigVC()
        navigationController?.pushViewController(customConfigVC, animated: true)
    }

    @objc private func openCustomH5() {
        guard let urlString = customH5TextField.text, !urlString.isEmpty else {
            ProgressHUDManager.showTextMessage("请输入H5地址")
            return
        }

        let webVC = LwBaseWebVC()
        webVC.urlString = urlString
        webVC.titleString = "自定义H5"
        navigationController?.pushViewController(webVC, animated: true)
    }

    @objc private func saveChannel() {
        guard let channel = channelTextField.text, !channel.isEmpty else {
            ProgressHUDManager.showTextMessage("请输入渠道信息")
            return
        }

        UserDefaults.standard.set(channel, forKey: "app_channel_cache")
        UserDefaults.standard.synchronize()

        ProgressHUDManager.showTextMessage("渠道信息已保存")
    }

    @objc private func copyToken() {
        guard let token = LWUserManger.getToken() else {
            ProgressHUDManager.showTextMessage("未找到Token")
            return
        }

        UIPasteboard.general.string = token
        ProgressHUDManager.showTextMessage("Token已复制到剪贴板")
    }

    // MARK: - Version Actions

    @objc private func saveVersion() {
        guard let version = versionTextField.text, !version.isEmpty else {
            ProgressHUDManager.showTextMessage("请输入版本号")
            return
        }

        // 验证版本号格式（可选）
        if !isValidVersionFormat(version) {
            let alert = UIAlertController(title: "版本号格式", message: "版本号格式不规范，是否继续保存？\n\n建议格式：x.x.x（如：3.6.4）", preferredStyle: .alert)

            alert.addAction(UIAlertAction(title: "继续保存", style: .default) { _ in
                self.performVersionSave(version)
            })

            alert.addAction(UIAlertAction(title: "取消", style: .cancel))

            present(alert, animated: true)
        } else {
            performVersionSave(version)
        }
    }

    private func performVersionSave(_ version: String) {
        AppTool.setCustomAppVersion(version)
        updateCurrentVersionLabel()
        ProgressHUDManager.showTextMessage("版本号已保存：\(version)")
    }

    @objc private func resetVersion() {
        let alert = UIAlertController(title: "重置版本号", message: "确定要重置为默认版本号吗？\n\n默认版本号：\(AppTool.defaultAppVersion)", preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            AppTool.resetToDefaultAppVersion()
            self.versionTextField.text = AppTool.appVersion
            self.updateCurrentVersionLabel()
            ProgressHUDManager.showTextMessage("已重置为默认版本号")
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        present(alert, animated: true)
    }

    @objc private func showVersionHistory() {
        let history = AppTool.getVersionHistory()

        if history.isEmpty {
            ProgressHUDManager.showTextMessage("暂无历史记录")
            return
        }

        let alert = UIAlertController(title: "版本号历史记录", message: "选择要使用的版本号", preferredStyle: .actionSheet)

        for version in history {
            let action = UIAlertAction(title: version, style: .default) { _ in
                self.versionTextField.text = version
            }
            alert.addAction(action)
        }

        alert.addAction(UIAlertAction(title: "清空历史", style: .destructive) { _ in
            AppTool.clearVersionHistory()
            ProgressHUDManager.showTextMessage("版本号历史记录已清空")
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        // 适配iPad
        if let popover = alert.popoverPresentationController {
            popover.sourceView = versionHistoryButton
            popover.sourceRect = versionHistoryButton.bounds
        }

        present(alert, animated: true)
    }

    private func updateCurrentVersionLabel() {
        currentVersionLabel.text = "当前版本号: \(AppTool.appVersion)"
    }

    private func isValidVersionFormat(_ version: String) -> Bool {
        // 简单的版本号格式验证：x.x.x 或 x.x.x.x
        let pattern = "^\\d+(\\.\\d+){1,3}$"
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: version.count)
        return regex?.firstMatch(in: version, options: [], range: range) != nil
    }
}
