//
//  CustomConfigVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/18.
//

import UIKit
import SnapKit

class CustomConfigVC: YinDBaseVC {
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    // API配置
    private lazy var apiSectionLabel: UILabel = {
        let label = UILabel()
        label.text = "请求地址配置"
        label.font = .medium(18)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var apiHostLabel: UILabel = {
        let label = UILabel()
        label.text = "域名/IP"
        label.font = .medium(14)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var apiHostTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "如: *********** 或 api.example.com"
        textField.font = .regular(14)
        textField.textColor = .textColor3
        textField.borderStyle = .roundedRect
        return textField
    }()
    
    private lazy var apiPortLabel: UILabel = {
        let label = UILabel()
        label.text = "端口 (可选)"
        label.font = .medium(14)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var apiPortTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "如: 8080"
        textField.font = .regular(14)
        textField.textColor = .textColor3
        textField.borderStyle = .roundedRect
        textField.keyboardType = .numberPad
        return textField
    }()
    
    private lazy var apiHistoryButton: UIButton = {
        let button = UIButton()
        button.setTitle("历史记录", for: .normal)
        button.setTitleColor(.themColor, for: .normal)
        button.backgroundColor = UIColor(hex: 0xF0F0F0)
        button.layer.cornerRadius = 8
        button.titleLabel?.font = .medium(14)
        button.addTarget(self, action: #selector(showAPIHistory), for: .touchUpInside)
        return button
    }()
    
    // 游戏API配置
    private lazy var gameApiSectionLabel: UILabel = {
        let label = UILabel()
        label.text = "游戏API地址配置"
        label.font = .medium(18)
        label.textColor = .textColor3
        return label
    }()

    private lazy var gameApiHostLabel: UILabel = {
        let label = UILabel()
        label.text = "域名/IP"
        label.font = .medium(14)
        label.textColor = .textColor3
        return label
    }()

    private lazy var gameApiHostTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "如: *********** 或 game.example.com"
        textField.font = .regular(14)
        textField.textColor = .textColor3
        textField.borderStyle = .roundedRect
        return textField
    }()

    private lazy var gameApiPortLabel: UILabel = {
        let label = UILabel()
        label.text = "端口 (可选)"
        label.font = .medium(14)
        label.textColor = .textColor3
        return label
    }()

    private lazy var gameApiPortTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "如: 5173"
        textField.font = .regular(14)
        textField.textColor = .textColor3
        textField.borderStyle = .roundedRect
        textField.keyboardType = .numberPad
        return textField
    }()

    private lazy var gameApiHistoryButton: UIButton = {
        let button = UIButton()
        button.setTitle("历史记录", for: .normal)
        button.setTitleColor(.themColor, for: .normal)
        button.backgroundColor = UIColor(hex: 0xF0F0F0)
        button.layer.cornerRadius = 8
        button.titleLabel?.font = .medium(14)
        button.addTarget(self, action: #selector(showGameAPIHistory), for: .touchUpInside)
        return button
    }()

    // H5配置
    private lazy var h5SectionLabel: UILabel = {
        let label = UILabel()
        label.text = "H5地址配置"
        label.font = .medium(18)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var h5HostLabel: UILabel = {
        let label = UILabel()
        label.text = "域名/IP"
        label.font = .medium(14)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var h5HostTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "如: *********** 或 h5.example.com"
        textField.font = .regular(14)
        textField.textColor = .textColor3
        textField.borderStyle = .roundedRect
        return textField
    }()
    
    private lazy var h5PortLabel: UILabel = {
        let label = UILabel()
        label.text = "端口 (可选)"
        label.font = .medium(14)
        label.textColor = .textColor3
        return label
    }()
    
    private lazy var h5PortTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "如: 5173"
        textField.font = .regular(14)
        textField.textColor = .textColor3
        textField.borderStyle = .roundedRect
        textField.keyboardType = .numberPad
        return textField
    }()
    
    private lazy var h5HistoryButton: UIButton = {
        let button = UIButton()
        button.setTitle("历史记录", for: .normal)
        button.setTitleColor(.themColor, for: .normal)
        button.backgroundColor = UIColor(hex: 0xF0F0F0)
        button.layer.cornerRadius = 8
        button.titleLabel?.font = .medium(14)
        button.addTarget(self, action: #selector(showH5History), for: .touchUpInside)
        return button
    }()
    
    // 保存按钮
    private lazy var saveButton: UIButton = {
        let button = UIButton()
        button.setTitle("保存配置", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .themColor
        button.layer.cornerRadius = 25
        button.titleLabel?.font = .medium(16)
        button.addTarget(self, action: #selector(saveConfig), for: .touchUpInside)
        return button
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadCurrentConfig()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 确保数据同步到磁盘
        UserDefaults.standard.synchronize()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        navigationTitle = "自定义配置"
        view.backgroundColor = .f9Color
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        setupAPISection()
        setupGameAPISection()
        setupH5Section()
        setupSaveButton()
    }
    
    private func setupAPISection() {
        contentView.addSubview(apiSectionLabel)
        contentView.addSubview(apiHostLabel)
        contentView.addSubview(apiHostTextField)
        contentView.addSubview(apiPortLabel)
        contentView.addSubview(apiPortTextField)
        contentView.addSubview(apiHistoryButton)
        
        apiSectionLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.equalToSuperview().offset(16)
        }
        
        apiHostLabel.snp.makeConstraints { make in
            make.top.equalTo(apiSectionLabel.snp.bottom).offset(20)
            make.left.equalToSuperview().offset(16)
        }
        
        apiHostTextField.snp.makeConstraints { make in
            make.top.equalTo(apiHostLabel.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalTo(apiHistoryButton.snp.left).offset(-12)
            make.height.equalTo(44)
        }
        
        apiHistoryButton.snp.makeConstraints { make in
            make.centerY.equalTo(apiHostTextField)
            make.right.equalToSuperview().offset(-16)
            make.width.equalTo(80)
            make.height.equalTo(36)
        }
        
        apiPortLabel.snp.makeConstraints { make in
            make.top.equalTo(apiHostTextField.snp.bottom).offset(16)
            make.left.equalToSuperview().offset(16)
        }
        
        apiPortTextField.snp.makeConstraints { make in
            make.top.equalTo(apiPortLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
    }
    
    private func setupGameAPISection() {
        contentView.addSubview(gameApiSectionLabel)
        contentView.addSubview(gameApiHostLabel)
        contentView.addSubview(gameApiHostTextField)
        contentView.addSubview(gameApiPortLabel)
        contentView.addSubview(gameApiPortTextField)
        contentView.addSubview(gameApiHistoryButton)

        gameApiSectionLabel.snp.makeConstraints { make in
            make.top.equalTo(apiPortTextField.snp.bottom).offset(40)
            make.left.equalToSuperview().offset(16)
        }

        gameApiHostLabel.snp.makeConstraints { make in
            make.top.equalTo(gameApiSectionLabel.snp.bottom).offset(20)
            make.left.equalToSuperview().offset(16)
        }

        gameApiHostTextField.snp.makeConstraints { make in
            make.top.equalTo(gameApiHostLabel.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalTo(gameApiHistoryButton.snp.left).offset(-12)
            make.height.equalTo(44)
        }

        gameApiHistoryButton.snp.makeConstraints { make in
            make.centerY.equalTo(gameApiHostTextField)
            make.right.equalToSuperview().offset(-16)
            make.width.equalTo(80)
            make.height.equalTo(36)
        }

        gameApiPortLabel.snp.makeConstraints { make in
            make.top.equalTo(gameApiHostTextField.snp.bottom).offset(16)
            make.left.equalToSuperview().offset(16)
        }

        gameApiPortTextField.snp.makeConstraints { make in
            make.top.equalTo(gameApiPortLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
    }

    private func setupH5Section() {
        contentView.addSubview(h5SectionLabel)
        contentView.addSubview(h5HostLabel)
        contentView.addSubview(h5HostTextField)
        contentView.addSubview(h5PortLabel)
        contentView.addSubview(h5PortTextField)
        contentView.addSubview(h5HistoryButton)

        h5SectionLabel.snp.makeConstraints { make in
            make.top.equalTo(gameApiPortTextField.snp.bottom).offset(40)
            make.left.equalToSuperview().offset(16)
        }
        
        h5HostLabel.snp.makeConstraints { make in
            make.top.equalTo(h5SectionLabel.snp.bottom).offset(20)
            make.left.equalToSuperview().offset(16)
        }
        
        h5HostTextField.snp.makeConstraints { make in
            make.top.equalTo(h5HostLabel.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.right.equalTo(h5HistoryButton.snp.left).offset(-12)
            make.height.equalTo(44)
        }
        
        h5HistoryButton.snp.makeConstraints { make in
            make.centerY.equalTo(h5HostTextField)
            make.right.equalToSuperview().offset(-16)
            make.width.equalTo(80)
            make.height.equalTo(36)
        }
        
        h5PortLabel.snp.makeConstraints { make in
            make.top.equalTo(h5HostTextField.snp.bottom).offset(16)
            make.left.equalToSuperview().offset(16)
        }
        
        h5PortTextField.snp.makeConstraints { make in
            make.top.equalTo(h5PortLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
    }
    
    private func setupSaveButton() {
        contentView.addSubview(saveButton)
        
        saveButton.snp.makeConstraints { make in
            make.top.equalTo(h5PortTextField.snp.bottom).offset(40)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
            make.bottom.equalToSuperview().offset(-30)
        }
    }
    
    private func loadCurrentConfig() {
        // 加载当前API配置
        let currentAPI = EnvManager.shared.apiURL
        if let url = URL(string: currentAPI) {
            apiHostTextField.text = url.host
            if let port = url.port {
                apiPortTextField.text = "\(port)"
            }
        }

        // 加载当前游戏API配置
        let currentGameAPI = EnvManager.shared.gameApiURL
        if let url = URL(string: currentGameAPI) {
            gameApiHostTextField.text = url.host
            if let port = url.port {
                gameApiPortTextField.text = "\(port)"
            }
        }

        // 加载当前H5配置
        let currentH5 = EnvManager.shared.webApiURL
        if let url = URL(string: currentH5) {
            h5HostTextField.text = url.host
            if let port = url.port {
                h5PortTextField.text = "\(port)"
            }
        }
    }

    // MARK: - Actions
    @objc private func showAPIHistory() {
        let historySheet = APIHistorySheetView(type: .api)
        historySheet.delegate = self
        historySheet.present(in: nil)
    }

    @objc private func showGameAPIHistory() {
        let historySheet = APIHistorySheetView(type: .gameApi)
        historySheet.delegate = self
        historySheet.present(in: nil)
    }

    @objc private func showH5History() {
        let historySheet = APIHistorySheetView(type: .h5)
        historySheet.delegate = self
        historySheet.present(in: nil)
    }

    @objc private func saveConfig() {
        guard let apiHost = apiHostTextField.text, !apiHost.isEmpty else {
            ProgressHUDManager.showTextMessage("请输入API域名/IP")
            return
        }

        guard let gameApiHost = gameApiHostTextField.text, !gameApiHost.isEmpty else {
            ProgressHUDManager.showTextMessage("请输入游戏API域名/IP")
            return
        }

        guard let h5Host = h5HostTextField.text, !h5Host.isEmpty else {
            ProgressHUDManager.showTextMessage("请输入H5域名/IP")
            return
        }

        let apiPort = apiPortTextField.text?.isEmpty == false ? apiPortTextField.text : nil
        let gameApiPort = gameApiPortTextField.text?.isEmpty == false ? gameApiPortTextField.text : nil
        let h5Port = h5PortTextField.text?.isEmpty == false ? h5PortTextField.text : nil

        let apiConfig = APIConfig(host: apiHost, port: apiPort, description: "自定义API")
        let gameApiConfig = GameAPIConfig(host: gameApiHost, port: gameApiPort, description: "自定义游戏API")
        let h5Config = H5Config(host: h5Host, port: h5Port, description: "自定义H5")

        // 检查API地址是否有变化
        let currentAPI = EnvManager.shared.apiURL
        let newAPI = apiConfig.fullURL
        let apiChanged = currentAPI != newAPI

        var message = "确定要保存并应用这些配置吗？\n\nAPI: \(apiConfig.fullURL)\n游戏API: \(gameApiConfig.fullURL)\nH5: \(h5Config.fullURL)"

        if apiChanged {
            message += "\n\n⚠️ API地址有变化，保存后应用将重启"
            message += "\n📱 配置将立即写入磁盘并生效"
        } else {
            message += "\n\n✅ 游戏API和H5地址将实时生效"
            message += "\n💾 配置将保存到本地存储"
        }

        let alert = UIAlertController(title: "保存配置", message: message, preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            // 确保所有配置都保存到UserDefaults并同步
            EnvManager.shared.saveCustomAPIConfig(apiConfig)
            EnvManager.shared.saveCustomGameAPIConfig(gameApiConfig)
            EnvManager.shared.saveCustomH5Config(h5Config)
            
            // 强制同步UserDefaults到磁盘，防止杀死APP时数据丢失
            UserDefaults.standard.synchronize()

            if apiChanged {
                // API地址有变化，需要重启
                // 延迟一点确保数据已写入磁盘
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    LWUserManger.shared.logoutUser()
                    EnvManager.shared.saveEnvironment(.custom)
                }
            } else {
                // API地址没变化，只保存配置不重启
                ProgressHUDManager.showTextMessage("配置已保存并生效")
                self.navigationController?.popViewController(animated: true)
            }
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))

        present(alert, animated: true)
    }
}

// MARK: - APIHistoryDelegate
extension CustomConfigVC: APIHistoryDelegate {
    
    func didSelectAPIConfig(_ config: APIConfig) {
        apiHostTextField.text = config.host
        apiPortTextField.text = config.port
    }
    
    func didSelectGameAPIConfig(_ config: GameAPIConfig) {
        gameApiHostTextField.text = config.host
        gameApiPortTextField.text = config.port
    }
    
    func didSelectH5Config(_ config: H5Config) {
        h5HostTextField.text = config.host
        h5PortTextField.text = config.port
    }
}
