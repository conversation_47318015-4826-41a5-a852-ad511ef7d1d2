//
//  MainTabBarVC.swift
//  YINDONG
//
//  Created by tyu<PERSON> on 2024/11/16.
//

import UIKit
import EachNavigationBar

class MainTabBarVC: UITabBarController {
    
    let disposeBag = DisposeBag()
    
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // Do any additional setup after loading the view.
        
        setupViewControllers()
        
        Async.main(after: 0.1) {
            LWUserManger.appInfoUpload()
            LWUserManger.shared.getFeature()
            LWUserManger.shared.getSignTrtc()
            AppConfigManger.shared.getRoomConfig()
            LWUserManger.shared.getUserNob()
            LWUserManger.shared.getUserBaseCount()
            LWUserManger.shared.getUserDis()
            
//            let jsong = """
//    {"stageTwoUser":{"rewardList":[{"giftName":"吃我一棒","giftQuietUrl":"https://yiyouxinxi.oss-cn-beijing.aliyuncs.com/social/gift/20250424/9BLt0P2NYXhG7EAF.png","giftNum":"x1"}],"hurtValue":92250,"nickName":"娟巧叔荃在我旁边","photoUrl":"https://houtaiproject.oss-cn-shenzhen.aliyuncs.com/socialTest/community/avatar/80001611683268_1744008928584.jpg"},"stageOneUser":{"photoUrl":"https://houtaiproject.oss-cn-shenzhen.aliyuncs.com/socialTest/community/avatar/80001611683268_1744008928584.jpg","rewardList":[{"giftName":"吃我一棒","giftQuietUrl":"https://yiyouxinxi.oss-cn-beijing.aliyuncs.com/social/gift/20250424/9BLt0P2NYXhG7EAF.png","giftNum":"x1"}],"hurtValue":92250,"nickName":"娟巧叔荃在我旁边"},"oneRewardGiftList":[{"giftName":"吃我一棒","giftQuietUrl":"https://yiyouxinxi.oss-cn-beijing.aliyuncs.com/social/gift/20250424/9BLt0P2NYXhG7EAF.png","giftNum":"x1"},{"giftName":"屠龙战神","giftQuietUrl":"https://yiyouxinxi.oss-cn-beijing.aliyuncs.com/social/decorate/20250429/w0e76EYtqHm39C68.png","giftNum":"x1天"}],"firstList":[{"photoUrl":"https://houtaiproject.oss-cn-shenzhen.aliyuncs.com/socialTest/community/avatar/80001611683268_1744008928584.jpg","userNumber":1971827769,"hurtValue":92250,"userId":4534677,"imNumber":80001611683268,"nickName":"娟巧叔荃在我旁边"}]}
//    """
//            if let a = TuLongSettlementModel.deserialize(from: jsong) {
//                let vv = TulongSettlementPopView()
//                vv.settlementData = a
//                vv.show()
//            }
        }
        
        tabBar.backgroundColor = .white
        
        
        NotificationCenter.default.rx.notification(.tabUnreadNotification).subscribe(onNext: { [weak self] notification in
            guard let self = self else { return }
            guard var index = notification.object as? Int else { return }
            
            if index == 3 {
                if T_IMHelper.shared.totalCount == 0 {
                    self.viewControllers?[2].tabBarItem.badgeValue = nil
                } else {
                    self.viewControllers?[2].tabBarItem.badgeValue = "\(T_IMHelper.shared.totalCount)"
                }
            }
            
            if index == 1 {
                if T_IMHelper.shared.noticeCount == 0 {
                    self.viewControllers?[1].tabBarItem.badgeValue = nil
                } else {
                    self.viewControllers?[1].tabBarItem.badgeValue = "\(T_IMHelper.shared.noticeCount)"
                }
            }
            
        }).disposed(by: disposeBag)
        
        
        
    }
    
    // 创建导航控制器并配置 TabBar 属性
    private func createNavController(rootViewController: UIViewController, title: String, normalImage: String, selectedImage: String) -> NavigationVC {
        let navController = NavigationVC(rootViewController: rootViewController)
        navController.tabBarItem = UITabBarItem(title: title, image: UIImage(named: normalImage), selectedImage: UIImage(named: selectedImage))
        
        // 配置字体
        //        navController.tabBarItem.setTitleTextAttributes([.font: UIFont.regular(11)], for: .normal)
        //        navController.tabBarItem.setTitleTextAttributes([.font: UIFont.bold(11)], for: .selected)
        
        return navController
    }
    
    
    func setupViewControllers() {
        // 使用统一的方法创建各个 tab 页面
        let drawingNavController = createNavController(
            rootViewController: MusicActListVC(),
            title: "首页",
            normalImage: "icontab首页n",
            selectedImage: "icontab首页y"
        )
        
        let homeNavVC = createNavController(
            rootViewController: HomeRoomListVC(),
            title: "首页",
            normalImage: "icontab首页n",
            selectedImage: "icontab首页y"
        )
        
        let dyNavVC = createNavController(
            rootViewController: DynamicMainVC(),
            title: "社区",
            normalImage: "icontab社区n",
            selectedImage: "icontab社区y"
        )
        
        let bookingNavController = createNavController(
            rootViewController: TwoMainViewController(),
            title: "听曲",
            normalImage: "icontab社区n",
            selectedImage: "icontab社区y"
        )
        
        let syteamNavController = createNavController(
            rootViewController: LwChatMainVC(),
            title: "消息",
            normalImage: "icontab消息n",
            selectedImage: "icontab消息y"
        )
        
        let profileNavController = createNavController(
            rootViewController: MineVC(),
            title: "我的",
            normalImage: "icontab我的n",
            selectedImage: "icontab我的y"
        )
        
        let profileNavController1 = createNavController(
            rootViewController: MineUserVC(),
            title: "我的",
            normalImage: "icontab我的n",
            selectedImage: "icontab我的y"
        )
        
        
        
        // 设置控制器数组
        viewControllers = appCodelw ?
        [drawingNavController, bookingNavController, syteamNavController, profileNavController] :
        [homeNavVC, dyNavVC, syteamNavController, profileNavController1]
        
        // 设置 TabBar 样式
        configureTabBarAppearance()
    }
    
    // 配置 TabBar 外观
    private func configureTabBarAppearance() {
        
        let appearance = UITabBarAppearance()
        // 用这个清掉默认背景和分隔线
         appearance.configureWithOpaqueBackground()
         appearance.backgroundColor = .white

         // 移除顶部那条分隔线
         appearance.shadowImage = nil
         appearance.shadowColor = .clear
        
        // 普通态字体
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .font: UIFont.regular(11),
            .foregroundColor: UIColor.init(hex: 0xBFC0CA) ?? .white
        ]
        // 选中态字体
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .font: UIFont.bold(11),
            .foregroundColor: UIColor.init(hex: 0x363853) ?? .white
        ]
        
        tabBar.standardAppearance = appearance
        // 如果你支持 iOS 15+，还需要设置 scrollEdgeAppearance
        if #available(iOS 15.0, *) {
            tabBar.scrollEdgeAppearance = appearance
        }
        
    }
}



class NavigationVC: UINavigationController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        navigation.configuration.titleTextAttributes = [.foregroundColor: UIColor.black, .font: UIFont.medium(18)]
        navigation.configuration.shadowImage = UIImage.imageWithColor(color: .clear)
        navigation.configuration.setBackgroundImage(UIImage.imageWithColor(color: .white), for: .any, barMetrics: .default)
        navigation.configuration.isEnabled = true
        navigation.configuration.barTintColor = UIColor.black
        navigation.configuration.tintColor = UIColor.black
    }
    
    override func pushViewController(_ viewController: UIViewController, animated: Bool) {
        if viewControllers.count > 0 {
            viewController.hidesBottomBarWhenPushed = true
        }
        super.pushViewController(viewController, animated: animated)
    }
    
}
