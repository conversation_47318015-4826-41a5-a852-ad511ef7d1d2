//
//  TextMsgCell.swift
//  YINDONG
//
//  Created by jj on 2025/3/25.
//

import UIKit

class TextMsgCell: MsgBaseCell {

    // MARK: - UI Elements
    private let messageLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(14)
        label.textColor = .textColor3
        label.numberOfLines = 0
        return label
    }()
    

    
    // MARK: - UI Setup
    override func setupUI() {
        super.setupUI()
        bubbleView.addSubview(messageLabel)
        
        messageLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(15)
            make.width.lessThanOrEqualTo(230)
        }
    }
    
    override func updateLayout() {
        super.updateLayout()
        
        bubbleView.snp.remakeConstraints { make in
            make.top.equalTo(avatarImageView)
            if isFromMe {
                make.right.equalTo(avatarImageView.snp.left).offset(-10)
            } else {
                make.left.equalTo(avatarImageView.snp.right).offset(10)
            }
            make.bottom.equalTo(-8)
        }
    }
    
    override func configModel(_ model: MsgBaseModel?) {
        super.configModel(model)
        if model?.messageType == .text {
            guard let text = model?.msg?.textElem?.text else { return }
            messageLabel.attributedText = EmojiManger.shared.replaceEmojiSymbols(in: text, font: .regular(14))
        }
        
        if model?.messageType == .friendAnswerReply {
            messageLabel.attributed.text = "\(model?.wdModel?.text ?? "") \(.image(UIImage(named: "chat_anwerIcon") ?? UIImage(), .original(.center)))"
        }
        
        if let color = model?.customData?.buddleFontColor {
            messageLabel.textColor = UIColor(argbHexString: color)
        } else {
            messageLabel.textColor = .textColor3
        }
        
        
        updateLayout()
    }


}
