//
//  MsgBaseCell.swift
//  YINDONG
//
//  Created by jj on 2025/3/25.
//

import UIKit
import Kingfisher

class MsgBaseCell: UITableViewCell {
    
    var messageModel: MsgBaseModel?
    
    func configModel(_ model: MsgBaseModel?) {
        guard let message = model else { return }
        self.messageModel = message
        timeLabel.isHidden = !message.showTime
        timeLabel.text = Date.chatDateString(from: Int(message.msg?.timestamp.timeIntervalSince1970 ?? 0))
        activityIndicatorView.stopAnimating()
        errorImageView.isHidden = true
//        avatarImageView.image = nil
        avatarImageView.avatarImageView.setImage(from: message.msg?.faceURL)
        
        avatarImageView.updateDecorationScale(multiplier: AvatarFrameView.defaultDecorationScale)
        
        if message.msg?.sender == kuser.imNumber.string {
            isFromMe = true
            //            bubbleView.backgroundColor = .init(hex: 0xF2C467)
            if model?.msg?.status == .V2TIM_MSG_STATUS_SENDING {
                activityIndicatorView.startAnimating()
                errorImageView.isHidden = true
            }
            
            if message.msg?.status == .V2TIM_MSG_STATUS_SEND_SUCC {
                activityIndicatorView.stopAnimating()
                errorImageView.isHidden = true
            }
            
            if message.msg?.status == .V2TIM_MSG_STATUS_SEND_FAIL {
                activityIndicatorView.stopAnimating()
                errorImageView.isHidden = false
            }
            
            avatarImageView.loadDecoration(from: UserDisguiseModel.load()?.getAvaterInfo()?.goodsUrlPreview ?? "")
            
        } else {
            isFromMe = false
            avatarImageView.loadDecoration(from: T_IMHelper.shared.toUserPre?.goodsUrlPreview ?? "")
        }
        
        //有气泡的情况，要么是语音 要么是文字
        if let a = message.customData, (model?.msg?.elemType == .V2TIM_ELEM_TYPE_SOUND || model?.msg?.elemType == .V2TIM_ELEM_TYPE_TEXT) {
            setBulee(mm: a)
            bubbleView.backgroundColor = .clear
        } else {
            bubbleView.backgroundColor = .white
            bubbleView.clear()
        }
        
        updateLayout()
        
    }
    
    func setBulee(mm: ChatBubbleModel) {
        
        if let url = mm.buddleUrl, isValid(url) {
            let downloader = ImageDownloader.default
            let options = KingfisherOptionsInfo([.transition(.fade(0.2)), .scaleFactor(UIScreen.main.scale)])
            downloader.downloadTimeout = 15
            downloader.downloadImage(with: URL(string: url)!, options: options) {[weak self] result in
                guard let `self` = self else { return }
                switch result {
                case .success(let value):
                    Async.main {
                        let img = value.image
                        let resizableImage = img.createResizableImageCentered()
                        self.bubbleView.bgImgV.image = resizableImage
                        self.bubbleView.bgImgV.backgroundColor = .clear
                    }
                case .failure(let error):
                    break
                }
            }
        }
        bubbleView.play(withMinXMinY: mm.buddleTL, minXMaxY: mm.buddleBR, maxXminY: mm.buddleTR, maxXMaxY: mm.buddleBR)
        
    }
    
    
    // MARK: - UI Elements
    let avatarImageView: AvatarFrameView = {
        let imageView = AvatarFrameView()
        imageView.backgroundColor = .f9Color
        imageView.avatarImageView.layerCornerRadius = 21
        imageView.isUserInteractionEnabled = true
        return imageView
    }()
    
    let timeLabel: UILabel = {
        let label = UILabel()
        label.textColor = .gray
        label.font = .regular(12)
        label.textAlignment = .center
        return label
    }()
    
    let bubbleView: BaseChatBgView = {
        let view = BaseChatBgView()
        view.layer.cornerRadius = 12
        view.backgroundColor = .white
        return view
    }()
    
    // MARK: - Properties
    var isFromMe: Bool = false {
        didSet {
            updateLayout()
        }
    }
    
    lazy var activityIndicatorView: UIActivityIndicatorView = {
        let indicatorView = UIActivityIndicatorView(style: UIActivityIndicatorView.Style.medium)
        indicatorView.color = .themColor // 设置为白色
        indicatorView.hidesWhenStopped = true
        return indicatorView
    }()
    
    lazy var errorImageView: InfoItemBaseView = {
        let imageView = InfoItemBaseView(rightImg: "icon_public_again", leftTitle: "发送失败", isLeft: true, space: 2, imgWH: CGSize(width: 12, height: 12))
        imageView.titleLab.font = .medium(9)
        imageView.titleLab.textColor = .init(hex: 0xA09D9C)
        imageView.isHidden = true
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
     func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(timeLabel)
        contentView.addSubview(avatarImageView)
        contentView.addSubview(bubbleView)
        contentView.addSubview(activityIndicatorView)
        contentView.addSubview(errorImageView)
        bubbleView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapAction)))
        
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.centerX.equalToSuperview()
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(30)
            make.left.equalTo(12)
            make.width.height.equalTo(42)
        }
        
        activityIndicatorView.snp.makeConstraints { make in
            make.centerY.equalTo(bubbleView)
            make.right.equalTo(bubbleView.snp.left).offset(-10)
            make.width.height.equalTo(10)
        }
        
        errorImageView.snp.makeConstraints { make in
            make.centerY.equalTo(bubbleView)
            make.right.equalTo(activityIndicatorView)
        }
         
         avatarImageView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(goUser)))
    }
    
    @objc
    func goUser() {
        if messageModel?.msg?.sender?.int?.isIMMe == true { return }
        LWJumpManger.goUserPage(id: messageModel?.msg?.sender?.int)
    }
    
    func updateLayout() {
        avatarImageView.snp.remakeConstraints { make in
            if timeLabel.isHidden {
                make.top.equalTo(12)
            } else {
                make.top.equalTo(35)
            }
            make.width.height.equalTo(42)
            if isFromMe {
                make.right.equalTo(-12)
            } else {
                make.left.equalTo(12)
            }
        }
        
        layoutIfNeeded()
    }
    
    
    @objc
    func tapAction() {
        
    }
    
}
