//
//  MsgBaseCell.swift
//  YINDONG
//
//  Created by jj on 2025/3/25.
//

import UIKit
import Kingfisher

class MsgBaseCell: UITableViewCell {

    var messageModel: MsgBaseModel?
    private var currentImageDownloadTask: DownloadTask?
    private var cellIdentifier: String = ""

    // MARK: - Cell Lifecycle
    override func prepareForReuse() {
        super.prepareForReuse()

        // 取消正在进行的图片下载任务
        currentImageDownloadTask?.cancel()
        currentImageDownloadTask = nil

        // 重置 bubbleView 状态
        bubbleView.resetForReuse()

        // 重置其他状态
        activityIndicatorView.stopAnimating()
        errorImageView.isHidden = true
        timeLabel.isHidden = false

        // 生成新的标识符用于防止异步回调混乱
        cellIdentifier = UUID().uuidString
    }

    func configModel(_ model: MsgBaseModel?) {
        guard let message = model else { return }

        // 先取消之前的任务
        currentImageDownloadTask?.cancel()
        currentImageDownloadTask = nil

        self.messageModel = message
        let currentIdentifier = cellIdentifier

        // 配置基本信息
        configureBasicInfo(message: message)

        // 配置发送状态
        configureSendingStatus(message: message, model: model)

        // 配置头像装饰
        configureAvatarDecoration(message: message)

        // 配置气泡样式
        configureBubbleStyle(message: message, model: model, identifier: currentIdentifier)

        // 最后更新布局
        updateLayoutIfNeeded()
    }

    private func configureBasicInfo(message: MsgBaseModel) {
        timeLabel.isHidden = !message.showTime
        timeLabel.text = Date.chatDateString(from: Int(message.msg?.timestamp.timeIntervalSince1970 ?? 0))
        avatarImageView.avatarImageView.setImage(from: message.msg?.faceURL)
        avatarImageView.updateDecorationScale(multiplier: AvatarFrameView.defaultDecorationScale)
        isFromMe = message.msg?.sender == kuser.imNumber.string
    }

    private func configureSendingStatus(message: MsgBaseModel, model: MsgBaseModel?) {
        guard message.msg?.sender == kuser.imNumber.string else {
            activityIndicatorView.stopAnimating()
            errorImageView.isHidden = true
            return
        }

        switch model?.msg?.status {
        case .V2TIM_MSG_STATUS_SENDING:
            activityIndicatorView.startAnimating()
            errorImageView.isHidden = true
        case .V2TIM_MSG_STATUS_SEND_SUCC:
            activityIndicatorView.stopAnimating()
            errorImageView.isHidden = true
        case .V2TIM_MSG_STATUS_SEND_FAIL:
            activityIndicatorView.stopAnimating()
            errorImageView.isHidden = false
        default:
            activityIndicatorView.stopAnimating()
            errorImageView.isHidden = true
        }
    }

    private func configureAvatarDecoration(message: MsgBaseModel) {
        if message.msg?.sender == kuser.imNumber.string {
            avatarImageView.loadDecoration(from: UserDisguiseModel.load()?.getAvaterInfo()?.goodsUrlPreview ?? "")
        } else {
            avatarImageView.loadDecoration(from: T_IMHelper.shared.toUserPre?.goodsUrlPreview ?? "")
        }
    }

    private func configureBubbleStyle(message: MsgBaseModel, model: MsgBaseModel?, identifier: String) {
        // 有气泡的情况，要么是语音 要么是文字
        if let customData = message.customData,
           (model?.msg?.elemType == .V2TIM_ELEM_TYPE_SOUND || model?.msg?.elemType == .V2TIM_ELEM_TYPE_TEXT) {
            setBubble(with: customData, identifier: identifier)
            bubbleView.backgroundColor = .clear
        } else {
            bubbleView.backgroundColor = .white
            bubbleView.clear()
        }
    }

    private func updateLayoutIfNeeded() {
        // 只在必要时更新布局，避免频繁的约束重设
        if needsLayoutUpdate() {
            updateLayout()
        }
    }

    private func needsLayoutUpdate() -> Bool {
        // 检查是否需要更新布局（比如 isFromMe 状态改变）
        return true // 简化处理，实际可以根据具体情况优化
    }
    
    private func setBubble(with bubbleModel: ChatBubbleModel, identifier: String) {
        // 先设置动画，避免等待图片下载
        bubbleView.play(withMinXMinY: bubbleModel.buddleTL,
                       minXMaxY: bubbleModel.buddleBR,
                       maxXminY: bubbleModel.buddleTR,
                       maxXMaxY: bubbleModel.buddleBR)

        // 如果有背景图片URL，异步加载
        guard let url = bubbleModel.buddleUrl,
              isValid(url),
              let imageURL = URL(string: url) else {
            return
        }

        // 检查缓存中是否已有图片
        let cache = ImageCache.default
        let cacheKey = url

        if let cachedImage = cache.retrieveImageInMemoryCache(forKey: cacheKey) {
            // 直接使用缓存图片
            let resizableImage = cachedImage.createResizableImageCentered()
            bubbleView.bgImgV.image = resizableImage
            bubbleView.bgImgV.backgroundColor = .clear
            return
        }

        // 异步下载图片
        let downloader = ImageDownloader.default
        let options = KingfisherOptionsInfo([
            .scaleFactor(UIScreen.main.scale),
            .cacheOriginalImage // 缓存原始图片
        ])
        downloader.downloadTimeout = 15

        currentImageDownloadTask = downloader.downloadImage(with: imageURL, options: options) { [weak self] result in
            guard let self = self,
                  self.cellIdentifier == identifier else {
                // Cell 已被重用或释放，忽略回调
                return
            }

            switch result {
            case .success(let value):
                DispatchQueue.main.async {
                    // 再次检查标识符，确保 cell 没有被重用
                    guard self.cellIdentifier == identifier else { return }

                    let img = value.image
                    let resizableImage = img.createResizableImageCentered()

                    // 使用无动画的方式设置图片，避免闪烁
                    UIView.performWithoutAnimation {
                        self.bubbleView.bgImgV.image = resizableImage
                        self.bubbleView.bgImgV.backgroundColor = .clear
                    }
                }
            case .failure(let error):
                TLog("气泡背景图片下载失败: \(error.localizedDescription)")
            }
        }
    }
    
    
    // MARK: - UI Elements
    let avatarImageView: AvatarFrameView = {
        let imageView = AvatarFrameView()
        imageView.backgroundColor = .f9Color
        imageView.avatarImageView.layerCornerRadius = 21
        imageView.isUserInteractionEnabled = true
        return imageView
    }()
    
    let timeLabel: UILabel = {
        let label = UILabel()
        label.textColor = .gray
        label.font = .regular(12)
        label.textAlignment = .center
        return label
    }()
    
    let bubbleView: BaseChatBgView = {
        let view = BaseChatBgView()
        view.layer.cornerRadius = 12
        view.backgroundColor = .white
        return view
    }()
    
    // MARK: - Properties
    var isFromMe: Bool = false {
        didSet {
            updateLayout()
        }
    }
    
    lazy var activityIndicatorView: UIActivityIndicatorView = {
        let indicatorView = UIActivityIndicatorView(style: UIActivityIndicatorView.Style.medium)
        indicatorView.color = .themColor // 设置为白色
        indicatorView.hidesWhenStopped = true
        return indicatorView
    }()
    
    lazy var errorImageView: InfoItemBaseView = {
        let imageView = InfoItemBaseView(rightImg: "icon_public_again", leftTitle: "发送失败", isLeft: true, space: 2, imgWH: CGSize(width: 12, height: 12))
        imageView.titleLab.font = .medium(9)
        imageView.titleLab.textColor = .init(hex: 0xA09D9C)
        imageView.isHidden = true
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
     func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(timeLabel)
        contentView.addSubview(avatarImageView)
        contentView.addSubview(bubbleView)
        contentView.addSubview(activityIndicatorView)
        contentView.addSubview(errorImageView)
        bubbleView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapAction)))
        
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.centerX.equalToSuperview()
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(30)
            make.left.equalTo(12)
            make.width.height.equalTo(42)
        }
        
        activityIndicatorView.snp.makeConstraints { make in
            make.centerY.equalTo(bubbleView)
            make.right.equalTo(bubbleView.snp.left).offset(-10)
            make.width.height.equalTo(10)
        }
        
        errorImageView.snp.makeConstraints { make in
            make.centerY.equalTo(bubbleView)
            make.right.equalTo(activityIndicatorView)
        }
         
         avatarImageView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(goUser)))
    }
    
    @objc
    func goUser() {
        if messageModel?.msg?.sender?.int?.isIMMe == true { return }
        LWJumpManger.goUserPage(id: messageModel?.msg?.sender?.int)
    }
    
    func updateLayout() {
        avatarImageView.snp.remakeConstraints { make in
            if timeLabel.isHidden {
                make.top.equalTo(12)
            } else {
                make.top.equalTo(35)
            }
            make.width.height.equalTo(42)
            if isFromMe {
                make.right.equalTo(-12)
            } else {
                make.left.equalTo(12)
            }
        }
        
        layoutIfNeeded()
    }
    
    
    @objc
    func tapAction() {
        
    }
    
}
