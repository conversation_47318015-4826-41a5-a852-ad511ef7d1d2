//
//  MsgBaseCell.swift
//  YINDONG
//
//  Created by jj on 2025/3/25.
//

import UIKit
import Kingfisher

class MsgBaseCell: UITableViewCell {

    var messageModel: MsgBaseModel?
    private var currentImageDownloadTask: DownloadTask?
    private var cellIdentifier: String = ""
    private var cachedHeight: CGFloat = 0
    private var lastConfiguredMessageId: String = ""

    // MARK: - Cell Lifecycle
    override func prepareForReuse() {
        super.prepareForReuse()

        // 取消正在进行的图片下载任务
        currentImageDownloadTask?.cancel()
        currentImageDownloadTask = nil

        // 重置 bubbleView 状态
        bubbleView.resetForReuse()

        // 重置其他状态
        activityIndicatorView.stopAnimating()
        errorImageView.isHidden = true
        timeLabel.isHidden = false

        // 重置缓存信息
        cachedHeight = 0
        lastConfiguredMessageId = ""

        // 生成新的标识符用于防止异步回调混乱
        cellIdentifier = UUID().uuidString
    }

    func configModel(_ model: MsgBaseModel?) {
        guard let message = model else { return }

        // 先取消之前的任务
        currentImageDownloadTask?.cancel()
        currentImageDownloadTask = nil

        self.messageModel = message
        let currentIdentifier = cellIdentifier

        // 配置基本信息
        configureBasicInfo(message: message)

        // 配置发送状态
        configureSendingStatus(message: message, model: model)

        // 配置头像装饰
        configureAvatarDecoration(message: message)

        // 配置气泡样式
        configureBubbleStyle(message: message, model: model, identifier: currentIdentifier)

        // 最后更新布局
        updateLayoutIfNeeded()
    }

    private func configureBasicInfo(message: MsgBaseModel) {
        timeLabel.isHidden = !message.showTime
        timeLabel.text = Date.chatDateString(from: Int(message.msg?.timestamp.timeIntervalSince1970 ?? 0))
        avatarImageView.avatarImageView.setImage(from: message.msg?.faceURL)
        avatarImageView.updateDecorationScale(multiplier: AvatarFrameView.defaultDecorationScale)
        isFromMe = message.msg?.sender == kuser.imNumber.string
    }

    private func configureSendingStatus(message: MsgBaseModel, model: MsgBaseModel?) {
        guard message.msg?.sender == kuser.imNumber.string else {
            activityIndicatorView.stopAnimating()
            errorImageView.isHidden = true
            return
        }

        switch model?.msg?.status {
        case .V2TIM_MSG_STATUS_SENDING:
            activityIndicatorView.startAnimating()
            errorImageView.isHidden = true
        case .V2TIM_MSG_STATUS_SEND_SUCC:
            activityIndicatorView.stopAnimating()
            errorImageView.isHidden = true
        case .V2TIM_MSG_STATUS_SEND_FAIL:
            activityIndicatorView.stopAnimating()
            errorImageView.isHidden = false
        default:
            activityIndicatorView.stopAnimating()
            errorImageView.isHidden = true
        }
    }

    private func configureAvatarDecoration(message: MsgBaseModel) {
        if message.msg?.sender == kuser.imNumber.string {
            avatarImageView.loadDecoration(from: UserDisguiseModel.load()?.getAvaterInfo()?.goodsUrlPreview ?? "")
        } else {
            avatarImageView.loadDecoration(from: T_IMHelper.shared.toUserPre?.goodsUrlPreview ?? "")
        }
    }

    private func configureBubbleStyle(message: MsgBaseModel, model: MsgBaseModel?, identifier: String) {
        // 有气泡的情况，要么是语音 要么是文字
        if let customData = message.customData,
           (model?.msg?.elemType == .V2TIM_ELEM_TYPE_SOUND || model?.msg?.elemType == .V2TIM_ELEM_TYPE_TEXT) {
            setBubble(with: customData, identifier: identifier)
            bubbleView.backgroundColor = .clear
        } else {
            bubbleView.backgroundColor = .white
            bubbleView.clear()
        }
    }

    private func updateLayoutIfNeeded() {
        guard let message = messageModel else { return }

        // 检查是否是相同消息，避免重复布局
        let messageId = message.msg?.msgID ?? ""
        if lastConfiguredMessageId == messageId && cachedHeight > 0 {
            return
        }

        // 使用 CATransaction 禁用隐式动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)

        updateLayout()

        // 缓存配置信息
        lastConfiguredMessageId = messageId
        cachedHeight = frame.height

        CATransaction.commit()
    }

    private func needsLayoutUpdate() -> Bool {
        guard let message = messageModel else { return true }
        let messageId = message.msg?.msgID ?? ""
        return lastConfiguredMessageId != messageId
    }
    
    private func setBubble(with bubbleModel: ChatBubbleModel, identifier: String)  {
        // 先设置动画，避免等待图片下载
        bubbleView.play(withMinXMinY: bubbleModel.buddleTL,
                       minXMaxY: bubbleModel.buddleBR,
                       maxXminY: bubbleModel.buddleTR,
                       maxXMaxY: bubbleModel.buddleBR)

        // 如果有背景图片URL，加载图片
        guard let url = bubbleModel.buddleUrl,
              isValid(url),
              let imageURL = URL(string: url) else {
            return
        }

        // 检查内存缓存 - 同步获取
        let cache = ImageCache.default
        let cacheKey = url

        // 先检查内存缓存
        if let cachedImage = cache.retrieveImageInMemoryCache(forKey: cacheKey) {
            // 同步设置缓存图片，完全避免异步
            let resizableImage = cachedImage.createResizableImageCentered()
            CATransaction.begin()
            CATransaction.setDisableActions(true)
            bubbleView.bgImgV.image = resizableImage
            bubbleView.bgImgV.backgroundColor = .clear
            CATransaction.commit()
            return
        }

        

            

        // 只有在没有任何缓存时才异步下载
        let downloader = ImageDownloader.default
        let options = KingfisherOptionsInfo([
            .scaleFactor(UIScreen.main.scale),
            .cacheOriginalImage,
            .transition(.none) // 禁用过渡动画
        ])
        downloader.downloadTimeout = 15

        currentImageDownloadTask = downloader.downloadImage(with: imageURL, options: options) { [weak self] result in
            guard let self = self,
                  self.cellIdentifier == identifier else {
                return
            }

            switch result {
            case .success(let value):
                // 使用 CATransaction 彻底禁用动画
                CATransaction.begin()
                CATransaction.setDisableActions(true)
                CATransaction.setCompletionBlock {
                    // 确保在主线程完成
                }

                DispatchQueue.main.async {
                    guard self.cellIdentifier == identifier else { return }

                    let img = value.image
                    let resizableImage = img.createResizableImageCentered()

                    self.bubbleView.bgImgV.image = resizableImage
                    self.bubbleView.bgImgV.backgroundColor = .clear
                }

                CATransaction.commit()

            case .failure(let error):
                TLog("气泡背景图片下载失败: \(error.localizedDescription)")
            }
        }
    }
    
    
    // MARK: - UI Elements
    let avatarImageView: AvatarFrameView = {
        let imageView = AvatarFrameView()
        imageView.backgroundColor = .f9Color
        imageView.avatarImageView.layerCornerRadius = 21
        imageView.isUserInteractionEnabled = true
        return imageView
    }()
    
    let timeLabel: UILabel = {
        let label = UILabel()
        label.textColor = .gray
        label.font = .regular(12)
        label.textAlignment = .center
        return label
    }()
    
    let bubbleView: BaseChatBgView = {
        let view = BaseChatBgView()
        view.layer.cornerRadius = 12
        view.backgroundColor = .white
        return view
    }()
    
    // MARK: - Properties
    var isFromMe: Bool = false {
        didSet {
            updateLayout()
        }
    }
    
    lazy var activityIndicatorView: UIActivityIndicatorView = {
        let indicatorView = UIActivityIndicatorView(style: UIActivityIndicatorView.Style.medium)
        indicatorView.color = .themColor // 设置为白色
        indicatorView.hidesWhenStopped = true
        return indicatorView
    }()
    
    lazy var errorImageView: InfoItemBaseView = {
        let imageView = InfoItemBaseView(rightImg: "icon_public_again", leftTitle: "发送失败", isLeft: true, space: 2, imgWH: CGSize(width: 12, height: 12))
        imageView.titleLab.font = .medium(9)
        imageView.titleLab.textColor = .init(hex: 0xA09D9C)
        imageView.isHidden = true
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
     func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(timeLabel)
        contentView.addSubview(avatarImageView)
        contentView.addSubview(bubbleView)
        contentView.addSubview(activityIndicatorView)
        contentView.addSubview(errorImageView)
        bubbleView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapAction)))
        
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(8)
            make.centerX.equalToSuperview()
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(30)
            make.left.equalTo(12)
            make.width.height.equalTo(42)
        }
        
        activityIndicatorView.snp.makeConstraints { make in
            make.centerY.equalTo(bubbleView)
            make.right.equalTo(bubbleView.snp.left).offset(-10)
            make.width.height.equalTo(10)
        }
        
        errorImageView.snp.makeConstraints { make in
            make.centerY.equalTo(bubbleView)
            make.right.equalTo(activityIndicatorView)
        }
         
         avatarImageView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(goUser)))
    }
    
    @objc
    func goUser() {
        if messageModel?.msg?.sender?.int?.isIMMe == true { return }
        LWJumpManger.goUserPage(id: messageModel?.msg?.sender?.int)
    }
    
    func updateLayout() {
        // 避免不必要的约束重设
        let topOffset: CGFloat = timeLabel.isHidden ? 12 : 35
        let horizontalOffset: CGFloat = isFromMe ? -12 : 12

        // 只在约束真正需要改变时才重设
        if avatarImageView.frame.origin.y != topOffset ||
            (isFromMe && avatarImageView.frame.maxX != contentView.frame.width - 12) ||
           (!isFromMe && avatarImageView.frame.minX != 12) {

            avatarImageView.snp.remakeConstraints { make in
                make.top.equalTo(topOffset)
                make.width.height.equalTo(42)
                if isFromMe {
                    make.right.equalTo(-12)
                } else {
                    make.left.equalTo(12)
                }
            }
        }

        // 强制立即布局，避免延迟布局导致的闪烁
        setNeedsLayout()
        layoutIfNeeded()
    }
    
    
    @objc
    func tapAction() {
        
    }
    
}
