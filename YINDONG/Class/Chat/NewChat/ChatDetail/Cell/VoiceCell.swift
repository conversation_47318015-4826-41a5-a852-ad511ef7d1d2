//
//  VoiceCell.swift
//  YINDONG
//
//  Created by jj on 2025/4/2.
//

import UIKit

class VoiceCell: MsgBaseCell {
    
    var playAudioCallback:((_ path:String?, _ model: MsgBaseModel?)->Void)?
    
    lazy var voiceGIFView: UIImageView = {
        let view = UIImageView()
        view.contentMode = .scaleAspectFill
        view.snp.makeConstraints({ (make) in
            make.size.equalTo(CGSize(width: 20, height: 20))
        })
        return view
    }()
    
    var allD: Int = 0
    
    lazy var secondLabel: UILabel = {
        let view = UILabel()
        view.textColor = .black
        view.font = .regular(13)
        view.textAlignment = .center
        return view
    }()
    
    lazy var stackView: UIStackView = {
        let view = UIStackView()
        view.axis = .horizontal
        view.alignment = .center
        view.spacing = 3
        return view
    }()
    
    lazy var redPintView: UIView = {
        let view = UIView(frame: .zero)
        view.backgroundColor = .red
        view.layerCornerRadius = 3
        view.isHidden = true
        return view
    }()
    
    
    override func setupUI() {
        super.setupUI()
        bubbleView.backgroundColor = .white
        bubbleView.addSubview(stackView)
        stackView.addArrangedSubview(voiceGIFView)
        stackView.addArrangedSubview(secondLabel)
        contentView.addSubview(redPintView)
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(playAudio))
        bubbleView.addGestureRecognizer(tapGesture)
        bubbleView.isUserInteractionEnabled = true
        
        
        self.stackView.snp.remakeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(horizontal: 30, vertical: 20))
        }
                
        redPintView.snp.remakeConstraints { make in
            make.left.equalTo(bubbleView.snp.right).inset(-5)
            make.centerY.equalTo(bubbleView.snp.centerY)
            make.size.equalTo(CGSize(width: 6, height: 6))
        }
        
        
        
    }
    
    @objc
    func playAudio() {
        
        let gifs = isFromMe ? ["me_icon01","me_icon02","me_icon03"] : ["other_icon01","other_icon02","other_icon03"]
        
        voiceGIFView.animationImages = gifs.compactMap({ UIImage(named: $0) })
        voiceGIFView.animationDuration = 1.0 // 动画持续1秒
        voiceGIFView.animationRepeatCount = 100 // 重复3次 (总共播放4次)
        voiceGIFView.startAnimating()
        
        self.messageModel?.msg?.soundElem?.getUrl(callback: { [weak self] url in
            self?.playAudioCallback?(url, self?.messageModel)
            self?.redPintView.isHidden = true
        })
        
    }
    
    func stopAnim() {
        voiceGIFView.stopAnimating()
        secondLabel.text = "\(allD)''"
    }
    
    override func updateLayout() {
        super.updateLayout()
        
        bubbleView.snp.remakeConstraints { make in
            make.top.equalTo(avatarImageView)
            if isFromMe {
                make.right.equalTo(avatarImageView.snp.left).offset(-10)
            } else {
                make.left.equalTo(avatarImageView.snp.right).offset(10)
            }
            make.height.equalTo(44)
            make.bottom.equalTo(-8)
        }
    }
    
    override func configModel(_ model: MsgBaseModel?) {
        super.configModel(model)
        
        guard let msg = model?.msg, let soundElem = msg.soundElem else { return }
        
        secondLabel.text = "\(soundElem.duration)''"
        self.allD = soundElem.duration
        if msg.isSelf {
            stackView.insertArrangedSubview(voiceGIFView, at: 0)
            voiceGIFView.image = UIImage(named: "me_icon03")
            redPintView.isHidden = true
        } else {
            stackView.insertArrangedSubview(secondLabel, at: 0)
            voiceGIFView.tintColor = .textColor6
            voiceGIFView.image = UIImage(named: "other_icon03")?.withRenderingMode(.alwaysTemplate)
            if msg.localCustomInt == 0 {
                redPintView.isHidden = false
            }else{
                redPintView.isHidden = true
            }
        }
        
        
        
        if let color = model?.customData?.buddleFontColor {
            secondLabel.textColor = UIColor(argbHexString: color)
        } else {
            secondLabel.textColor = .textColor3
        }
        
    }
}
