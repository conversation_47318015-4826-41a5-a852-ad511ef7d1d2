//
//  ImageMsgCell.swift
//  YINDONG
//
//  Created by jj on 2025/3/25.
//

import UIKit

class ImageMsgCell: MsgBaseCell {

    // MARK: - UI Elements
    private let messageImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 12
        return imageView
    }()

    private var originalImage: V2TIMImage?
    private var thumbnailImage: V2TIMImage?
    private var currentImagePath: String?

    // MARK: - Cell Lifecycle
    override func prepareForReuse() {
        super.prepareForReuse()
        messageImageView.image = nil
        messageImageView.kf.cancelDownloadTask()
        originalImage = nil
        thumbnailImage = nil
        currentImagePath = nil
    }

    // MARK: - UI Setup
    override func setupUI() {
        super.setupUI()
        bubbleView.addSubview(messageImageView)
    }
    
    override func updateLayout() {
        super.updateLayout()
        
        // Calculate image size
        let size = messageModel?.calculateImageSize(imageElem: messageModel?.msg?.imageElem) ?? .zero
        
        messageImageView.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(size.width)
            make.height.equalTo(size.height)
        }
        
        bubbleView.snp.remakeConstraints { make in
            make.top.equalTo(avatarImageView)
            if isFromMe {
                make.right.equalTo(avatarImageView.snp.left).offset(-10)
            } else {
                make.left.equalTo(avatarImageView.snp.right).offset(10)
            }
            make.width.equalTo(size.width)
            make.height.equalTo(size.height)
            make.bottom.equalTo(-8)
        }
    }
    
    override func configModel(_ model: MsgBaseModel?) {
        super.configModel(model)
        
        guard let v2Msg = model?.msg, let imageElem = v2Msg.imageElem else { return }
        
        self.messageImageView.setImage(from: imageElem.imageList.filter({ $0.type == .V2TIM_IMAGE_TYPE_THUMB }).first?.url)

        updateLayout()
    }
    
    
}
