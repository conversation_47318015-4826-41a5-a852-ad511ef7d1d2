//
//  UseSkillCell.swift
//  YINDONG
//
//  Created by jj on 2025/6/16.
//

import UIKit
import Lottie

class UseSkillCell: MsgBaseCell {
    
    var pkAction: (() -> Void)?
    var flAction: (() -> Void)?

    lazy var titleLab: UILabel = {
        let lab = UILabel(withText: "等级", textColor: .init(hex: 0x989898), fontType: .regular(11))
        return lab
    }()
    
    lazy var contentLab: UILabel = {
        let lab = UILabel(withText: "", textColor: .textColor3, fontType: .regular(14))
        lab.numberOfLines = 0
        return lab
    }()
    
    lazy var lottieView: LottieAnimationView = {
        let imgV = LottieAnimationView()
        imgV.loopMode = .loop
        imgV.animationSpeed = 0.5
        return imgV
    }()
    
    lazy var bottomStackV: UIStackView = {
        let stack = UIStackView(arrangedSubviews: [], axis: .horizontal, spacing: 20, alignment: .fill, distribution: .fillEqually)
        return stack
    }()

    private let starView = SkillLevelRatingView()

    lazy var titleStack: UIStackView = {
        let stackV = UIStackView(arrangedSubviews: [], axis: .horizontal, spacing: 6, alignment: .fill, distribution: .fillProportionally)
        return stackV
    }()

    // 底部按钮
    private lazy var giveUpButton: UIButton = {
        let button = UIButton(title: "服了 饶命", font: .regular(11))
        button.defaultColor3()
        button.addTarget(self, action: #selector(buttonTapped(button: )), for: .touchUpInside)
        return button
    }()

    private lazy var levelSkillButton: UIButton = {
        let button = UIButton(title: "升级技能", font: .regular(11))
        button.defaultColor3()
        button.addTarget(self, action: #selector(buttonTapped(button: )), for: .touchUpInside)
        return button
    }()

    private lazy var useAgainButton: UIButton = {
        let button = UIButton(title: "再来一次", font: .regular(11))
        button.defaultColor3()
        button.addTarget(self, action: #selector(buttonTapped(button: )), for: .touchUpInside)
        return button
    }()

    // 添加状态标记
    private var animationsPaused = false
    
    override func setupUI() {
        super.setupUI()
        bubbleView.backgroundColor = .white
        bubbleView.layerBorderColor = .init(hex: 0xF6F6FB)
        bubbleView.layerBorderWidth = 2
        bubbleView.layerCornerRadius = 16
        bubbleView.addSubview(lottieView)
        bubbleView.addSubview(contentLab)
        bubbleView.addSubview(titleLab)
        
        lottieView.layerCornerRadius = 8
        lottieView.snp.makeConstraints { make in
            make.width.height.equalTo(67)
            make.centerY.equalToSuperview()
            make.left.equalTo(10)
        }
        
        //底部按钮配置
        bottomStackV.addArrangedSubview(giveUpButton)
        bottomStackV.addArrangedSubview(levelSkillButton)
        bottomStackV.addArrangedSubview(useAgainButton)

        contentView.addSubview(bottomStackV)
        bottomStackV.snp.makeConstraints { make in
            make.centerX.equalTo(bubbleView)
            make.top.equalTo(bubbleView.snp.bottom).offset(12)
            make.height.equalTo(24)
        }
        
        bubbleView.addSubview(titleStack)
        titleStack.snp.makeConstraints { make in
            make.height.equalTo(20)
            make.top.equalTo(13)
        }
        
        titleStack.addArrangedSubview(titleLab)
        titleStack.addArrangedSubview(starView)
        titleLab.snp.makeConstraints { make in
            make.width.equalTo(22)
            make.height.equalTo(20)
        }
        starView.snp.makeConstraints { make in
            make.height.equalTo(20)
        }
        
        [levelSkillButton, giveUpButton, useAgainButton].forEach { bb in
            bb.snp.makeConstraints { make in
                make.width.equalTo(80)
            }
        }
        
        contentLab.snp.makeConstraints { make in
            make.top.equalTo(titleStack.snp.bottom).offset(12)
            make.left.right.equalTo(titleStack)
        }

        // 设置 contentLab 的属性以支持多行文本
        contentLab.numberOfLines = 0
        contentLab.lineBreakMode = .byWordWrapping

    }
    
    override func updateLayout() {
        super.updateLayout()

        // ✅ 动态计算 bubbleView 高度
        let calculatedHeight = calculateBubbleViewHeight()

        bubbleView.snp.remakeConstraints { make in
            make.top.equalTo(avatarImageView)
            if isFromMe {
                make.right.equalTo(avatarImageView.snp.left).offset(-10)
            } else {
                make.left.equalTo(avatarImageView.snp.right).offset(10)
            }
            make.height.equalTo(calculatedHeight)
            make.bottom.equalTo(-40)
            make.width.equalTo(228)
        }
        
        lottieView.snp.remakeConstraints { make in
            make.width.height.equalTo(67)
            make.centerY.equalToSuperview()
            if isFromMe {
                make.right.equalTo(-10)
            } else {
                make.left.equalTo(10)
            }
        }
        
        titleStack.snp.remakeConstraints { make in
            make.height.equalTo(20)
            make.top.equalTo(13)
            if isFromMe {
                make.right.equalTo(lottieView.snp.left).offset(-10)
                make.left.equalTo(10)
            } else {
                make.left.equalTo(lottieView.snp.right).offset(10)
                make.right.equalTo(-10)
            }
        }

    }

    /// 计算 bubbleView 的动态高度
    private func calculateBubbleViewHeight() -> CGFloat {
        let minHeight: CGFloat = 87 // 最小高度
        
        var totalContentHeight = 65.0
        
        if let h = contentLab.attributedText?.string.app_height(forWidth: 130, font: .regular(14)) {
            totalContentHeight += h
        }
        // 返回最大值（确保不小于最小高度）
        return max(minHeight, totalContentHeight)
    }

    override func configModel(_ model: MsgBaseModel?) {
        super.configModel(model)

        guard let skill = model?.skillModel else { return }

        // ✅ 配置Lottie动画但不立即播放
        if let file = GameUtils.getSkillLottieFileName(skillType: skill.skillType, success: skill.success) {
            lottieView.animation = LottieAnimation.named(file)
            lottieView.play()
            // 不在这里调用 play()，由外部控制
        }

        // ✅ 配置星级但不立即播放动画
        starView.configure(withLevel: skill.skillLevel)

        // 配置内容文本和按钮显示
        configureContentAndButtons(model: model, skill: skill)

        updateLayout()
    }

    /// 配置内容文本和按钮显示逻辑
    private func configureContentAndButtons(model: MsgBaseModel?, skill: ChatSkillModel) {
        guard let model = model else { return }

        let isSelf = model.isMeSend
        let isKissSkill = GameUtils.isKissSkill(skillType: skill.skillType)

        // 获取技能描述
        let desc = GameUtils.skillUserDesc(isMe: isSelf, bean: skill)

        if !skill.success {
            // 失败情况
            if skill.runeFanjiMsg == 0 && isSelf {
                contentLab.text = "被攻击触发反击，反击失败"
            } else {
                // 使用 YYText 创建富文本
                configureRichText(isSelf: isSelf, skill: skill, model: model, desc: desc)
            }

            // 控制底部按钮显示隐藏 - 失败情况
            if isSelf {
                giveUpButton.isHidden = true
                levelSkillButton.isHidden = false
                useAgainButton.isHidden = false
                useAgainButton.setTitle("再来一次", for: .normal)
            } else {
                giveUpButton.isHidden = true
                levelSkillButton.isHidden = true
                useAgainButton.isHidden = false
                useAgainButton.setTitle(isKissSkill ? "回馈Ta" : "反击", for: .normal)
            }
        } else {
            // 成功情况
            let prefix = (skill.runeFanjiMsg == 0 && isSelf) ? "被攻击触发反击，" : ""
            contentLab.text = "\(prefix)\(desc)"

            // 控制底部按钮显示隐藏 - 成功情况
            if isSelf {
                giveUpButton.isHidden = true
                levelSkillButton.isHidden = true
                useAgainButton.isHidden = false
                useAgainButton.setTitle("再来一次", for: .normal)
            } else {
                giveUpButton.isHidden = isKissSkill
                levelSkillButton.isHidden = true
                useAgainButton.isHidden = false
                useAgainButton.setTitle(isKissSkill ? "回馈Ta" : "不服 报仇", for: .normal)
            }
        }

        // 显示底部按钮容器
        bottomStackV.isHidden = false
    }

    /// 配置富文本（使用 YYText）
    private func configureRichText(isSelf: Bool, skill: ChatSkillModel, model: MsgBaseModel, desc: String) {
        let attributedText = NSMutableAttributedString()

        // 添加前缀
        if isSelf {
            attributedText.append(NSAttributedString(string: "你向"))
        }

        // 添加用户名（可点击）
        let userName = isSelf ? skill.userNickName : model.msg?.nickName ?? ""
        let userNameAttr = NSMutableAttributedString(string: userName)
        userNameAttr.yy_setColor(UIColor.systemBlue, range: NSRange(location: 0, length: userName.count))

        // 添加点击事件
        userNameAttr.yy_setTextHighlight(NSRange(location: 0, length: userName.count),
                                        color: UIColor.systemBlue,
                                        backgroundColor: UIColor.clear) { [weak self] (view, text, range, rect) in
            
//            self?.openUserDetailPage(userID: userID)
        }

        attributedText.append(userNameAttr)

        // 添加描述
        attributedText.append(NSAttributedString(string: desc))
        attributedText.yy_font = .regular(14)
        
        contentLab.attributedText = attributedText
    }

    /// 打开用户详情页面
    private func openUserDetailPage(userID: String) {
        print("打开用户详情页面: \(userID)")
        // TODO: 实现打开用户详情页面的逻辑
    }
    
    
    override func tapAction() {

    }

    // MARK: - Button Actions
    @objc func buttonTapped(button: UIButton) {
        
        switch button.currentTitle ?? "" {
        case "再来一次", "不服 报仇", "反击":
            pkAction?()
        case "服了饶命":
            flAction?()
        case "升级技能":
            let vc = SkillMainListVC()
            AppTool.getCurrentViewController().navigationController?.pushViewController(vc, animated: true)
            
            
        default:
            break
        }
        // 放弃按钮点击事件
        print("放弃按钮被点击")
        // TODO: 实现放弃逻辑
    }


}
