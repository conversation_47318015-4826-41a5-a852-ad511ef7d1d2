//
//  GiftMsgCell.swift
//  YINDONG
//
//  Created by jj on 2025/3/25.
//

import UIKit

class GiftMsgCell: MsgBaseCell {

    lazy var contentLab: UILabel = {
        let lab = UILabel(withText: "", textColor: .textColor3, fontType: .regular(14))
        lab.numberOfLines = 2
        return lab
    }()

    lazy var iconImgV: UIImageView = {
        let imgV = UIImageView(image: UIImage(named: ""))
        return imgV
    }()

    // MARK: - Cell Lifecycle
    override func prepareForReuse() {
        super.prepareForReuse()
        contentLab.text = nil
        iconImgV.image = nil
        iconImgV.kf.cancelDownloadTask()
    }

    override func setupUI() {
        super.setupUI()
        bubbleView.backgroundColor = .white
        bubbleView.addSubview(contentLab)
        bubbleView.addSubview(iconImgV)
        
        contentLab.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(15)
            make.width.lessThanOrEqualTo(150)
        }
        
        iconImgV.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.height.equalTo(44)
            make.left.equalTo(contentLab.snp.right).offset(15)
            make.right.equalTo(-12)
        }
        
    }
    
    override func updateLayout() {
        super.updateLayout()
        
        bubbleView.snp.remakeConstraints { make in
            make.top.equalTo(avatarImageView)
            if isFromMe {
                make.right.equalTo(avatarImageView.snp.left).offset(-10)
            } else {
                make.left.equalTo(avatarImageView.snp.right).offset(10)
            }
            make.height.equalTo(55)
            make.bottom.equalTo(-8)
        }
    }

    override func configModel(_ model: MsgBaseModel?) {
        super.configModel(model)
        if let text = model?.giftItem?.txtContent, text.count > 0 {
            contentLab.text = text
        } else {
            contentLab.text = "\(model?.giftItem?.giftName ?? "") x \(model?.giftItem?.giftNum ?? 0)"
        }
        iconImgV.setImage(from: model?.giftItem?.giftQuietUrl)
        updateLayout()
    }
}
