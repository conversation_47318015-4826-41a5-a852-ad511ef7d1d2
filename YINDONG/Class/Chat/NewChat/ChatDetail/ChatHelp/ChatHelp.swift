//
//  ChatHelp.swift
//  YINDONG
//
//  Created by jj on 2025/4/9.
//

import UIKit

class ChatHelp: NSObject {
    
    var sendMsgPulisher = PublishSubject<[String : MsgBaseModel]>()

    
    override init() {
        super.init()
        T_IMHelper.shared.addDelagete(self)
    }

    func remove() {
        T_IMHelper.shared.removeDelegate(self)
        T_IMHelper.shared.toUserPre = nil
    }
    
    var toUserPre: DressBaseModel?
    
    //获取对方的装扮信息
    func getUserPre(id: String?, callBack: (() -> Void)?) {
        NetworkUtility.request(target: .userGetPre(["imNumber": id ?? ""]), model: DressBaseModel.self, isList: true) { result in
            if result.isError { return }
            self.toUserPre = result.modelArr.first
            callBack?()
        }
    }
    
}

extension ChatHelp: MessageHandlerDelegate {
    
    func onRecvNewMessage(dict: [String : MsgBaseModel]) {
        sendMsgPulisher.onNext(dict)
    }
    
}
