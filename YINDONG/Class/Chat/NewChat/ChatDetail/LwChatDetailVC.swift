//
//  LwChatDetailVC.swift
//  YINDONG
//
//  Created by jj on 2025/3/25.
//

import UIKit
import IQKeyboardManagerSwift
import IQKeyboardToolbarManager

class LwChatDetailVC: YinDBaseVC {
    
    var playManger = MediaPlaybackManager()
    
    var tempVoCell: VoiceCell?
    
    var uid: String?
    
    var datas: [MsgBaseModel] = []
    
    var infoModel: ChatDetailUserModel?
    
    var userInfo: V2TIMFriendInfo?
    
    lazy var navView: ChatDetailNavView = {
        let vv = ChatDetailNavView()
        return vv
    }()
    
    lazy var tipView: ChatWarningView = {
        let view = ChatWarningView()
        view.backgroundColor = UIColor(hexString: "#FEF8F7")
        view.snp.makeConstraints({ (make) in
            make.height.equalTo(30)
        })
        view.onDismiss = {[weak self] in
            guard let `self` = self else { return }
            view.isHidden = true
        }
        return view
    }()
    
    lazy var chatInputView: ChatInputView = {
        let inputV = ChatInputView()
        inputV.delegate = self
        return inputV
    }()
    
    ///用户是否滑动了
    var scrollingTriggeredByUser = false
    var shouldAutoScroll = true
    // 设置一个阈值（根据需求调整），当滚动到距离底部不足这个阈值时认为处于“底部区域”
    let autoScrollThreshold: CGFloat = 50.0
    
    var lasMsg: V2TIMMessage?
    
    var tempMsg: [MsgBaseModel] = []
    
    lazy var chatDetailHelp: ChatHelp = {
        let help = ChatHelp()
        return help
    }()
    
    lazy var roomItem: RoomItemView = {
        let vv = RoomItemView()
        vv.frame = CGRectMake(0, 0, 60, 60)
        vv.isHidden = true
        return vv
    }()
    
    private var isScrolling = false
    private var cellsPaused = false  // 添加状态标记，避免重复操作

    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // Do any additional setup after loading the view.
        noDataTitle = ""
        noDataImageName = ""
        loadData()
        loadInfo()
        
        isNavigationBarVisible = false
        
        
        
        NotificationCenter.default.rx.notification(.removeMessageNotification).subscribe(onNext: { [weak self] notification in
            guard let self = self else { return }

            self.datas.removeAll()
            // 清空数据时使用全量刷新
            self.reloadTable()

        }).disposed(by: baseDisposeBag)
        
        NotificationCenter.default.rx.notification(.friendInfoChangedNotification).subscribe(onNext: { [weak self] notification in
            guard let self = self else { return }
            
            if let vv = notification.object as? V2TIMFriendInfo {
                self.userInfo = vv
                self.reloadUserInfo()
            }
        }).disposed(by: baseDisposeBag)
        
        
        NotificationCenter.default.rx.notification(.intimacyActionNotification).subscribe(onNext: { [weak self] notification in
            guard let self = self else { return }
            
            guard let index = notification.object as? Int else {
                return
            }
            
            if index == 0 {
                let vv = GiftPanelView(index: 0, type: .chat)
                vv.user_id = self.uid
                vv.present(in: self.view)
            }
            
            if index == 1 {
                self.chatInputView.textField.becomeFirstResponder()
            }
            
            
        }).disposed(by: baseDisposeBag)
        
        chatDetailHelp.sendMsgPulisher.subscribe(onNext: { [weak self] dict in
            self?.onRecvNewMessage(dict: dict)
        }).disposed(by: baseDisposeBag)
        
        playManger.delegate = self
        
        
        chatDetailHelp.getUserPre(id: uid) { [weak self] in
            guard let self = self else { return }
            
            T_IMHelper.shared.toUserPre = self.chatDetailHelp.toUserPre
            self.reloadTable()
        }
        
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        
        let navStackV = UIStackView(arrangedSubviews: [], axis: .vertical, alignment: .fill, distribution: .fillProportionally)
        view.addSubview(navStackV)
        navStackV.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
        }
        
        navStackV.addArrangedSubview(navView)
        if ChatWarningView.shouldShowTip() {
            navStackV.addArrangedSubview(tipView)
        }
        
        
        navView.snp.makeConstraints { make in
            make.height.equalTo(AppTool.navigationBarHeight)
        }
        
        view.backgroundColor = .f9Color
        tableView.dataSource = self
        tableView.delegate = self
        tableView.alpha = 0
        tableView.register(cellWithClass: TextMsgCell.self)
        tableView.register(cellWithClass: ImageMsgCell.self)
        tableView.register(cellWithClass: NoticeMsgCell.self)
        tableView.register(cellWithClass: GiftMsgCell.self)
        tableView.register(cellWithClass: ChatCardCell.self)
        tableView.register(cellWithClass: TacitOptionCell.self)
        tableView.register(cellWithClass: VoiceCell.self)
        tableView.register(cellWithClass: RoomInviteMsgCell.self)
        tableView.register(cellWithClass: UseSkillCell.self)

        // 配置性能优化
        configureTableViewPerformance()

        view.addSubview(tableView)
        
        let tap = UITapGestureRecognizer(target: self, action: #selector(hideKeyboard))
        tap.numberOfTapsRequired = 1;
        tap.cancelsTouchesInView = false;
        tableView.addGestureRecognizer(tap)
        
        view.addSubview(activityIndicator)
        activityIndicator.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        activityIndicator.startAnimating()
        
        view.addSubview(chatInputView)
        
        tableView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalTo(chatInputView.snp.top).offset(-5)
            make.top.equalTo(navStackV.snp.bottom)
        }
        
        view.addSubview(roomItem)
        roomItem.snp.makeConstraints { make in
            make.width.height.equalTo(60)
            make.right.equalTo(-20)
            make.top.equalTo(AppTool.navigationBarHeight + 50)
        }
        addRefesh()
    }
    
    @objc
    func hideKeyboard() {
        //        view.endEditing(true)
        chatInputView.dismissAllPanels()
    }
    
    func loadInfo() {
        self.navView.uid = self.uid
        
        NetworkUtility.request(target: .chatUserInfo(["imId": uid ?? ""]), model: ChatDetailUserModel.self) { [weak self] result in
            guard let self = self else { return }
            if result.isError { return }
            self.infoModel = result.model
            self.navView.model = self.infoModel
            self.chatInputView.infoModel = self.infoModel
        }
        
        roomItem.isHidden = true
        NetworkUtility.request(target: .roomCheck(["imId": uid ?? ""])) { result in
            if result.isError { return }
            
            ///有房间id的情况
            if let roomid = result.dataJson?.rawString(), roomid.count > 0 {
                self.roomItem.roomId = roomid
            }
        }
        
        V2TIMManager.shared.getFriendsInfo(userIDList: [uid ?? ""]) { [weak self] resultList in
            guard let self = self else { return }
            guard let result = resultList.first, let model = result.friendInfo else { return }
            self.userInfo = model
            self.reloadUserInfo()
        } fail: { code, desc in
            TLog("获取用户资料失败: \(desc)")
        }
    }
    
    
    func reloadUserInfo() {
        guard let model = userInfo else { return }
        var title = model.friendRemark
        if let remark = model.friendRemark, remark.isEmpty == false  {
            title = remark
        } else if let nickname = model.userFullInfo.nickName, nickname.isEmpty == false  {
            title = nickname
        } else {
            title = model.userID
        }
        self.navView.nameLab.text = title
        self.chatInputView.userInfo = model
    }
    
    func reloadVisibleRows() {
        guard let visibleRows = self.tableView.indexPathsForVisibleRows else { return }

        // 使用 CATransaction 彻底禁用动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        CATransaction.setAnimationDuration(0)

        UIView.performWithoutAnimation {
            self.tableView.reloadRows(at: visibleRows, with: .none)
        }

        CATransaction.commit()
    }

    /// 智能刷新：只在必要时进行全量刷新
    func smartReloadTable() {
        // 如果数据量较小或者是首次加载，使用全量刷新
        if datas.count <= 20 || tableView.alpha < 1 {
            reloadTable()
            return
        }

        // 否则只刷新可见行
        reloadVisibleRows()
    }
    
    override func loadData() {
        
        guard let uid = uid else { return }
        
        V2TIMManager.shared.getC2CHistoryMessageList(userID: uid, count: pageMax, lastMsg: lasMsg) { [weak self] msgs in
            
            guard let self = self else { return }
            self.endRefesh()
            if msgs.count < self.pageMax {
                self.tableView.mj_header = nil
            }
            
            self.activityIndicator.stopAnimating()
            
            self.tempMsg = msgs.compactMap({ return MsgBaseModel(msg: $0) }).reversed()
            
            for (i, item) in self.tempMsg.enumerated() {
                // 第一条消息直接显示时间
                if i == 0 {
                    item.showTime = true
                    continue
                }
                
                // 如果消息类型为 meetSuccess 或 tipsContent，则显示时间
                if item.messageType == .meetSuccess || item.messageType == .tipsContent {
                    item.showTime = true
                    continue
                }
                
                // 获取当前和上一条消息的时间戳，注意 guard 中使用 continue 而不是 return
                guard let curStamp = item.msg?.timestamp.timeIntervalSince1970,
                      let previousStamp = self.tempMsg[i - 1].msg?.timestamp.timeIntervalSince1970 else {
                    item.showTime = true
                    continue
                }
                
                // 如果当前消息与上一条消息的时间差超过5分钟，则显示时间
                item.showTime = (curStamp - previousStamp) > (5 * 60)
                TLog("Current timestamp: \(curStamp) --- Previous timestamp: \(previousStamp)")
            }
            
            if self.lasMsg == nil {
                self.datas = self.tempMsg
            } else {
                self.datas.insert(contentsOf: self.tempMsg, at: 0)
            }
            
            self.updateMessagesForSelection(messages: &self.datas)
            
            let tempArr = self.datas.filter({ $0.msg?.isRead == false && $0.messageType == .gift })
        
            
            self.reloadTable()
            if self.lasMsg != nil {
                let visibleIndexPaths = self.tableView.indexPathsForVisibleRows
                // 恢复之前可见的位置
                if let firstVisibleIndexPath = visibleIndexPaths?.first {
                    let newIndex = firstVisibleIndexPath.row + self.tempMsg.count
                    // 确保新的索引不会超出现有行的范围
                    let totalRows = self.tableView.numberOfRows(inSection: 0)
                    // 使用min函数确保newIndex不会超出最后一行的索引
                    let safeIndex = min(newIndex, totalRows - 1)
                    let newIndexPath = IndexPath(row: safeIndex, section: 0)
                    self.tableView.scrollToRow(at: newIndexPath, at: .top, animated: false)
                }
            } else {
                self.scrollToLastMessage(animated: false)
            }
            
            // 记录下次拉取的 lastMsg，用于下次拉取
            self.lasMsg = msgs.last
            
            if self.tableView.alpha == 1 { return }
            UIView.animate(withDuration: 0.4) {
                self.tableView.alpha = 1
                
                tempArr.forEach({
                    self.playGift(model: $0)
                })
            }
            T_IMHelper.shared.clearUnreadMessage("c2c_\(uid)")

        } fail: { code, desc in
            TLog("获取失败----fail \(code), \(desc)")
            self.activityIndicator.stopAnimating()
        }
        
    }
    
    
    func updateMessagesForSelection(messages: inout [MsgBaseModel]) {
        // 记录 1018 消息在数组中的索引
        var indexMap = [Int: Int]()
        
        // 遍历找到所有 type == 1018 的消息，并初始化 userSelected
        for (index, message) in messages.enumerated() where message.messageType == .friendAnswerProblem {
            messages[index].selectedUsers = []
            indexMap[message.tacitModel?.businessId ?? 0] = index
        }
        
        // 遍历所有 type == 1019 的消息，找到匹配的 1018，并更新 userSelected
        for message in messages where message.messageType == .friendAnswerReply {
            if let index = indexMap[message.wdModel?.businessId ?? 0] {
                messages[index].selectedUsers.append(message)
            }
        }
    }
    
    private func scrollToLastMessage(animated: Bool = true) {
        if datas.count > 0 {
            let lastRow = datas.count - 1
            let indexPath = IndexPath(row: lastRow, section: 0)
            // 避免在tableView约束未完成布局时滚动导致崩溃
            DispatchQueue.main.async {
                if self.tableView.contentSize.height > 0 {
                    self.tableView.scrollToRow(at: indexPath, at: .bottom, animated: animated)
                }
            }
        }
    }
    
    deinit {
        TLog("消息详情销毁移除代理=======")
        chatInputView.textField.exitText()
        chatDetailHelp.remove()
    }
}

// MARK: - TableView Performance Optimization
extension LwChatDetailVC {

    /// 配置 TableView 性能优化
    private func configureTableViewPerformance() {
        // 启用自动行高估算
        tableView.estimatedRowHeight = 80
        tableView.rowHeight = UITableView.automaticDimension

        // 优化滚动性能
        tableView.decelerationRate = UIScrollView.DecelerationRate.fast

        // 预加载优化
        tableView.prefetchDataSource = self

        // 禁用不必要的动画
        tableView.layer.allowsGroupOpacity = false
        tableView.layer.shouldRasterize = false

        // 优化分隔线
        tableView.separatorStyle = .none

        // 设置内容模式
        tableView.contentInsetAdjustmentBehavior = .never
    }

}

extension LwChatDetailVC: UITableViewDataSourcePrefetching {
    func tableView(_ tableView: UITableView, prefetchRowsAt indexPaths: [IndexPath]) {
        // 预加载行的逻辑，可以在这里预处理一些数据
        for indexPath in indexPaths {
            if indexPath.row < datas.count {
                let message = datas[indexPath.row]
                // 预计算高度或预加载图片等
                _ = message.calculatedHeight
            }
        }
    }
}

extension LwChatDetailVC: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.datas.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let message = datas[indexPath.row]
        
        switch message.messageType {
        case .image:
            let cell = tableView.dequeueReusableCell(withClass: ImageMsgCell.self)
            cell.configModel(message)
            return cell
            
        case .text, .friendAnswerReply:
            let cell = tableView.dequeueReusableCell(withClass: TextMsgCell.self)
            cell.configModel(message)
            return cell
            
        case .gift:
            let cell = tableView.dequeueReusableCell(withClass: GiftMsgCell.self)
            cell.configModel(message)
            return cell
            
        case .tipsContent, .clapUser, .follow, .deleteFriend:
            let cell = tableView.dequeueReusableCell(withClass: NoticeMsgCell.self)
            cell.configModel(message)
            return cell
            
        case .meetSuccess:
            let cell = tableView.dequeueReusableCell(withClass: ChatCardCell.self)
            cell.configModel(message)
            return cell
            
        case .friendAnswerProblem:
            let cell = tableView.dequeueReusableCell(withClass: TacitOptionCell.self)
            cell.configModel(message)
            cell.anwerAction = { [weak self] txt, model in
                self?.anwer(txt: txt, model: model)
            }
            return cell
        case .voice:
            let cell = tableView.dequeueReusableCell(withClass: VoiceCell.self)
            cell.configModel(message)
            cell.playAudioCallback = { [weak self] url, model in
                guard let self = self else { return }
                //设置已播放
                model?.msg?.localCustomInt = 1
                if let tempVoCell = self.tempVoCell {
                    if tempVoCell == cell {
                        if self.playManger.currentState == .loading { return }
                        if self.playManger.currentState == .playing {
                            tempVoCell.stopAnim()
                            self.playManger.stop()
                        } else {
                            self.playAudio(url: url)
                        }
                        return
                    }
                    tempVoCell.stopAnim()
                    self.playManger.stop()
                }
                self.playAudio(url: url)
                self.tempVoCell = cell
            }
            return cell
        case .liveInvite:
            let cell = tableView.dequeueReusableCell(withClass: RoomInviteMsgCell.self)
            cell.configModel(message)
            return cell
        case .skill:
            let cell = tableView.dequeueReusableCell(withClass: UseSkillCell.self)
            cell.configModel(message)
            cell.pkAction = { [weak self] in
                guard let self = self else { return }
                let pk = UserPkSheetView(uid: uid, name: userInfo?.userFullInfo.nickName)
                pk.present(in: self.view)
            }
            return cell
        default:
            let cell = tableView.dequeueReusableCell(withClass: NoticeMsgCell.self)
            cell.configModel(message)
            return cell
        }
        
    }
    
    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        let message = datas[indexPath.row]
        return message.calculatedHeight
    }
    
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        scrollingTriggeredByUser = true
        self.hideKeyboard()
        
        isScrolling = true
        
    }
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 计算滚动视图的底部偏移量
        let bottomOffset = scrollView.contentSize.height - scrollView.bounds.size.height
        // 当当前偏移量在底部区域内时，允许自动滚动
        if scrollView.contentOffset.y >= bottomOffset - autoScrollThreshold {
            shouldAutoScroll = true
        } else {
            shouldAutoScroll = false
        }
    }
    
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        updateAutoScrollStatus(scrollView)
      
    }

    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        updateAutoScrollStatus(scrollView)
     
    }
    
    
    
    /// 辅助方法：判断用户停止拖动后是否处于底部区域
    private func updateAutoScrollStatus(_ scrollView: UIScrollView) {
        let bottomOffset = scrollView.contentSize.height - scrollView.bounds.size.height
        if scrollView.contentOffset.y >= bottomOffset - autoScrollThreshold {
            shouldAutoScroll = true
        } else {
            shouldAutoScroll = false
        }
        scrollingTriggeredByUser = false
    }
    
    
    func playAudio(url: String?) {
        TLog("每次的----\(url)")
        guard let url = URL(string: url) else { return }
        playManger.play(url: url)
    }
    
    func updateInfo(content: String?, isAdd: Int) {
        var dict = creatDict()
        dict["toAccount"] = uid ?? ""
        dict["friendId"] = infoModel?.id ?? 0
        dict["isFriendValue"] = isAdd
        dict["type"] = 0
        dict["messageContent"] = content
        
        NetworkUtility.request(target: .updateIntimacy(dict)) { result in
            if result.isError { return }
            
            self.loadInfo()
        }
    }
    
}


extension LwChatDetailVC {
    
    func onRecvNewMessage(dict: [String : MsgBaseModel]) {
        guard let id = dict.keys.first, let model = dict[id] else { return }
        TLog("过来了一个新消息-----id\(id)")
        if id != uid && id != kuser.imNumber.string { return }
        
        if let msg = model.msg, (!msg.isSelf) {
            T_IMHelper.shared.clearUnreadMessage("c2c_\(uid ?? "")")
        }
        
        if model.messageType == .image, model.msg?.isSelf == false {
            var content = creatDict()
            content["content"] = model.msg?.imageElem?.imageList.last?.url ?? ""
            content["type"] = 32
            updateInfo(content: content.jsonString(), isAdd: 0)
        }
        
        ///收到礼物 立马刷新亲密度
        if model.messageType == .gift {
            loadInfo()
            playGift(model: model)
        }
        
        self.datas.append(model)
        
        self.datas.sort { lhs, rhs in
            // 再按时间排序（时间最新的排前面）
            let lhsTime = lhs.time
            let rhsTime = rhs.time
            
            let lhsSeq = lhs.msg?.seq ?? 0
            let rhsSeq = rhs.msg?.seq ?? 0
            
            if lhsTime == rhsTime {
                return lhsSeq < rhsSeq
            } else {
                return lhsTime < rhsTime
            }
        }
        
        for (index, message) in self.datas.enumerated() {
            TLog("Index: \(index), Time: \(message.time), Seq: \(message.msg?.seq)---\(message.messageType)")
        }
        
        //如果是回答，立马刷新所有的数据
        if model.messageType == .friendAnswerReply {
            self.updateMessagesForSelection(messages: &datas)
            self.reloadTable() // 回答消息需要全量刷新
        } else {
            // 新消息只需要插入新行，避免全量刷新
            self.insertNewMessage()
        }

        if shouldAutoScroll {
            Async.main(after: 0.01) {
                self.scrollToLastMessage(animated: true)
            }
        }
    }
    
    func playGift(model: MsgBaseModel) {
        
        let a = AnimationElement()
        a.animationView = self.view
        a.url = model.giftItem?.giftEastUrl
        a.imageUrl = model.giftItem?.giftQuietUrl
        a.userName = userInfo?.userFullInfo.nickName
        a.phoneUrl = userInfo?.userFullInfo.faceURL
        PlayerManager.shared.addAnimationElement(a)
        
    }

    //刷新视图
    func reloadTable() {
        // 使用 CATransaction 彻底禁用动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        CATransaction.setAnimationDuration(0)

        // 同时使用 UIView.performWithoutAnimation 双重保险
        UIView.performWithoutAnimation {
            tableView.reloadData()
        }

        CATransaction.commit()
    }

    /// 插入新消息，避免全量刷新
    private func insertNewMessage() {
        let newIndexPath = IndexPath(row: datas.count - 1, section: 0)

        // 使用 CATransaction 彻底禁用动画
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        CATransaction.setAnimationDuration(0)

        UIView.performWithoutAnimation {
            tableView.insertRows(at: [newIndexPath], with: .none)
        }

        CATransaction.commit()
    }

    /// 批量更新消息，用于优化性能
    private func batchUpdateMessages(updates: () -> Void) {
        tableView.performBatchUpdates({
            updates()
        }, completion: nil)
    }
    
}

extension LwChatDetailVC {
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        IQKeyboardManager.shared.isEnabled = false
        IQKeyboardToolbarManager.shared.isEnabled = false
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        IQKeyboardManager.shared.isEnabled = true
        IQKeyboardToolbarManager.shared.isEnabled = true

    }
    
    func anwer(txt: String?, model: TacitModel?) {
        
        var isAdd = 0
        if self.datas.last?.msg?.isSelf == false {
            isAdd = 1
        }
        var dict = self.creatDict()
        dict["isFriendValue"] = isAdd
        dict["resultText"] = txt ?? ""
        dict["businessId"] = model?.businessId
        dict["friendsIds"] = self.infoModel?.id ?? 0
        dict["imNumber"] = self.uid ?? ""
        NetworkUtility.request(target: .mqanswerMsg(dict)) { result in
            if result.isError { return }
            
            if isAdd == 1 {
                self.loadInfo()
            }
        }
    }
}


extension LwChatDetailVC: ChatInputViewDelegate {
    
    func inputToolbar(_ toolbar: ChatInputView, didSendText text: String) {
        
        let timMsg = T_IMHelper.shared.sendV2TIMMessage(type: .text(content: text), toUser: self.userInfo) { [weak self] isSuccess, msgID in
            self?.reloadVisibleRows()
        }
        guard let timMsg = timMsg else { return }
        
        var isAdd = 0
        if self.datas.last?.msg?.isSelf == false {
            isAdd = 1
        }
        var content = creatDict()
        content["content"] = text
        content["type"] = 16
        updateInfo(content: content.jsonString(), isAdd: isAdd)
        
        self.datas.append(MsgBaseModel(msg: timMsg))
        self.reloadTable()
        scrollToLastMessage(animated: true)
    }
    
    func inputToolbar(_ toolbar: ChatInputView, didTapFunctionButton type: FunctionButtonType) {
        toolbar.textField.resignFirstResponder()
        switch type {
        case .photo:
            AppTool.presentPhotoPicker(maxSelectionCount: 1, onSelectionCompleteWithImages:  { [weak self] imgs, isOriginal in
                guard let self = self else { return }
                guard let img = imgs.first else { return }
                let path = AppTool.saveImageToLocalDirectory(image: img) ?? ""
                let timMsg = T_IMHelper.shared.sendV2TIMMessage(type: .photo(imagePath: path), toUser: self.userInfo) { isSuccess, msgID in
                    self.reloadVisibleRows()
                }
                guard let timMsg = timMsg else { return }
                self.datas.append(MsgBaseModel(msg: timMsg))
                self.reloadTable()
                scrollToLastMessage(animated: true)
            })
        case .gift:
            let vv = GiftPanelView(index: 0, type: .chat)
            vv.user_id = uid
            vv.present(in: self.view)
        case .chat:
            let vv = MatchQuestionModalView()
            vv.present(in: self.view)
            vv.onQuestionSelected = { mm in
                var dict = self.creatDict()
                dict["friendsIds"] = self.infoModel?.id ?? ""
                dict["imNumber"] = self.uid ?? ""
                dict["businessId"] = mm.id
                vv.anwer(dict: dict)
            }
        case .game:
            let vv = UserPkSheetView(uid: uid, name: userInfo?.userFullInfo.nickName)
            vv.present(in: self.view)
        default:
            break
        }
    }
    
    func inputToolbar(_ toolbar: ChatInputView, didChangeHeight height: CGFloat) {
        if !toolbar.isKeyboardVisible && toolbar.activePanel == .none  { return }
        Async.main(after: 0.01) {
            self.scrollToLastMessage(animated: false)
        }
    }
    
    func inputToolbar(_ toolbar: ChatInputView, didSendVoice url: String, time: Int) {
        
        TLog("语音消息-------\(url)----\(time)")
        let timMsg = T_IMHelper.shared.sendV2TIMMessage(type: .voice(path: url, dd: time), toUser: self.userInfo) { isSuccess, msgID in
            self.reloadVisibleRows()
        }
        guard let timMsg = timMsg else { return }
        self.datas.append(MsgBaseModel(msg: timMsg))
        self.reloadTable()
        scrollToLastMessage(animated: true)
    }
    
}


extension LwChatDetailVC: MediaPlaybackDelegate {
    
    
    func playbackManager(_ manager: MediaPlaybackManager, progressDidChange progress: Float) {
        
    }
    
    func playbackManager(_ manager: MediaPlaybackManager, didUpdateCurrentTime time: TimeInterval) {
        if time <= 0 {
            return
        }
        // 更新时间标签
        //        let timeString = manager.formatTime(time)
        //        print("当前时间: \(timeString)") // 格式：00:00
        //        let i = Int((self.tempVoCell?.allD.double ?? 0.0) - time)
        //        tempVoCell?.secondLabel.text = "\(i)''"
    }
    
    
    func playbackManager(_ manager: MediaPlaybackManager, didChangeState state: PlaybackState) {
        switch state {
        case .finished:
            print("播放完成")
            // 处理播放完成逻辑
            tempVoCell?.stopAnim()
        case .failed(let error):
            print("播放失败: \(error.localizedDescription)")
            tempVoCell?.stopAnim()
        default:
            break
        }
    }
    
    
}
