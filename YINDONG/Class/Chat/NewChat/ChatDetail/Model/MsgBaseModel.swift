//
//  MsgBaseModel.swift
//  YINDONG
//
//  Created by jj on 2025/3/25.
//

import UIKit
import SwiftyJSON

enum V2MessageType: Int {
    case meetSuccess = 1001           // 邂逅成功
//    case playDice = 1002              // 抛骰子
//    case trueSpeak = 1003             // 真心话
//    case friendApply = 1004           // 纳后宫请求
//    case palette = 1005               // 你画我猜
    case gift = 1006                  // 礼物消息
    case tipsContent = 1007           // 提示
//    case diceVictory = 1008           // 选择惩罚
    case deleteFriend = 1009          // 解除关系
//    case afficheLove = 1010           // 表白消息
    case follow = 1011                // 关注消息
//    case gift2 = 1014                 // 搭讪礼物
    case liveInvite = 1015            // 邀请消息
//    case confessionNotify = 1016      // 组CP消息
//    case confessionSuccess = 1017     // CP邀请函 - 成功
    case friendAnswerProblem = 1018   // 默契问答
    case friendAnswerReply = 1019     // 默契问答
    case clapUser = 1020              // 拍一拍
    case skill = 1200                 //技能
//    case followGift = 1021            // 邀请关注礼物
//    case playCaiquan = 1022           // 猜拳
//    case confessionFail = 1023        // CP邀请函 - 失败
//    case whisper = 1024               // 纸飞机
    
    case text
    case image
    case voice
    case unkonw
    
    /// 根据 id 查找对应的枚举类型
    static func from(id: Int) -> V2MessageType? {
        return V2MessageType(rawValue: id)
    }
    
    var lastContet: String? {
        switch self {
        case .meetSuccess:
            return "[邂逅成功]"
        case .image:
            return "[图片]"
        case .voice:
            return "[语音]"
        case .clapUser:
            return "[拍一拍]"
        case .friendAnswerProblem,
                .friendAnswerReply:
            return "[默契问答]"
        case .tipsContent:
            return "[提示]"
        case .gift:
            return "[礼物]"
        case .deleteFriend:
            return "[好友已解除]"
        case .follow:
            return "[关注消息]"
        case .liveInvite:
            return "[邀请消息]"
        case .skill:
            return "[使用技能]"
        default:
            return "未知消息"
        }
        
        
    }
}


class MsgBaseModel {
    
    var messageType: V2MessageType = .unkonw
    
    var time: TimeInterval {
        return (msg?.timestamp.timeIntervalSince1970 ?? 0) * 1000.0
    }
    
    var isMeSend: Bool {
        return msg?.isSelf ?? false
    }
    
    var msg: V2TIMMessage?
    
    
    var bubbleModel: ChatBubbleModel?
    
    var customData: ChatBubbleModel? {
        if let bb = bubbleModel {
            return bb
        }
        let msgData = msg?.cloudCustomData?.toJson()
        bubbleModel = ChatBubbleModel.deserialize(from: msgData ?? "")
        TLog("消息体里面的云字段------\(msgData)")
        return bubbleModel
    }
    
    var calculatedHeight: CGFloat = 0
    
    var tipContent: String?
    
    var showTime: Bool = false
    
    var giftItem: GiftModel?
    
    var cardModel: UserInfoModel?
    
    var wdModel: TacitAnswerModel?
    
    var tacitModel: TacitModel?
    ///默契问答使用到的
    var selectedUsers: [MsgBaseModel] = []
    var isTacitAgreement: Bool = false
    
    var inviteM: RoomInviteModel?
    
    var skillModel: ChatSkillModel?
    
    convenience init(msg: V2TIMMessage?) {
        self.init()
        self.msg = msg
        guard let msg = msg else {
            messageType = .unkonw
            return
        }
        
        if msg.status == .V2TIM_MSG_STATUS_LOCAL_REVOKED {
            messageType = .tipsContent
            tipContent = msg.sender == kuser.imNumber.string ? "你撤回了一条消息" : "对方撤回了一条消息"
            self.calculatedHeight = calculateHeight()
            return
        }
        
        switch msg.elemType {
        case .V2TIM_ELEM_TYPE_CUSTOM:
            guard let data = msg.customElem?.data else { return }
            let json = try? JSON(data: data)
            let type = json?["type"].rawString()?.int ?? 0
            self.messageType = V2MessageType.from(id: type) ?? .unkonw
            if let jsonstring = msg.customElem?.data?.toJson() {
                TLog("自定义消息内容-----\(jsonstring)----\(self.messageType)")
            }
            
            if self.messageType == .tipsContent || self.messageType == .deleteFriend {
                tipContent = json?["content"].rawString()
            }
            
            if self.messageType == .clapUser {
                let uu = ClapUserModel.deserialize(from: json?.dictionaryObject ?? [:])
                tipContent = uu?.tipContent
            }
            
            if self.messageType == .gift {
                giftItem = GiftModel.deserialize(from: json?.dictionaryObject ?? [:])   
            }
            
            if self.messageType == .meetSuccess {
                cardModel = UserInfoModel.deserialize(from: json?.dictionaryObject ?? [:])
            }
            
            if self.messageType == .friendAnswerReply {
                wdModel = TacitAnswerModel.deserialize(from: json?.dictionaryObject ?? [:])
            }
            
            if self.messageType == .friendAnswerProblem {
                tacitModel = TacitModel.deserialize(from: json?.dictionaryObject ?? [:], designatedPath: "data")
                tacitModel?.businessId = json?["businessId"].intValue ?? 0
            }
            
            if self.messageType == .follow {
                tipContent = json?["remarks"].rawString()
            }
            
            if self.messageType == .liveInvite {
                inviteM = RoomInviteModel.deserialize(from: json?.dictionaryObject ?? [:])
            }
            
            if self.messageType == .skill {
                skillModel = ChatSkillModel.deserialize(from: json?.dictionaryObject ?? [:])
            }
            
        case .V2TIM_ELEM_TYPE_TEXT:
            messageType = .text
        case .V2TIM_ELEM_TYPE_IMAGE:
            messageType = .image
        case .V2TIM_ELEM_TYPE_SOUND:
            messageType = .voice
        default:
            messageType = .unkonw
        }
        
        if self.messageType == .unkonw {
            tipContent = "未知消息"
        }
        
        self.calculatedHeight = calculateHeight()
    }
    
    
    private func calculateHeight() -> CGFloat {
        switch messageType {
        case .text:
            return calculateTextHeight(msg?.textElem?.text ?? "")
        case .image:
            return calculateImageSize(imageElem: msg?.imageElem).height
        case .voice:
            return 60 // 语音气泡固定高度
        case .tipsContent, .unkonw:
            return calculateTipHeight()
        case .gift:
            return 80
        case .meetSuccess:
            return 250
        case .friendAnswerReply:
            return calculateTextHeight(wdModel?.text ?? "")
        case .friendAnswerProblem:
            return 300
        default:
            return 50 // 其他类型默认固定高度
        }
    }
    
    
    private func calculateTextHeight(_ text: String) -> CGFloat {
        var height = text.app_height(forWidth: 230, font: .regular(14)) + 10
        height = max(40, height)
        return ceil(height) + 20 // 加上 padding
    }
    
    private func calculateTipHeight() -> CGFloat {
        var height = (tipContent?.app_height(forWidth: 260, font: .regular(12)) ?? 0) + 25
        height = max(30, height)
        return ceil(height)
    }
    
    
    /// 计算图片消息的尺寸
    func calculateImageSize(imageElem: V2TIMImageElem?) -> CGSize {
        guard let imageList = imageElem?.imageList, !imageList.isEmpty else {
            return CGSize(width: 100, height: 100)  // 默认图片大小
        }
        
        // 取 `type == 1`（原图）或者 `type == 2`（缩略图）
        let image = imageList.first { $0.type == .V2TIM_IMAGE_TYPE_ORIGIN } ?? imageList.first { $0.type == .V2TIM_IMAGE_TYPE_THUMB }
        
        guard let width = image?.width, let height = image?.height, width > 0, height > 0 else {
            return CGSize(width: 100, height: 100)
        }
        
        // 限制最大宽度和最大高度
        let maxWidth: CGFloat = 110
        let maxHeight: CGFloat = 220
        
        // 计算原始宽高比
        let aspectRatio = CGFloat(height) / CGFloat(width)
        
        // 计算缩放后尺寸
        var newWidth = CGFloat(width)
        var newHeight = CGFloat(height)
        
        if newWidth > maxWidth {
            newWidth = maxWidth
            newHeight = newWidth * aspectRatio
        }
        
        if newHeight > maxHeight {
            newHeight = maxHeight
            newWidth = newHeight / aspectRatio
        }
        
        return CGSize(width: newWidth, height: newHeight)
    }


    
}


class ClapUserModel: SmartCodable {
    var imNumber: String?
    var nickName: String?
    var type: Int = 0

    required init() {}
    
    var tipContent: String {
        return imNumber != kuser.imNumber.string ? "我拍了拍 “\(nickName ?? "")“" : "对方拍了拍我～"
    }
}


struct RoomInviteModel: SmartCodable {
    var roomCover: String?
    var roomId: String?
    var roomTitle: String?
    var roomType: Int = 0
    var type: Int = 0
    var nickName: String?
    var remarks: String?
    var liveRoomNo: String?
    
}

struct ChatSkillModel: SmartCodable {
    var skillType: SkillType = .skill1                         // 技能类型
    var skillLevel: Int = 0                        // 技能等级
    var userImNumber: String = ""                  // 用户 IM 编号
    var userNickName: String = ""                  // 用户昵称
    var value: String = ""                         // 附加值（例如时间或次数）
    var hitReault: Int = 0                         // 是否击中（0为成功，1为失败）
    var personPoints: Double = 0.0                 // 获得/失去的经验值
    var charm: Double = 0.0                        // 魅力值变化
    var powerPoints: Double = 0.0                  // 战神值变化

    var triggerBreak: Int = 0                      // 是否打出突破技 0是 1否
    var yuanbao: Int = 0                           // 元宝值
    var meatShield: Int = 0                        // 是否触发肉盾效果 0是 1否
    var negativeEffectName: String? = ""           // 净化的负面效果名称
    var randomSkillName: String = ""               // 被封印的技能名称
    var chaosTime: Int = 0                         // 封印时间（分钟）

    var runeFengyin: Int = 1                       // 是否触发符石封印 0是 1否
    var runeTianmi: Int = 1                        // 是否触发符石甜蜜 0是 1否
    var runeFanjiMsg: Int = 1                      // 是否为反击消息 0是 1否

    /// 是否击中成功（true = 成功）
    var success: Bool {
        return hitReault == 0
    }

    /// 获取经验值（取整）
    func getLongPersonPoints() -> Int64 {
        return Int64(personPoints)
    }

    /// 获取魅力值（取整）
    func getLongCharm() -> Int64 {
        return Int64(charm)
    }

    /// 获取战神值（取整）
    func getLongPowerPoints() -> Int64 {
        return Int64(powerPoints)
    }

    /// 获取符石效果描述（甜蜜）
    func getRuneStoneHitDesc() -> String {
        return runeTianmi == 0 ? "，恋爱指数+1" : ""
    }

    /// 获取符石效果描述（封印）
    func getRuneStoneHitDesc2(isSelf: Bool = false) -> String {
        guard runeFengyin == 0 else { return "" }
        let prefix = isSelf ? "" : "对方"
        return "，\(prefix)\(skillType.title)被封印5分钟"
    }
}
