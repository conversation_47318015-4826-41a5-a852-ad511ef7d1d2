//
//  SkillListVC.swift
//  YINDONG
//
//  Created by jj on 2025/5/13.
//

import UIKit
import JXSegmentedView

enum SkillType: Int, SmartCaseDefaultable {
    ///旋风腿
    case skill1 = 1
    ///点穴手
    case skill2 = 2
    ///变猪头
    case skill3 = 3
    ///套麻袋
    case skill4 = 4
    ///么么哒
    case skill5 = 5
    
    static var allType: [SkillType] = [.skill1, .skill5, .skill2, .skill3, .skill4]
    
    var imgName: String {
        switch self {
        case .skill1:
            return "icon_skill_xuanfengtui"
        case .skill2:
            return "icon_skill_dianxueshou"
        case .skill3:
            return "icon_skill_bianzhutou"
        case .skill4:
            return "icon_skill_taomadai"
        case .skill5:
            return "icon_skill_memeda"
        }
    }
    
    var title: String {
        switch self {
        case .skill1: return "旋风腿"
        case .skill5: return "么么哒"
        case .skill2: return "点穴手"
        case .skill3: return "变猪头"
        case .skill4: return "套麻袋"
            
        }
    }
}


class SkillListVC: YinDBaseVC {
    
    var type: SkillType = .skill1
    var skillModel: SkillItemModel?
    
    // 容器滚动视图
    private let scrollView = UIScrollView()
    // 主容器 StackView
    private let containerStack = UIStackView()
    
    // 子视图组件
    let vv = LipPowerView()
    let vv1 = UpgradeCostView()
    let pointsExchangeView = PointsExchangeView()
    
    lazy var skillListV: UpgradeConditionView = {
        let vv = UpgradeConditionView()
        vv.isHidden = true
        return vv
    }()
    
    lazy var tuPoV: TuPoView = {
        let vv = TuPoView()
        vv.isHidden = true
        return vv
    }()
    
    
    var type1: String {
        return vv1.selectedConsume == 0 ? "greenCrystal" : "purpleCrystal"
    }
    
    var type2: String {
        return vv1.selectedCost == 0 ? "yuanbao" : "diamond"
    }
    
    // 底部按钮
    var openBtn = UIButton(title: "", isDefault: true, size: CGSize(width: AppTool.screenWidth - 32, height: 48))
    
    
    lazy var infoItemView: InfoItemBaseView = {
        let vv = InfoItemBaseView(rightImg: nil, leftTitle: "---- 已到达最高等级 ----")
        vv.titleLab.font = .regular(14)
        vv.titleLab.textColor = .textColor6
        vv.isHidden = true
        return vv
    }()
    
    
    var infoModel: UserRechargeModel? {
        didSet {
            guard let infoModel = infoModel else { return }
            
            vv1.consumeTitle.attributedText = vv1.makeTitle("升级消耗", greenCount: infoModel.greenCrystal, purpleCount: infoModel.purpleCrystal)
            vv1.costTitle.attributedText = vv1.makeTitle("升级花费", greenCount: infoModel.yuanbao, purpleCount: Int(LWUserManger.shared.rechargeModel?.diamond ?? 0), greenIconName: "icon_yunabao", purpleIconName: "zuan_coinicon")
            pointsExchangeView.setPoints(infoModel.upgradePoints)
            tuPoV.valueLabel.text = "\(infoModel.breakStone)"
            vv1.payInfo = infoModel
        }
    }

    
    lazy var tupoTipLab: UILabel = {
        let labe = UILabel(withText: "", numberOfLines: 2, textAlignment: .center, fontType: .bold(20))
        labe.textColor = .white
        labe.attributed.text = "突破\n\("剩余技能等级未达到要求", .font(.medium(12)))"
        labe.isHidden = true
        return labe
    }()

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        
        loadData()
    }
    
    override func loadData() {
        
        NetworkUtility.request(target: .userSkillInfo(["type": type.rawValue]), model: SkillItemModel.self) { result in
            if result.isError { return }
            
            var itemModel = result.model
            self.skillModel = itemModel
            self.reloadData()
        }
        
        guard let infoModel = infoModel else { return }
        
        self.infoModel = infoModel
    }
    
    func reloadData() {
        guard let itemModel = skillModel else { return }
        vv.skillModel = itemModel
        
        
        if !isValid(itemModel.nextSkillLevel) {
            vv1.isHidden = true
            openBtn.isHidden = true
            pointsExchangeView.isHidden = true
            infoItemView.isHidden = false
            return
        }
        
        openBtn.setTitle(itemModel.curSkillLevel == 0 ? "开通" : "升级", for: .normal)
        vv1.skillModel = itemModel
        
        skillListV.titleLabel.text = "突破条件(以下技能等级达到Lv.\(itemModel.breakCondition)"
        skillListV.configure(with: itemModel.elseSkillsList, maxValue: itemModel.breakCondition)
        skillListV.isHidden = true
        tuPoV.isHidden = true
        
        tuPoV.skillModel = itemModel
        
        if itemModel.curSkillLevel == 0 {
            vv1.isHidden = true
            pointsExchangeView.isHidden = true
        } else {
            vv1.isHidden = false
            pointsExchangeView.isHidden = false
        }
        
        if let consumeBreakStone = itemModel.consumeBreakStone, consumeBreakStone > 0 {
            vv1.isHidden = true
            skillListV.isHidden = false
            tuPoV.isHidden = false
        }
        
        checkBreakCondition()
    }
    
    ///检查是否需要突破
    func checkBreakCondition() {
        let data = skillModel?.elseSkillsList ?? []
        let breakLevel = skillModel?.breakCondition ?? 0

        var isCompleteCondition = true
        for item in data {
            if item.skillLevel < breakLevel {
                isCompleteCondition = false
                break
            }
        }

        // 如果有技能未达到条件，按钮透明度降低
        if isCompleteCondition {
            openBtn.isEnabled = true
            openBtn.alpha = 1.0
            tupoTipLab.isHidden = true
            openBtn.setTitle( skillModel?.curSkillLevel ?? 0 == 0 ? "开通" : "升级", for: .normal)
        } else {
            openBtn.isEnabled = false
            openBtn.alpha = 0.6
            tupoTipLab.isHidden = false
            openBtn.setTitle("", for: .normal)
        }
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        view.backgroundColor = .clear
        setupScrollView()
        setupStackView()
        setupButtons()
    }

    
    private func setupScrollView() {
        // 设置滚动视图
        view.addSubview(scrollView)
        scrollView.showsVerticalScrollIndicator = false
        scrollView.contentInsetAdjustmentBehavior = .never
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-70) // 为底部按钮留出空间
        }
    }
    
    private func setupStackView() {
        // 设置主容器 StackView
        containerStack.axis = .vertical
        containerStack.spacing = 12
        containerStack.alignment = .fill
        containerStack.distribution = .fill
        scrollView.addSubview(containerStack)
        
        // 让 StackView 宽度固定，高度自适应
        containerStack.snp.makeConstraints { make in
            make.top.equalTo(scrollView.contentLayoutGuide.snp.top).offset(12)
            make.left.right.equalTo(scrollView.frameLayoutGuide).inset(16)
            make.bottom.equalTo(scrollView.contentLayoutGuide.snp.bottom)
            make.width.equalTo(scrollView.frameLayoutGuide).offset(-32) // 左右各16点间距
        }
        
        // 添加子视图到 StackView
        containerStack.addArrangedSubview(vv)
        containerStack.addArrangedSubview(vv1)
        containerStack.addArrangedSubview(tuPoV)
        containerStack.addArrangedSubview(skillListV)
        containerStack.addArrangedSubview(pointsExchangeView)
//        containerStack.addArrangedSubview(infoItemView)
        view.addSubview(infoItemView)
        infoItemView.snp.makeConstraints { make in
            make.height.equalTo(30)
            make.centerX.equalToSuperview()
            make.top.equalTo(containerStack.snp.bottom).offset(8)
        }
        
    }
    
    private func setupButtons() {
        // 设置底部按钮
        view.addSubview(openBtn)
        openBtn.addTarget(self, action: #selector(shengji))
        openBtn.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(48)
            make.bottom.equalTo(-(AppTool.safeAreaBottomHeight + 10))
        }
        
        openBtn.addSubview(tupoTipLab)
        tupoTipLab.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        // 添加按钮点击动画
        openBtn.addTarget(self, action: #selector(buttonTouchDown), for: .touchDown)
        openBtn.addTarget(self, action: #selector(buttonTouchUp), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }
    
    // 按钮触摸动画
    @objc private func buttonTouchDown() {
        UIView.animate(withDuration: 0.1) {
            self.openBtn.transform = CGAffineTransform(scaleX: 0.97, y: 0.97)
        }
    }
    
    @objc private func buttonTouchUp() {
        UIView.animate(withDuration: 0.1) {
            self.openBtn.transform = .identity
        }
    }
    
    @objc
    func shengji() {
        
        var pp = creatDict()
        var types = [type1, type2]
        
        if vv1.protectCheckbox.isSelected {
            types.append("reinforcedStone")
        }
        
        ///当前属于突破阶段
        if skillModel?.consumeBreakStone ?? 0 > 0 {
            types = ["breakStone"]
        }
        
        pp["skillType"] = type.rawValue
        pp["materialList"] = types
        
        NetworkUtility.request(target: .upgradeSkill(pp)) { result in
            if result.isError {
                return
            }
            
            if self.skillModel?.curSkillLevel == 0 {
                ProgressHUDManager.showTextMessage("开通成功")
            } else {
                let a = result.dataJson?["upgradeMsg"].rawString()
                ProgressHUDManager.showTextMessage(a)
            }
            
            NotificationCenter.default.post(name: .skillUpNotification, object: nil)
            self.loadData()
        }
        
    }

}
extension SkillListVC: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}
