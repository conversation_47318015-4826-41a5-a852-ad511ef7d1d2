//
//  PkResultPopView.swift
//  YINDONG
//
//  Created by jj on 2025/6/17.
//

import UIKit
import Lottie

class PkResultPopView: CustomPopupView {
    
    var msgModel: RoomBaseMsgModel? {
        didSet {
            guard let msgModel = msgModel, let type = msgModel.skillType else { return }
            
            if let file = GameUtils.getSkillLottieFileName(skillType: type, success: true) {
                lottieView.animation = LottieAnimation.named(file)
                lottieView.play()
            }
            
            if type == .skill5 {
                buttons.last?.setTitle("回馈Ta", for: .normal)
            }
            
            contentLab.attributed.text = """
你被\(msgModel.userNickName ?? "", .foreground(.themColor ?? .white), .action({
            LWJumpManger.goUserPage(id: msgModel.userImNumber)
}))\(GameUtils.skillHitDesc(model: msgModel, breakSkillHitDesc: msgModel.breakSkillHitDesc()))
"""
        }
    }
    
    
    var buttons: [UIButton] = []

    
    lazy var lottieView: LottieAnimationView = {
        let imgV = LottieAnimationView()
        imgV.loopMode = .loop
        imgV.animationSpeed = 0.5
        return imgV
    }()
    
    lazy var contentLab: UILabel = {
        let lab = UILabel(withText: "", textColor: .textColor3, fontType: .regular(14))
        lab.numberOfLines = 0
        return lab
    }()

    override func configureUI() {
        super.configureUI()
        
        self.frame = CGRect(x: 0, y: 0, width: 299, height: 248)
        
        addSubview(lottieView)
        addSubview(contentLab)
        
        lottieView.layerCornerRadius = 8
        lottieView.snp.makeConstraints { make in
            make.width.height.equalTo(77)
            make.centerX.equalToSuperview()
            make.top.equalTo(24)
        }
        
        contentLab.snp.makeConstraints { make in
            make.top.equalTo(lottieView.snp.bottom).offset(15)
            make.left.right.equalToSuperview().inset(20)
        }
        
        let otherTitles = ["忍了", "报仇"]
        
        // 右侧按钮（盘Ta和送礼，各占右侧一半）
        let rightButtonWidth = 119.0
        for i in 0..<otherTitles.count {
            let button = UIButton(title: otherTitles[i], font: .regular(14), color: i == 0 ? .textColor3 : .white)
            button.backgroundColor = i == 0 ? .f8Color : UIImage(dvt: [.init(hex: 0xFD6476), .init(hex: 0xFB816C)], size: CGSizeMake(rightButtonWidth, 42), direction: .left2right)?.imageToColor()
            button.layerCornerRadius = 21
            button.addTarget(self, action: #selector(buttonTap(btn: )))
            buttons.append(button)
            addSubview(button)
        }
        
        buttons[0].snp.makeConstraints { make in
            make.bottom.equalTo(-24)
            make.left.equalTo(20)
            make.width.equalTo(119)
            make.height.equalTo(42)
        }
        
        buttons[1].snp.makeConstraints { make in
            make.bottom.equalTo(-24)
            make.right.equalTo(-20)
            make.size.equalTo(buttons[0])
        }

    }
    
    @objc
    func buttonTap(btn: UIButton) {
        if btn.currentTitle == "忍了" {
            dismiss()
            return
        }
        let pk = UserPkSheetView(uid: msgModel?.userImNumber?.string ?? "", name: msgModel?.userNickName, defaultType: btn.currentTitle == "回馈Ta" ? .skill5 : nil, onlyOne: true)
        pk.present(in: AppTool.getCurrentViewController().view)
    }

}
