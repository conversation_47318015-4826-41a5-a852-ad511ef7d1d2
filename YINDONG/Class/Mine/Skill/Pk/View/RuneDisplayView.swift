//
//  RuneDisplayView.swift
//  YINDONG
//
//  Created by jj on 2025/6/20.
//

import UIKit
import SnapKit

class RuneDisplayView: UIView {
    
    var skillModel: SkillInfoModel?
    
    // MARK: - Properties
    private var defaultImageViews: [UIImageView] = []
    private var svgaViews: [SVGAnimationPlayer] = []
    private let infoButton = UIButton(type: .infoLight)
    private let slotCount = 5
    private let defaultImage = UIImage(named: "icon_default_rune_tone") // 默认图片
    private let spacing: CGFloat = 1 // 卡槽间距
    
    // 可配置的卡槽大小，默认 15x15
    var slotSize: CGSize = CGSize(width: 15, height: 15) {
        didSet {
            updateConstraints1()
        }
    }
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)

    }
    
    convenience init(slotSize: CGSize = CGSize(width: 15, height: 15)) {
        self.init(frame: .zero)
        self.slotSize = slotSize
        updateConstraints()
    }
    
    // MARK: - Setup
    private func setupViews() {
        // 创建5个默认图片视图和5个SVGA视图，一次性创建完成
        for i in 0..<slotCount {
            // 默认图片视图
            let imgV = UIImageView()
            imgV.image = defaultImage
            imgV.contentMode = .scaleAspectFit
            imgV.backgroundColor = UIColor.clear
            imgV.tag = i
            imgV.isHidden = false // 默认显示
            defaultImageViews.append(imgV)
            addSubview(imgV)
            
            // SVGA视图
            let svgaView = SVGAnimationPlayer()
            svgaView.tag = i
            svgaView.isHidden = true // 默认隐藏
            svgaViews.append(svgaView)
            addSubview(svgaView)
        }
        
        // 设置信息按钮
        infoButton.tintColor = .white
        infoButton.addTarget(self, action: #selector(infoTapped), for: .touchUpInside)
        addSubview(infoButton)
    }
    
    private func setupConstraints() {
        // 设置每个卡槽的约束（默认图片和SVGA视图共用相同位置）
        for index in 0..<slotCount {
            let defaultImageView = defaultImageViews[index]
            let svgaView = svgaViews[index]
            
            // 默认图片视图约束
            defaultImageView.snp.makeConstraints { make in
                make.size.equalTo(slotSize)
                make.centerY.equalToSuperview()
                
                if index == 0 {
                    make.leading.equalToSuperview()
                } else {
                    make.leading.equalTo(defaultImageViews[index - 1].snp.trailing).offset(spacing)
                }
            }
            
            // SVGA视图约束（与默认图片视图重叠）
            svgaView.snp.makeConstraints { make in
                make.edges.equalTo(defaultImageView)
            }
        }
        
        // 设置信息按钮约束
        infoButton.snp.makeConstraints { make in
            make.size.equalTo(slotSize)
            make.centerY.equalToSuperview()
            make.leading.equalTo(defaultImageViews.last!.snp.trailing).offset(spacing)
            make.trailing.equalToSuperview()
        }
        
        // 设置整体视图的约束
        self.snp.makeConstraints { make in
            let totalWidth = CGFloat(slotCount) * slotSize.width + CGFloat(slotCount) * spacing + slotSize.width
            let totalHeight = slotSize.height
            make.width.equalTo(totalWidth)
            make.height.equalTo(totalHeight)
        }
    }
    
    private func updateConstraints1() {
        // 更新默认图片视图大小约束
        for defaultImageView in defaultImageViews {
            defaultImageView.snp.updateConstraints { make in
                make.size.equalTo(slotSize)
            }
        }
        
        // 更新信息按钮大小约束
        infoButton.snp.updateConstraints { make in
            make.size.equalTo(slotSize)
        }
        
        // 更新整体视图约束
        self.snp.updateConstraints { make in
            let totalWidth = CGFloat(slotCount) * slotSize.width + CGFloat(slotCount) * spacing + slotSize.width
            let totalHeight = slotSize.height
            make.width.equalTo(totalWidth)
            make.height.equalTo(totalHeight)
        }
    }
    var listModel: SkillListModel?
    // MARK: - Public Methods
    func configure(with runeList: [RuneListModel], skillType: SkillListModel?) {
        self.listModel = skillType
        // 重置所有卡槽为默认状态
        resetAllSlots()
        
        // 根据 runeList 更新对应位置的卡槽
        for rune in runeList {
            let position = rune.positionNo - 1 // 假设 positionNo 从1开始，数组从0开始
            if position >= 0 && position < slotCount && rune.skillType == skillType?.id {
                showSVGAAt(position: position, with: rune)
            }
        }
    }
    
    private func resetAllSlots() {
        // 显示所有默认图片，隐藏所有SVGA
        for i in 0..<slotCount {
            defaultImageViews[i].isHidden = false
            defaultImageViews[i].image = defaultImage // 重置为默认图片
            svgaViews[i].isHidden = true
            svgaViews[i].stopAnimation() // 停止可能正在播放的动画
        }
    }
    
    private func showSVGAAt(position: Int, with rune: RuneListModel) {
        guard position < slotCount else { return }
        
        // 根据URL后缀判断是SVGA还是图片
        if let urlString = rune.url?.lowercased() {
            if urlString.hasSuffix(".svga") {
                // 是SVGA动画，隐藏默认图片，显示SVGA
                defaultImageViews[position].isHidden = true
                svgaViews[position].isHidden = false
                loadSVGAAnimation(for: svgaViews[position], with: rune)
            } else {
                // 是普通图片，在默认图片视图中加载
                svgaViews[position].isHidden = true
                defaultImageViews[position].isHidden = false
                loadImageForSlot(at: position, with: rune)
            }
        } else {
            // 没有URL，保持默认状态
            svgaViews[position].isHidden = true
            defaultImageViews[position].isHidden = false
        }
    }
    
    private func loadSVGAAnimation(for svgaView: SVGAnimationPlayer, with rune: RuneListModel) {
        // 根据符石数据加载对应的SVGA动画
        if let urlString = rune.url, let url = URL(string: urlString) {
            svgaView.isAutoPlayEnabled = true
            svgaView.parseAndStartAnimation(from: url)
        }
    }
    
    private func loadImageForSlot(at position: Int, with rune: RuneListModel) {
        // 加载普通图片到指定位置的默认图片视图
        guard position < defaultImageViews.count else { return }
        
        let imageView = defaultImageViews[position]
        imageView.setImage(from: rune.url)
    }
    
    
        // MARK: - Actions
    @objc private func infoTapped() {
        // 获取当前符石数据
        var runeList: [RuneListModel] = skillModel?.toUserRuneList ?? []
        var leve = skillModel?.otherSkillLevel
        if tag == 0 {
            runeList = skillModel?.userRuneList ?? []
            leve = skillModel?.userSkillLevel
        }
        runeList = runeList.filter({ $0.skillType == listModel?.id })
        
        
        
        // 创建并显示弹窗
        let infoListView = RuneDisplayInfoListView(targetView: infoButton, runeList: runeList, level: leve ?? 0)
        infoListView.tag = tag
        infoListView.show()
    }
}
