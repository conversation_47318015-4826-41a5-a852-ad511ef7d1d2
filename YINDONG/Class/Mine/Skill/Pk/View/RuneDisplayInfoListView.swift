//
//  RuneDisplayInfoListView.swift
//  YINDONG
//
//  Created by jj on 2025/6/23.
//

import UIKit
import SnapKit

class RuneDisplayInfoListView: UIView {
    
    // MARK: - Properties
    private var targetView: UIView?
    private var runeList: [RuneListModel] = [] // 符石列表
    
    
    private let minHeight: CGFloat = 67
    private let maxHeight: CGFloat = 217
    private let estimatedItemHeight: CGFloat = 70
    private let maxVisibleItems = 3
    
    // MARK: - UI Components
    lazy var bgImgView: UIImageView = {
        let imgV = UIImageView(image: UIImage(named: "me_runeBg")?.createResizableImageCentered())
        imgV.isUserInteractionEnabled = true
        return imgV
    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.backgroundColor = .clear
        table.separatorStyle = .none
        table.delegate = self
        table.dataSource = self
        table.showsVerticalScrollIndicator = false
        table.estimatedRowHeight = estimatedItemHeight
        table.rowHeight = UITableView.automaticDimension
        table.register(RuneInfoCell.self, forCellReuseIdentifier: "RuneInfoCell")
        return table
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .regular(12)
        label.textAlignment = .center
        label.isHidden = true
        return label
    }()
    
    var level: Int?
    
    // MARK: - Initialization
    convenience init(targetView: UIView, runeList: [RuneListModel], level: Int?) {
        self.init(frame: .zero)
        self.targetView = targetView
        self.runeList = runeList
        self.level = level
        setupViews()
        setupGestures()
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    // MARK: - Setup
    private func setupViews() {
        backgroundColor = UIColor.black.withAlphaComponent(0.1) // 半透明背景
        addSubview(bgImgView)
        bgImgView.addSubview(tableView)
        bgImgView.addSubview(titleLabel)
    }
    
    private func setupConstraints() {
        guard let targetView = targetView else { return }
        
        // 获取目标视图在 Window 中的位置
        guard let window = AppTool.firstWindow,
              let targetSuperview = targetView.superview else { return }
        
        let targetFrameInWindow = targetSuperview.convert(targetView.frame, to: window)
        
        // 计算弹窗高度
        let contentHeight = calculateContentHeight()
        let popupHeight = contentHeight + 20 // 加上内边距
        
        // 计算弹窗位置（在目标视图上方）
        let popupY = targetFrameInWindow.minY - popupHeight - 10 // 上方10pt间距
        
        // 计算 X 轴位置，根据 tag 判断左右，并检查屏幕边界
        let popupWidth: CGFloat = 209 // 弹窗宽度
        let screenWidth = UIScreen.main.bounds.width
        var popupX: CGFloat
        
        if tag == 0 {
            // 左边：以目标视图左边为基准
            popupX = targetFrameInWindow.minX
            // 检查是否超出左边界
            if popupX < 15 {
                popupX = 15
            }
        } else {
            // 右边：以目标视图右边为基准，弹窗在右侧
            popupX = targetFrameInWindow.maxX - popupWidth
            // 检查是否超出右边界
            if popupX + popupWidth > screenWidth - 15 {
                popupX = screenWidth - popupWidth - 15
            }
        }
        
        // 注意：不在这里设置 self 的约束，因为此时还没添加到父视图
        let name = tag == 0 ? "me_runeBg" : "other_runeBg"
        bgImgView.image = UIImage(named: name)?.createResizableImageCentered()
        // 设置弹窗背景约束
        bgImgView.snp.makeConstraints { make in
            make.width.equalTo(popupWidth)
            make.height.equalTo(popupHeight)
            make.left.equalToSuperview().offset(popupX)
            make.top.equalToSuperview().offset(max(10, popupY))
        }
        
        
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.lessThanOrEqualTo(198)
        }
        
        // 设置表格约束
        tableView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(8)
            make.left.right.equalToSuperview()
        }
    }
    
    private func setupGestures() {
        // 点击背景消失
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        tapGesture.delegate = self
        addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Public Methods
    func show() {
        guard let window = AppTool.firstWindow else { return }
        
        // 先添加到 window
        window.addSubview(self)
        
        // 设置全屏约束
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        if let level = level, level < 50 {
            titleLabel.isHidden = false
            titleLabel.text = "技能等级达到50级开启符石功能"
        }
        
        if let level = level, level >= 50, runeList.count == 0 {
            titleLabel.isHidden = false
            titleLabel.text = "未镶嵌符石"
        }
        
        // 现在设置子视图约束
        setupConstraints()
        
        // 动画显示
        alpha = 0
        bgImgView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        
        UIView.animate(withDuration: 0.25, delay: 0, options: .curveEaseOut) {
            self.alpha = 1
            self.bgImgView.transform = .identity
        }
    }
    
    func dismiss() {
        UIView.animate(withDuration: 0.2, delay: 0, options: .curveEaseIn) {
            self.alpha = 0
            self.bgImgView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        } completion: { _ in
            self.removeFromSuperview()
        }
    }
    
    // MARK: - Private Methods
    private func calculateContentHeight() -> CGFloat {
        let itemCount = runeList.count
        if itemCount == 0 {
            return minHeight
        } else if itemCount <= maxVisibleItems {
            return max(minHeight, CGFloat(itemCount) * estimatedItemHeight + 30) // 30是标题和间距
        } else {
            return maxHeight
        }
    }
    
    // MARK: - Actions
    @objc private func backgroundTapped() {
        dismiss()
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate
extension RuneDisplayInfoListView: UITableViewDataSource, UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return runeList.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "RuneInfoCell", for: indexPath) as! RuneInfoCell
        let rune = runeList[indexPath.row]
        cell.configure(with: rune)
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
}

// MARK: - UIGestureRecognizerDelegate
extension RuneDisplayInfoListView: UIGestureRecognizerDelegate {
    
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        // 只有点击背景时才响应，点击弹窗内容不响应
        let touchPoint = touch.location(in: self)
        let bgFrame = bgImgView.frame
        return !bgFrame.contains(touchPoint)
    }
}

// MARK: - RuneInfoCell
class RuneInfoCell: UITableViewCell {
    
    private lazy var cellBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        view.layer.cornerRadius = 8
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.white.withAlphaComponent(0.2).cgColor
        return view
    }()
    
    private lazy var iconImageView: UIImageView = {
        let imgV = UIImageView()
        imgV.contentMode = .scaleAspectFit
        imgV.backgroundColor = .clear
        return imgV
    }()
    
    private lazy var svgaView: SVGAnimationPlayer = {
        let player = SVGAnimationPlayer()
        player.isHidden = true
        return player
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .medium(12)
        return label
    }()
    
    private lazy var levelLabel: InfoItemBaseView = {
        let label = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 4)
        label.titleLab.textColor = .white.withAlphaComponent(0.8)
        label.titleLab.font = .numFont(10)
        return label
    }()
    
    private lazy var descLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white.withAlphaComponent(0.7)
        label.font = .regular(10)
        label.numberOfLines = 0 // 允许多行
        label.lineBreakMode = .byWordWrapping
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupCell()
    }
    
    private func setupCell() {
        backgroundColor = .clear
        selectionStyle = .none
        
        // 添加背景视图
        contentView.addSubview(cellBackgroundView)
        cellBackgroundView.addSubview(iconImageView)
        cellBackgroundView.addSubview(svgaView)
        cellBackgroundView.addSubview(titleLabel)
        cellBackgroundView.addSubview(levelLabel)
        cellBackgroundView.addSubview(descLabel)
        
        // Cell 背景约束（留出间距）
        cellBackgroundView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(4)
            make.left.equalToSuperview().offset(8)
            make.right.equalToSuperview().offset(-8)
            make.bottom.equalToSuperview().offset(-4)
        }
        
        // 符石图标约束
        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalToSuperview().offset(7)
            make.size.equalTo(22)
        }
        
        // SVGA 视图约束
        svgaView.snp.makeConstraints { make in
            make.edges.equalTo(iconImageView)
        }
        
        // 符石名称约束
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(3)
            make.centerY.equalTo(iconImageView)
        }
        
        // 等级标签约束（右上角）
        levelLabel.layerCornerRadius = 5.5
        levelLabel.backgroundColor = .init(hex: 0xB2D2FB)?.withAlphaComponent(0.2)
        levelLabel.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.left.equalTo(titleLabel.snp.right).offset(6)
            make.height.equalTo(11)
        }
        
        // 描述文字约束
        descLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(3)
            make.left.right.equalToSuperview().inset(12)
            make.bottom.equalToSuperview().offset(-8)
        }
    }
    
    func configure(with rune: RuneListModel) {
        // 重置状态
        svgaView.stopAnimation()
        
        // 设置符石名称
        titleLabel.text = rune.runeName ?? "未知符石"
        
        // 设置符石等级
        levelLabel.titleLab.text = "Lv.\(rune.level)"
        levelLabel.isHidden = rune.level == 0
        
        if levelLabel.isHidden {
            iconImageView.snp.remakeConstraints { make in
                make.width.equalTo(50)
                make.height.equalTo(22)
                make.left.equalToSuperview().offset(12)
                make.top.equalToSuperview().offset(7)
            }
        } else {
            iconImageView.snp.remakeConstraints { make in
                make.left.equalToSuperview().offset(12)
                make.top.equalToSuperview().offset(7)
                make.size.equalTo(22)
            }
        }
        
        let rate = rune.attributeValue * (rune.attributeValue > 1 ? 1 : 100)

        var formattedRate = String(format: "%.2f", rate)
        if rune.attributeValue > 1 {
            formattedRate = "\(rate.int)"
        }

        let rateString = rune.runeAttributeDesc?.replacingOccurrences(of: "x", with: "\(formattedRate)")
        // 设置符石描述
        descLabel.text = rateString ?? ""
        
        // 根据URL后缀判断是SVGA还是图片
        if let urlString = rune.url?.lowercased() {
            if urlString.hasSuffix(".svga") {
                // 是SVGA动画，显示SVGA，隐藏图片
                iconImageView.isHidden = true
                svgaView.isHidden = false
                if let url = URL(string: rune.url ?? "") {
                    svgaView.isAutoPlayEnabled = true
                    svgaView.parseAndStartAnimation(from: url)
                }
            } else {
                // 是普通图片，显示图片，隐藏SVGA
                svgaView.isHidden = true
                iconImageView.isHidden = false
                iconImageView.setImage(from: rune.url)
            }
        } else {
            // 没有URL，显示默认图片
            svgaView.isHidden = true
            iconImageView.isHidden = false
            iconImageView.image = UIImage(named: "icon_default_rune_tone")
        }
    }
}
