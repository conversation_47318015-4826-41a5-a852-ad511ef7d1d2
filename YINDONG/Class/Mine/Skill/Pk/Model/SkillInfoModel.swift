//
//  SkillInfoModel.swift
//  YINDONG
//
//  Created by jj on 2025/6/13.
//

import UIKit

struct SkillInfoModel: SmartCodable {
    var blindnessFlag: Int = 0
    var chaosFlag: Int = 0
    var charm: Int = 0
    var meatShieldFlag: Int = 0
    var otherAddSkillLevel: Int = 0
    var otherBaseRate: Int = 0
    var otherCalSkillLevel: Int = 0
    var otherEffectSkillLevel: Int = 0
    var otherRuneRate: Float = 0.0
    var otherSkillLevel: Int = 0
    var otherSkillRate: Int = 0
    var totalRate: Int = 0
    var toUserIm: Int = 0
    var toUserPhoto: String?
    var userAddSkillLevel: Int = 0
    var userBaseRate: Int = 0
    var userCalSkillLevel: Int = 0
    var userEffectSkillLevel: Int = 0
    var userRuneRate: Float = 0.0
    var userSkillDescriptionList = [String]()
    var userSkillLevel: Int = 0
    var userSkillRate: Int = 0
    //对方的符石
    var toUserRuneList = [RuneListModel]()
    //自己的符石
    var userRuneList = [RuneListModel]()
    
    var otherNobleAddLevel: Int = 0
    var otherNobleId: Int = 0
    var userNobleId: Int = 0
    var userNobleAddLevel: Int = 0
    
    //总的等级
    var meAllLevel: Int {
        return userSkillLevel + userAddSkillLevel
    }
    
    //总的等级
    var toAllLevel: Int {
        return otherSkillLevel + otherAddSkillLevel
    }
    
    /// 是否存在突破技（封印或致盲）
    func selfShowBreakFlag() -> Bool {
        return blindnessFlag == 0 || chaosFlag == 0
    }
    
    /// 获取突破技文本，优先封印
    func selfBreakFlagText() -> String {
        if chaosFlag == 0 {
            return "封印"
        } else if blindnessFlag == 0 {
            return "致盲"
        }
        return ""
    }
    
    /// 上部分技能效果描述
    func getSkillLevelDescription1() -> String {
        return userSkillDescriptionList.first ?? ""
        
    }
    
    /// 下部分及突破技效果描述
    func getSkillLevelDescription2() -> String {
        guard !userSkillDescriptionList.isEmpty else { return "" }
        var desc = userSkillDescriptionList.count > 1 ? userSkillDescriptionList[1] : ""
        if userSkillDescriptionList.count > 2 {
            desc += "\n" + userSkillDescriptionList[2]
        }
        return desc
    }
    
    /// 获取自己镶嵌的符石列表，根据 positionNo 升序
    func getSelfRuneStoneList() -> [RuneListModel] {
        return userRuneList.sorted { $0.positionNo < $1.positionNo }
    }
    
    /// 获取对方镶嵌的符石列表
    func getOtherRuneStoneList() -> [RuneListModel] {
        return toUserRuneList.sorted { $0.positionNo < $1.positionNo }
    }
    
    /// 获取自己的强攻符石（runeId == 74）
    func getSelfStormRuneStone() -> RuneListModel? {
        return userRuneList.first { $0.runeId == 74 }
    }
    
    /// 获取对方的御敌符石（runeId == 80）
    func getOtherResistEnemyRuneStone() -> RuneListModel? {
        return toUserRuneList.first { $0.runeId == 80 }
    }
    
    //获取自己等级的说明
    func getMeLevelArr() -> [RuneListModel] {
        var datas: [RuneListModel] = []
        if userNobleAddLevel > 0 {
            var oneInf0 = RuneListModel()
            oneInf0.url = Noble.init(type: userNobleId) == .emperor ? "skill_pk_upgrade_dh" : "skill_pk_upgrade_gw"
            oneInf0.runeName = "等级+\(userNobleAddLevel)"
            oneInf0.runeAttributeDesc = "PK时技能等级命中/闪避+\(userNobleAddLevel)个等级"
            datas.append(oneInf0)
        }
        
        if let a = getSelfStormRuneStone() {
            datas.append(a)
        }
        return datas
    }
    
    //获取对方等级的说明
    func getOtherLevelArr() -> [RuneListModel] {
        var datas: [RuneListModel] = []
        if otherNobleAddLevel > 0 {
            var oneInf0 = RuneListModel()
            oneInf0.url = Noble.init(type: otherNobleId) == .emperor ? "skill_pk_upgrade_dh" : "skill_pk_upgrade_gw"
            oneInf0.runeName = "等级+\(otherNobleAddLevel)"
            oneInf0.runeAttributeDesc = "PK时技能等级命中/闪避+\(otherNobleAddLevel)个等级"
            datas.append(oneInf0)
        }
        
        if let a = getOtherResistEnemyRuneStone() {
            datas.append(a)
        }
        return datas
    }
}

struct SkillResultModel: SmartCodable {
    var hitReault: Int = 0
    var meatShield: Int = 1  // 肉盾状态，0表示肉盾抵挡，1表示正常
    var runeFanji: Int = 0
    var runeLianhuan: Int = 0
    var runeTriggerList = [String]()
}


struct RuneListModel: SmartCodable {
    var attributeValue: Float = 0.0
    var backPackId: Int = 0
    var id: Int = 0
    var level: Int = 0
    var lockStatus: Int = 0
    var oneSkillLimit: String?
    var positionNo: Int = 0
    var qualityId: Int = 0
    var runeAttributeDesc: String?
    var runeFighting: Int = 0
    var runeId: Int = 0
    var runeName: String?
    var skillType: SkillType = .skill1
    var status: Int = 0
    var typeId: Int = 0
    var url: String?
    var userId: Int = 0
}
