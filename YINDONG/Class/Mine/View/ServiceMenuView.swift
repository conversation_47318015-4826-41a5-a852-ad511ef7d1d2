//
//  ServiceMenuView.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit
import SnapKit

class ServiceMenuView: UIView {
    
    // MARK: - Properties
    
    private var menuItems: [MenuItemModel] = [] {
        didSet {
            updateLayout()
        }
    }
    
    // MARK: - UI Components
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "其他服务"
        label.font = .medium(13)
        label.textColor = UIColor(hex: 0x363853)
        return label
    }()
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center
        stackView.spacing = 30
        return stackView
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        loadData()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        // 添加外边距容器
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layerCornerRadius = 16
        addSubview(containerView)
        
        containerView.addSubview(titleLabel)
        containerView.addSubview(stackView)
        
        // 容器约束 - 设置外边距
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.bottom.equalTo(-16)
            make.left.right.equalToSuperview().inset(16)
        }
        
        // 标题约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.equalToSuperview().offset(20)
        }
        
        // StackView约束
        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.equalToSuperview().offset(20)
            make.bottom.equalToSuperview().offset(-20)
            make.height.equalTo(50)
        }
    }
    
    private func setupConstraints() {
    
    }
    
    // MARK: - Data Loading
    
    func loadData() {
        menuItems = MenuItemManager.shared.getServiceMenuItems()
    }
    
    // MARK: - Layout Update
    
    private func updateLayout() {
        // 清除现有的子视图
        stackView.arrangedSubviews.forEach { view in
            stackView.removeArrangedSubview(view)
            view.removeFromSuperview()
        }
        
        // 添加新的菜单项
        for item in menuItems {
            let itemView = createServiceItemView(with: item)
            stackView.addArrangedSubview(itemView)
        }
    }
    
    private func createServiceItemView(with item: MenuItemModel) -> UIView {
        let containerView = UIView()
        
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(named: item.iconName)
        iconImageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = item.displayName
        titleLabel.font = .medium(12)
        titleLabel.textColor = UIColor(hex: 0x333333)
        titleLabel.textAlignment = .center
        
        containerView.addSubview(iconImageView)
        containerView.addSubview(titleLabel)
        
        // 图标约束
        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        // 标题约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(4)
            make.bottom.equalToSuperview().offset(-8)
        }
        
        // 配置可用状态
        containerView.alpha = item.isEnabled ? 1.0 : 0.5
        containerView.isUserInteractionEnabled = item.isEnabled
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(serviceItemTapped(_:)))
        containerView.addGestureRecognizer(tapGesture)
        containerView.tag = menuItems.firstIndex(where: { $0.type == item.type }) ?? 0
        
        return containerView
    }
    
    // MARK: - Actions
    
    @objc private func serviceItemTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              view.tag < menuItems.count else { return }
        
        let item = menuItems[view.tag]
        
        // 添加点击动画
        UIView.animate(withDuration: 0.1, animations: {
            view.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                view.transform = .identity
            }
        }
        
        MenuItemManager.shared.handleMenuItemTap(item.type)
    }
}
