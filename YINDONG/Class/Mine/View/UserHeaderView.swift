//
//  UserHeaderView.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit
import SnapKit

class UserHeaderView: UIView {
    
    // MARK: - UI Components
    
    /// 用户头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.backgroundColor = UIColor(hex: 0xF0F0F0)
        imageView.layerBorderWidth = 3
        imageView.layerBorderColor = .white
        imageView.layerCornerRadius = 44
        return imageView
    }()
    
    /// 用户昵称
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .bold(18)
        label.textColor = UIColor(hex: 0x363853)
        return label
    }()
    
    /// 用户ID和完善度
    private lazy var userInfoLabel: UILabel = {
        let label = UILabel()
        label.font = .Font_115(11)
        label.textColor = UIColor(hex: 0xC2BFCA)
        return label
    }()
    
    /// 完善度百分比
    private lazy var completionLabel: InfoItemBaseView = {
        let label = InfoItemBaseView(rightImg: "edit_rate", leftTitle: "", isLeft: true, space: 4, imgWH: CGSizeMake(16, 16), margin: 12)
        label.titleLab.font = .bold(10)
        label.titleLab.textColor = UIColor(hex: 0x545466)
        label.backgroundColor = .white
        label.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(cilkHomePage)))
        return label
    }()
    
    /// 统计数据容器
    private lazy var statsContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    /// 统计数据 StackView
    private lazy var statsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center
        stackView.spacing = 30
        return stackView
    }()
    
    /// 会员中心横幅
    lazy var memberBannerView: UIImageView = {
        let view = UIImageView(image: UIImage(named: "vip_baseBg")?.createResizableImageCentered())
        return view
    }()
    
    private lazy var memberIconView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "icon_vip")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var memberTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "会员中心，开通会员享专属特权"
        label.font = .bold(12)
        label.textColor = .white
        return label
    }()
    
    private lazy var memberActionLabel: UILabel = {
        let label = UILabel()
        label.text = "立即开通"
        label.font = .medium(12)
        label.textColor = .white
        label.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        label.textAlignment = .center
        label.layer.cornerRadius = 12.5
        label.layer.masksToBounds = true
        return label
    }()
    
    lazy var bottomView: UIView = {
        let vv = UIView()
        vv.backgroundColor = .white
        vv.layerCornerRadius = 16
        return vv
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        setupUserInfo()
        setupStatsView()
        setupMemberBanner()
        setupConstraints()
        setupGestures()
    }
    
    private func setupUserInfo() {
        addSubview(avatarImageView)
        addSubview(nameLabel)
        addSubview(userInfoLabel)
        addSubview(completionLabel)
        
    }
    
    private func setupStatsView() {
        addSubview(statsContainerView)
        statsContainerView.addSubview(statsStackView)
        
        // 创建统计项
        let statsData = [
            ("0", "动态"),
            ("0", "关注"),
            ("0", "粉丝")
        ]
        
        for (count, title) in statsData {
            let statView = createStatView(count: count, title: title)
            statsStackView.addArrangedSubview(statView)
        }
    }
    
    private func setupMemberBanner() {
        addSubview(memberBannerView)
        memberBannerView.addSubview(memberIconView)
        memberBannerView.addSubview(memberTitleLabel)
        memberBannerView.addSubview(memberActionLabel)
   
        addSubview(bottomView)
        
        
        var buttons: [UIButton] = []
        
        ["me_skill_left", "me_goods_right"].enumerated().forEach { idx, ss in
            let button = UIButton()
            button.tx_Img(ss, isBG: true)
            button.tag = idx
            button.addTarget(self, action: #selector(skillOrGood(btn: )))
            bottomView.addSubview(button)
            buttons.append(button)
        }
        
        buttons[0].snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(16)
            make.height.equalTo(64)
        }
        
        buttons[1].snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalTo(-16)
            make.size.equalTo(buttons[0])
            make.left.equalTo(buttons[0].snp.right).offset(11)
        }
    }
    
    @objc
    func skillOrGood(btn: UIButton) {
        MenuItemManager.shared.handleMenuItemTap(btn.tag == 0 ? .skill : .store)
    }
    
    private func createStatView(count: String, title: String) -> UIStackView {
        let containerView = UIStackView(arrangedSubviews: [], axis: .vertical, spacing: 2, alignment: .fill, distribution: .equalCentering)
        
        let countLabel = UILabel()
        countLabel.text = count
        countLabel.font = .Font_115(15)
        countLabel.textColor = UIColor(hex: 0x545466)
        countLabel.textAlignment = .center
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .medium(11)
        titleLabel.textColor = UIColor(hex: 0x8F90A0)
        titleLabel.textAlignment = .center
        
        containerView.addArrangedSubview(countLabel)
        containerView.addArrangedSubview(titleLabel)
        
        
        return containerView
    }
    
    private func setupConstraints() {
        // 头像约束
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(28)
            make.left.equalToSuperview().offset(20)
            make.width.height.equalTo(88)
        }
        
        // 昵称约束
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView).offset(2)
            make.left.equalTo(avatarImageView.snp.right).offset(20)
            make.right.equalToSuperview().offset(-20)
        }
        
        // 用户信息约束
        userInfoLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(4)
            make.left.equalTo(nameLabel)
        }
        
        // 完善度约束
        completionLabel.snp.makeConstraints { make in
            make.centerY.equalTo(userInfoLabel).inset(-20)
            make.right.equalToSuperview()
            make.height.equalTo(32)
        }
        
        // 统计容器约束
        statsContainerView.snp.makeConstraints { make in
            make.top.equalTo(userInfoLabel.snp.bottom).offset(8)
//            make.right.equalToSuperview().inset(30)
            make.left.equalTo(nameLabel)
            make.height.equalTo(40)
        }
        
        // 统计 StackView 约束
        statsStackView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview()
            make.height.equalToSuperview()
            make.right.equalToSuperview()
        }
        
        // 会员横幅约束
        memberBannerView.snp.makeConstraints { make in
            make.top.equalTo(statsContainerView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(56)
        }
        
        bottomView.snp.makeConstraints { make in
            make.top.equalTo(memberBannerView.snp.bottom).offset(-12)
            make.left.right.equalTo(memberBannerView)
            make.height.equalTo(88)
            make.bottom.equalToSuperview()
        }
        
        // 会员图标约束
        memberIconView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(-10)
            make.top.equalTo(9)
            make.width.height.equalTo(24)
        }
        
        // 会员标题约束
        memberTitleLabel.snp.makeConstraints { make in
            make.left.equalTo(memberIconView.snp.right).offset(8)
            make.centerY.equalTo(memberIconView)
        }
        
        // 会员操作按钮约束
        memberActionLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalTo(memberIconView)
            make.width.equalTo(68)
            make.height.equalTo(25)
        }
        
        
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        completionLabel.roundCorners([.topLeft, .bottomLeft], radius: 16)
    }
    
    private func setupGestures() {
        // 头像点击手势
        let avatarTap = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
        avatarImageView.isUserInteractionEnabled = true
        avatarImageView.addGestureRecognizer(avatarTap)
        
        // 会员横幅点击手势
        let memberTap = UITapGestureRecognizer(target: self, action: #selector(memberBannerTapped))
        memberBannerView.isUserInteractionEnabled = true
        memberBannerView.addGestureRecognizer(memberTap)
    }
    
    // MARK: - Actions
    
    @objc private func avatarTapped() {
        LWJumpManger.goUserPage()
    }
    
    @objc private func memberBannerTapped() {
        // 跳转到会员中心
        print("跳转到会员中心")
    }
    
    @objc
    func cilkHomePage() {
        LWJumpManger.goUserPage()
    }
    
    // MARK: - Public Methods
    
    /// 更新用户信息
    func updateUserInfo(avatar: String?, name: String?, userID: String?, completion: Int?) {
        if let avatar = avatar {
            avatarImageView.setImage(from: avatar)
        }
        
        if let name = name {
            nameLabel.text = name
        }
        
        if let userID = userID {
            userInfoLabel.text = "ID:\(userID)"
        }
        
        if let completion = completion {
            completionLabel.titleLab.text = "完善度\(completion)%"
        }
    }
    
    /// 更新统计数据
    func updateStats() {
        guard let info = LWUserManger.shared.infoCollectionModel else { return }
        
        let stats = [info.dyCount, info.followCount, info.fansCount]
        
        for (index, stat) in stats.enumerated() {
            if index < statsStackView.arrangedSubviews.count {
                let statView = statsStackView.arrangedSubviews[index]
                if let countLabel = statView.subviews.first as? UILabel {
                    countLabel.text = "\(stat)"
                }
            }
        }
    }
  
}
