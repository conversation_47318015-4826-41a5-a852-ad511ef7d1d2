//
//  FunctionGridView.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit
import SnapKit

class FunctionGridView: UIView {
    
    // MARK: - Configuration

    private let columns: Int = 4  // 每行4个
    private let itemHeight: CGFloat = 88  // 每个item高度 - 稍微增加
    private let lineSpacing: CGFloat = 24  // 行间距 - 稍微增加
    private let itemSpacing: CGFloat = 24  // 列间距
    private let sectionInsets = UIEdgeInsets(top: 20, left: 20, bottom: 20, right: 20)
    
    // MARK: - Properties
    
    private var menuItems: [MenuItemModel] = [] {
        didSet {
            updateLayout()
        }
    }
    
    private var heightConstraint1: Constraint?
    
    // MARK: - UI Components
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "特色玩法"
        label.font = .medium(13)
        label.textColor = UIColor(hex: 0x363853)
        return label
    }()
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = lineSpacing  // 行间距
        layout.minimumInteritemSpacing = itemSpacing  // 列间距
        layout.sectionInset = UIEdgeInsets(top: 0, left: 20, bottom: 0, right: 20)

        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = .clear
        cv.isScrollEnabled = false  // 禁用滚动，由外层ScrollView处理
        cv.delegate = self
        cv.dataSource = self
        cv.register(FunctionItemCell.self, forCellWithReuseIdentifier: "FunctionItemCell")
        return cv
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        loadData()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        loadData()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear


        // 添加外边距容器
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layerCornerRadius = 16
        addSubview(containerView)

        containerView.addSubview(titleLabel)
        containerView.addSubview(collectionView)

        // 容器约束 - 设置外边距
        containerView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.right.equalToSuperview().inset(16)
        }

        setupConstraints()
    }
    
    private func setupConstraints() {
        // 标题约束
        titleLabel.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(16)
            make.height.equalTo(20)
        }
        
        // CollectionView约束
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
            heightConstraint1 = make.height.equalTo(0).constraint
        }
    }
    
    // MARK: - Data Loading
    
    func loadData() {
        menuItems = MenuItemManager.shared.getFunctionMenuItems()
    }
    
    // MARK: - Layout Update
    
    private func updateLayout() {
        // 计算需要的高度
        let calculatedHeight = calculateCollectionViewHeight()
        
        // 更新CollectionView高度约束
        heightConstraint1?.update(offset: calculatedHeight)
        
        // 刷新数据
        collectionView.reloadData()
        
        // 通知父视图更新布局
        setNeedsLayout()
        layoutIfNeeded()
    }
    
    /// 计算CollectionView所需高度
    private func calculateCollectionViewHeight() -> CGFloat {
        guard !menuItems.isEmpty else { return 0 }

        let rows = ceil(Double(menuItems.count) / Double(columns))
        let totalHeight = CGFloat(rows) * itemHeight + CGFloat(max(0, rows - 1)) * lineSpacing

        print("🎯 FunctionGridView 高度计算: items=\(menuItems.count), rows=\(rows), height=\(totalHeight)")

        return totalHeight
    }
    
    /// 计算item大小
    private func calculateItemSize() -> CGSize {
        let availableWidth = bounds.width - sectionInsets.left - sectionInsets.right - 32
        let totalSpacing = CGFloat(columns - 1) * itemSpacing
        let itemWidth = (availableWidth - totalSpacing) / CGFloat(columns)
        return CGSize(width: itemWidth, height: itemHeight)
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        // 更新CollectionView布局
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            layout.itemSize = calculateItemSize()
        }
    }
}

// MARK: - UICollectionViewDataSource

extension FunctionGridView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return menuItems.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "FunctionItemCell", for: indexPath) as! FunctionItemCell
        let item = menuItems[indexPath.item]
        cell.configure(with: item)
        return cell
    }
}

// MARK: - UICollectionViewDelegate

extension FunctionGridView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let item = menuItems[indexPath.item]
        
        MenuItemManager.shared.handleMenuItemTap(item.type)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension FunctionGridView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return calculateItemSize()
    }
}

// MARK: - FunctionItemCell

class FunctionItemCell: UICollectionViewCell {
    
    // MARK: - UI Components
    
    private lazy var iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(13)  // 稍微小一点
        label.textColor = UIColor(hex: 0x545466)  // 稍微浅一点的颜色
        label.textAlignment = .center
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var badgeLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(10)
        label.textColor = .white
        label.backgroundColor = UIColor(hex: 0xFF4757)
        label.textAlignment = .center
        label.layer.cornerRadius = 8
        label.layer.masksToBounds = true
        label.isHidden = true
        return label
    }()
    
    private lazy var newLabel: UILabel = {
        let label = UILabel()
        label.text = "NEW"
        label.font = .medium(8)
        label.textColor = .white
        label.backgroundColor = UIColor(hex: 0xFF6B35)
        label.textAlignment = .center
        label.layer.cornerRadius = 6
        label.layer.masksToBounds = true
        label.isHidden = true
        return label
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        let bgView = UIView()
        bgView.backgroundColor = UIColor(hex: 0xF6F6FB)  // 更浅的灰色背景
        bgView.layerCornerRadius = 16

        contentView.addSubview(bgView)
        bgView.addSubview(iconImageView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(badgeLabel)
        contentView.addSubview(newLabel)

        bgView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(54)
        }
        
        // 图标约束 - 调整位置让整体更居中
        iconImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(32)  // 恢复图标大小
        }

        // 标题约束 - 减少间距
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(bgView.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(6)
            make.height.equalTo(18)
        }

        // 角标约束
        badgeLabel.snp.makeConstraints { make in
            make.top.equalTo(bgView).offset(-6)
            make.right.equalTo(bgView).offset(10)
            make.width.height.equalTo(18)
        }

        // NEW标签约束
        newLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(6)
            make.right.equalToSuperview().offset(-6)
            make.width.equalTo(28)
            make.height.equalTo(14)
        }
    }
    

    // MARK: - Configuration
    
    func configure(with item: MenuItemModel) {
        iconImageView.image = UIImage(named: item.iconName)
        titleLabel.text = item.displayName
        
        // 配置角标
        if let badgeCount = item.badgeCount, badgeCount > 0 {
            badgeLabel.isHidden = false
            badgeLabel.text = badgeCount > 99 ? "99+" : "\(badgeCount)"
        } else {
            badgeLabel.isHidden = true
        }
        
        // 配置NEW标签
        newLabel.isHidden = !item.isNew
        
        // 配置可用状态
        alpha = item.isEnabled ? 1.0 : 0.5
        isUserInteractionEnabled = item.isEnabled
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        badgeLabel.isHidden = true
        newLabel.isHidden = true
        alpha = 1.0
        isUserInteractionEnabled = true
    }
}
