//
//  MineUserVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit
import SnapKit

class MineUserVC: YinDBaseVC {

    // MARK: - UI Components

    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()

    private lazy var contentStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()

    private lazy var userHeaderView: UserHeaderView = {
        let view = UserHeaderView()
        return view
    }()

    private lazy var functionGridView: FunctionGridView = {
        let view = FunctionGridView()
        return view
    }()

    private lazy var rechargeCardView: RechargeCardView = {
        let view = RechargeCardView()
        return view
    }()

    private lazy var serviceMenuView: ServiceMenuView = {
        let view = ServiceMenuView()
        return view
    }()

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        
        isNavigationBarVisible = false

        setupUI()
        
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }

    // MARK: - UI Setup

    private func setupUI() {
        view.backgroundColor = UIColor(hex: 0xF6F6FB)
        
        let imgV = UIImageView(image: UIImage(named: "baseVcTopBg"))
        imgV.layerCornerRadius = 10
        view.addSubview(imgV)
        imgV.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentStackView)

        // 添加各个组件到StackView
        contentStackView.addArrangedSubview(userHeaderView)
        contentStackView.addArrangedSubview(functionGridView)
        contentStackView.addArrangedSubview(rechargeCardView)
        contentStackView.addArrangedSubview(serviceMenuView)

        setupConstraints()
        
        
    }

    private func setupConstraints() {
        // ScrollView约束
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        // ContentStackView约束 - 撑满宽度，不设置统一间距
        contentStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        rechargeCardView.snp.makeConstraints { make in
            make.height.equalTo(195)
        }
    }

    // MARK: - Data Loading

    override func loadData() {
        loadUserInfo()
        loadAccountInfo()
    }

    private func loadUserInfo() {
        // 更新用户基本信息
        userHeaderView.updateUserInfo(
            avatar: kuser.photoUrl,
            name: kuser.nickName,
            userID: kuser.userNumber.string,
            completion: 85  // TODO: 从API获取实际完善度
        )
        
        LWUserManger.shared.infoRelay.subscribe(onNext: { [weak self] info in
            guard let self = self else { return }
            
            self.userHeaderView.updateStats()
            
        }).disposed(by: baseDisposeBag)
    }

    private func loadAccountInfo() {
        // 加载账户信息
        LWUserManger.shared.getAccountInfo(type: .currencyOnly) { model in
            self.rechargeCardView.setDiamondCount(Int(model?.diamond ?? 0.0))
            self.rechargeCardView.setYuanbaoCount(model?.yuanbao ?? 0)
        }

        // 加载等级信息
        NetworkUtility.request(target: .userLevelInfo, model: UserLevelInfoModel.self) { result in
            if result.isError { return }
            self.rechargeCardView.setLevel(result.model?.curLevel ?? 0)
        }
    }

}

