//
//  MansionMangerListVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/19.
//

import UIKit

class MansionMangerListVC: YinDBaseVC {

    var mm: MansionMainModel?

    // 倒计时定时器
    private var countdownTimer: Timer?

    // 记录是否有解除操作，页面消失时需要刷新
    private var hasReleaseAction = false

    // 刷新回调
    var onNeedRefresh: (() -> Void)?

    override func viewDidLoad() {
        super.viewDidLoad()

        navigationTitle = "跟班管理"
        navigationBarAlpha = 0
        navigationTitleColor = .white
        navigationBarTintColor = .white
        
        noDataTitle = "可使用【套麻袋】抓跟班/系统短工\n为你打工哦~"
        noDataImageName = "empty_ggIcon"

        if mm?.attendantUserList.count ?? 0 > 0 {
            startCountdownTimer()
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 页面消失时，如果有解除操作，通知外部刷新
        if hasReleaseAction {
            onNeedRefresh?()
        }

        stopCountdownTimer()
    }

    deinit {
        stopCountdownTimer()
    }
    
    
    override func lw_setupUI() {
        super.lw_setupUI()
        
        view.backgroundColor = .init(hex: 0x181524)
        view.addSubview(tableView)
        tableView.dataSource = self
        tableView.delegate = self
        tableView.register(cellWithClass: MansionAttendantCell.self)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview()
        }
    }


}


extension MansionMangerListVC: UITableViewDataSource, UITableViewDelegate {

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withClass: MansionAttendantCell.self)

        guard let attendantList = mm?.attendantUserList, indexPath.row < attendantList.count else {
            return cell
        }

        let attendant = attendantList[indexPath.row]
        cell.configure(with: attendant)
        cell.delegate = self

        let currentTime = Int(Date().timeIntervalSince1970 * 1000)
        let remainingTime = attendant.attendantEndDate - currentTime
        let timeString = formatCountdownTime(remainingTime: remainingTime)
        cell.configureHiredState(timeString: timeString)
        return cell
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return mm?.attendantUserList.count ?? 0
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 96
    }
}

// MARK: - MansionAttendantCellDelegate
extension MansionMangerListVC: MansionAttendantCellDelegate {
    
    func attendantCell(_ cell: MansionAttendantCell, didTapDispatch attendant: AttendantUserList) {
        // 处理解除雇佣操作
        releaseAttendant(attendant)
    }
    
    func attendantCell(_ cell: MansionAttendantCell, didTapViewDetails attendant: AttendantUserList) {
        
    }
}

// MARK: - Countdown Timer
extension MansionMangerListVC {

    private func startCountdownTimer() {
        stopCountdownTimer()
        // 启动定时器，每秒更新一次
        countdownTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateAllCountdowns()
        }
    }

    private func stopCountdownTimer() {
        countdownTimer?.invalidate()
        countdownTimer = nil
    }

    private func updateAllCountdowns() {
        guard let attendantList = mm?.attendantUserList else { return }

        var expiredIndices: [Int] = []

        // 遍历所有可见的 cell，更新倒计时
        for (index, attendant) in attendantList.enumerated() {
            let indexPath = IndexPath(row: index, section: 0)

            if let cell = tableView.cellForRow(at: indexPath) as? MansionAttendantCell {
                // 只更新被雇佣的跟班
                if attendant.attendantEndDate > 0 {
                    let currentTime = Int(Date().timeIntervalSince1970 * 1000)
                    let remainingTime = attendant.attendantEndDate - currentTime

                    if remainingTime > 0 {
                        // 更新倒计时显示
                        let timeString = formatCountdownTime(remainingTime: remainingTime)
                        cell.updateCountdown(timeString: timeString)
                    } else {
                        // 雇佣到期，记录需要删除的索引
                        expiredIndices.append(index)
                    }
                }
            }
        }

        // 删除到期的用户（从后往前删除，避免索引错乱）
        if !expiredIndices.isEmpty {
            removeExpiredAttendants(at: expiredIndices)
        }
    }

    private func removeExpiredAttendants(at indices: [Int]) {
        // 从后往前删除，避免索引错乱
        let sortedIndices = indices.sorted(by: >)

        for index in sortedIndices {
            // 从数据源中删除
            mm?.attendantUserList.remove(at: index)

            // 从表格中删除
            let indexPath = IndexPath(row: index, section: 0)
            tableView.deleteRows(at: [indexPath], with: .fade)
        }

        // 标记有变更，页面消失时需要刷新
        hasReleaseAction = true

        // 显示提示
        if sortedIndices.count == 1 {
            ProgressHUDManager.showTextMessage("雇佣已到期，已自动解除")
        } else {
            ProgressHUDManager.showTextMessage("有\(sortedIndices.count)个雇佣已到期，已自动解除")
        }
    }

    private func formatCountdownTime(remainingTime: Int) -> String {
        // 将毫秒转换为秒
        let seconds = remainingTime / 1000

        // 计算时、分、秒
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        let secs = seconds % 60

        // 格式化时间字符串
        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, secs)
        } else {
            return String(format: "%02d:%02d", minutes, secs)
        }
    }
}

// MARK: - Release Action
extension MansionMangerListVC {

    private func releaseAttendant(_ attendant: AttendantUserList) {
        
        let msgV = MessagePopupView(title: "是否确认解除雇佣", content: "你将失去未收取得派遣收益")
        msgV.confirmButton.setTitle("确认解除", for: .normal)
        msgV.cancelButton.setTitle("我再想想", for: .normal)
        msgV.onAction = { [weak self] isc, V in
            guard let self = self else { return }
            if isc { return }
            self.performReleaseAction(attendant)
        }
        msgV.show()
    }

    private func performReleaseAction(_ attendant: AttendantUserList) {
        // 执行解除雇佣的网络请求
        var dict: [String: Any] = [:]
        dict["userId"] = attendant.userId

        NetworkUtility.request(target: .relieveUserMansions(dict)) { [weak self] result in
            if result.isError {
                ProgressHUDManager.showTextMessage("解除雇佣失败")
                return
            }
            ProgressHUDManager.showTextMessage("解除雇佣成功")
            // 标记有解除操作
            self?.hasReleaseAction = true
            // 从本地数据中移除该跟班
            self?.removeAttendantFromLocal(attendant)
        }
    }

    private func removeAttendantFromLocal(_ attendant: AttendantUserList) {
        guard let index = mm?.attendantUserList.firstIndex(where: { $0.userId == attendant.userId }) else { return }

        mm?.attendantUserList.remove(at: index)

        // 刷新表格
        let indexPath = IndexPath(row: index, section: 0)
        tableView.deleteRows(at: [indexPath], with: .fade)
    }
}
