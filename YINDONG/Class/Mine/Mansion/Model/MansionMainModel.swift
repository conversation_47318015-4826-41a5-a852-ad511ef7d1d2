//
//  MansionMainModel.swift
//  YINDONG
//
//  Created by jj on 2025/6/18.
//

import UIKit

class MansionMainModel: SmartCodable {
    
    var attendantCount: Int = 0
    var attendantCountMax: Float = 0.0
    var attendantUserList = [AttendantUserList]()
    var level: Int = 0
    var superWorkerHireStatus: Int = 0
    var superWorkerLevel: Int = 0
    var systemWorkerHireStatus: Int = 0
    var typeOfWorkList = [WorkListModel]()
    var endDate: Int = 0
    var masterAttendantCount: Int = 0
    var masterAttendantCountMax: Int = 0
    var masterImNumber: Int?
    var masterLevel: Int = 0
    var masterNickName: String?
    var masterPhotoUrl: String?
    var masterSex: Int = 0
    
    
    
    required init() {
        
    }
    
}

class AttendantUserList: SmartCodable {
    var attendantEndDate: Int = 0
    var attendantTaskId: Int = 0
    var attendantTaskName: String?
    var attendantUserLevel: Int = 0
    var createDate: Int = 0
    var id: Int = 0
    var imNumber: String?
    var nickName: String?
    var photoUrl: String?
    var positionId: Int = 0
    var robotFlag: String?
    var sex: Int = 0
    var userId: Int = 0
    required init() {
        
    }
}

class WorkListModel: SmartCodable {
    var background: String?
    var isDispatch: Int = 0
    var isUnlock: Int = 0
    var level: String?
    var remarks: String?
    var taskId: String?
    var taskName: String?
    var taskUrl: String?
    var textColour: String?
    
    var isSeleted: Bool = false
    
    required init() {
        
    }
}

struct MansionQuerData: SmartCodable {
    var expectHourRewardUrl: String?
    var expectHourRewardValue: Int = 0
    var minuteNumRemarks: String?
    var minuteRemarks: String?
    var minutesSum: Int = 0
    var minuteValueRemarks: String?
    var rewardList = [MansionRewardModel]()
    var startDate: Int = 0
    var taskId: Int = 0
    var minuteValue: Int = 0
    
    var additionalNumRemarks: String?
    var additionalRewardType: Int = 0
    var additionalRewardUrl: String?
    var additionalRewardValue: Int = 0
    var expCurMaxValue: Int = 0
    var expCurValue: Int = 0
    var expMaxValue: Int = 0

}
struct MansionRewardModel: SmartCodable {
    var rewardName: String?
    var rewardNumRemarks: String?
    var rewardType: Int = 0
    var rewardUrl: String?
    var rewardValue: Int = 0
}


struct MansionPositionModel: SmartCodable {
    var attendantUserIm: Int = 0
    var content1: String?
    var content2: String?
    var createDate: Int = 0
    var endType: Int = 0
    var nickName: String?
    var positionId: Int = 0
    var positionName: String?
}
