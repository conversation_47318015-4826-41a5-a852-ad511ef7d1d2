//
//  MansionMainVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/18.
//

import UIKit

class MansionMainVC: YinDBaseVC {

    var seleteModel: WorkListModel?

    var infoModel: MansionMainModel? {
        didSet {
            updateUI()
        }
    }

    // 倒计时定时器
    private var countdownTimer: Timer?

    // MARK: - UI Components

    /// 头部用户信息视图
    private lazy var headerInfoView: MansionHeaderInfoView = {
        let view = MansionHeaderInfoView()
        return view
    }()

    /// 滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.contentInsetAdjustmentBehavior = .never
        return scrollView
    }()

    /// 主容器 StackView
    private lazy var containerStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.alignment = .fill
        stackView.distribution = .fill
        return stackView
    }()

    /// 主人区域视图
    private lazy var masterSectionView: MansionMasterSectionView = {
        let view = MansionMasterSectionView()
        return view
    }()

    /// 工种区域视图
    private lazy var workSectionView: MansionWorkSectionView = {
        let view = MansionWorkSectionView()
        view.delegate = self
        return view
    }()

    /// 跟班区域视图
    private lazy var attendantSectionView: MansionAttendantSectionView = {
        let view = MansionAttendantSectionView()
        view.delegate = self
        return view
    }()

    override func viewDidLoad() {
        super.viewDidLoad()

        navigationTitle = "我的工馆"
        navigationBarAlpha = 0
        navigationTitleColor = .white
        navigationBarTintColor = .white
        
        let btn = UIButton()
        btn.tx_Img("wh_right")
        btn.addTarget(self, action: #selector(showInfo))
        navigation.item.rightBarButtonItem = UIBarButtonItem(customView: btn)

        loadData()
        
        masterSectionView.onFollowButtonTapped = { [weak self] in
            guard let self = self else { return }
            
            let msgVC = MessagePopupView(title: "提示", content: "赎身需花费100钻石")
            msgVC.confirmButton.setTitle("确认赎身", for: .normal)
            msgVC.cancelButton.setTitle("我再想想", for: .normal)
            msgVC.onAction = { [weak self] isC, vv in
                if isC { return }
                
                NetworkUtility.request(target: .redemptionMansions) { result in
                    if result.isError { return }
                    ProgressHUDManager.showTextMessage("赎身成功")
                    self?.infoModel?.masterImNumber = nil
                    self?.updateUI()
                }
            }
            msgVC.show()
            
        }
        
        // 设置头部视图的刷新回调
        headerInfoView.onNeedRefresh = { [weak self] in
            self?.loadData()
        }
        
        //开通技能跳转技能
        headerInfoView.onUpgradeButtonTapped = { [weak self] in
            if self?.infoModel?.level == 0 {
                self?.skillOpen()
                return
            }
            let skill = SkillMainListVC()
            self?.navigationController?.pushViewController(skill)
        }
    }
    
    func skillOpen() {
        var pp = creatDict()
        pp["skillType"] = SkillType.skill4.rawValue
        NetworkUtility.request(target: .upgradeSkill(pp)) { result in
            if result.isError {
                return
            }
            ProgressHUDManager.showTextMessage("开通成功")
            NotificationCenter.default.post(name: .skillUpNotification, object: nil)
            self.loadData()
        }
    }
    
    

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        stopCountdownTimer()
    }

    deinit {
        stopCountdownTimer()
    }
    
    @objc
    func showInfo() {
        let info = WorkInfoPopupView()
        info.show()
    }

    override func lw_setupUI() {
        super.lw_setupUI()

        setupBackground()
        setupHeaderView()
        setupContainerViews()
    }

    override func loadData() {
        NetworkUtility.request(target: .skillMansionsInfo, model: MansionMainModel.self) { result in
            if result.isError { return }
            self.infoModel = result.model
        }
    }

    // MARK: - Private Methods

    private func setupBackground() {
        let imgV = UIImageView(image: UIImage(named: "mansion_bg"))
        view.addSubview(imgV)
        imgV.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(227 + AppTool.safeAreaTopHeight)
        }

        let bgView = UIView()
        bgView.backgroundColor = UIImage(dvt: [.init(hex: 0x342D64), .init(hex: 0x181524), .init(hex: 0x181524)], size: CGSize(width: AppTool.screenWidth, height: AppTool.screenHeight - 128 - AppTool.navigationBarHeight), direction: .top2bottom)?.imageToColor()
        view.addSubview(bgView)
        bgView.snp.makeConstraints { make in
            make.top.equalTo(AppTool.navigationBarHeight + 128)
            make.left.bottom.right.equalToSuperview()
        }
        
        bgView.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        scrollView.addSubview(containerStackView)
        containerStackView.snp.makeConstraints { make in
            make.top.equalTo(25)
            make.left.bottom.right.equalToSuperview()
            make.width.equalTo(AppTool.screenWidth)
        }

        view.layoutIfNeeded()
        bgView.roundCorners([.topLeft, .topRight], radius: 16)
    }

    private func setupHeaderView() {
        view.addSubview(headerInfoView)
        headerInfoView.snp.makeConstraints { make in
            make.top.equalTo(AppTool.navigationBarHeight + 20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(78)
        }
    }

    private func setupContainerViews() {
        // 添加所有子视图到 StackView，初始时可以隐藏
        containerStackView.addArrangedSubview(masterSectionView)
        containerStackView.addArrangedSubview(workSectionView)
        containerStackView.addArrangedSubview(attendantSectionView)

        // 初始状态隐藏主人区域
        masterSectionView.isHidden = true
    }

    private func updateUI() {
        guard let model = infoModel else { return }

        // 更新头部信息
        headerInfoView.configure(with: model)

        // 控制主人区域显示/隐藏
        let shouldShowMaster = shouldShowMasterSection(model)
        masterSectionView.isHidden = !shouldShowMaster
        if shouldShowMaster {
            masterSectionView.configure(with: model)
            // 开启倒计时
            startCountdownTimer(endDate: model.endDate)
        } else {
            // 停止倒计时
            stopCountdownTimer()
        }
        
        let workList = model.typeOfWorkList
        for item in workList {
            if item.isUnlock == 1 && item.isDispatch == 0 {
                item.isSeleted = true                 // 如果是 class 引用语义，这样就够了
                break                                 // 找到后立即跳出
            }
        }
        model.typeOfWorkList = workList

        // 更新工种区域数据
        workSectionView.configure(with: model.typeOfWorkList)
        // 更新跟班区域数据
        attendantSectionView.configure(with: model.attendantUserList)
    }

    //判断主人 ID 是否为空
    private func shouldShowMasterSection(_ model: MansionMainModel) -> Bool {
        // 根据业务逻辑判断是否显示主人区域
        return isValid(model.masterImNumber)
    }

    // MARK: - Countdown Timer

    private func startCountdownTimer(endDate: Int) {
        // 停止现有定时器
        stopCountdownTimer()

        // 计算剩余时间
        let currentTime = Int(Date().timeIntervalSince1970 * 1000) // 当前时间戳（毫秒）
        let remainingTime = endDate - currentTime

        // 如果已经过期，不启动定时器
        guard remainingTime > 0 else {
            // 时间已到，可以触发相关逻辑
            handleCountdownFinished()
            return
        }

        // 启动定时器，每秒更新一次
        countdownTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self, let model = self.infoModel else { return }

            let currentTime = Int(Date().timeIntervalSince1970 * 1000)
            let remainingTime = model.endDate - currentTime

            if remainingTime <= 0 {
                // 倒计时结束
                self.handleCountdownFinished()
            } else {
                // 更新倒计时显示
                self.updateCountdownDisplay(remainingTime: remainingTime)
            }
        }
    }

    private func stopCountdownTimer() {
        countdownTimer?.invalidate()
        countdownTimer = nil
    }

    private func updateCountdownDisplay(remainingTime: Int) {
        // 将毫秒转换为秒
        let seconds = remainingTime / 1000

        // 计算时、分、秒
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        let secs = seconds % 60

        // 格式化时间字符串
        let timeString: String
        if hours > 0 {
            timeString = String(format: "%02d:%02d:%02d后解除雇佣", hours, minutes, secs)
        } else {
            timeString = String(format: "%02d:%02d后解除雇佣", minutes, secs)
        }

        // 更新主人区域的时间显示
        masterSectionView.updateCountdown(timeString: timeString)
    }

    private func handleCountdownFinished() {
        // 倒计时结束的处理
        stopCountdownTimer()

        // 刷新数据，重新获取最新状态
//        loadData()

        // 可以显示提示信息
        ProgressHUDManager.showTextMessage("雇佣时间已到")
    }
}

// MARK: - MansionWorkSectionViewDelegate
extension MansionMainVC: MansionWorkSectionViewDelegate {
    func workSectionView(_ view: MansionWorkSectionView, didSelectWork work: WorkListModel) {
        // 处理工种选择
        print("选择工种：\(work.taskName ?? "")")
        // 这里可以添加具体的工种选择逻辑
        self.seleteModel = work
    }
}

// MARK: - MansionAttendantSectionViewDelegate
extension MansionMainVC: MansionAttendantSectionViewDelegate {
    func attendantSectionViewDidTapHireSystem(_ view: MansionAttendantSectionView) {
        // 处理雇佣系统跟班
        guard let model = infoModel else { return }
        
        if model.level == 0 {
            ProgressHUDManager.showTextMessage("技能暂未开通")
            return
        }


        let systemPopView = MansionSytemPopView()
        systemPopView.configure(with: model)

        // 设置雇佣回调
        systemPopView.onHireButtonTapped = { [weak self] in
            self?.hireSystemWorker()
        }

        systemPopView.show()
    }

    func attendantSectionView(_ view: MansionAttendantSectionView, didTapDispatch attendant: AttendantUserList) {
        // 处理派遣跟班
        print("派遣跟班：\(attendant.nickName ?? "")")
        // 这里可以添加具体的派遣逻辑
        var mm = infoModel?.typeOfWorkList.filter({ $0.isSeleted }).first
        if let work = seleteModel {
            mm = work
        }
        
        var dict: [String: Any] = [:]
        dict["workCode"] = attendant.positionId > 0 ? attendant.positionId : mm?.taskId ?? ""
        dict["attendantUserId"] = attendant.userId
        NetworkUtility.request(target: .skillMansionsQuer(dict), model: MansionQuerData.self) { result in
            if result.isError { return }

            let sheetV = MansionTaskSheetView()
            sheetV.model = result.model
            sheetV.attendant = attendant

            // 判断是否正在工作（根据任务名称判断）
            sheetV.isWorking = attendant.positionId > 0

            // 设置回调
            sheetV.onTerminateTask = { [weak self] in
                // 处理终止派遣逻辑
                self?.terminateTask(for: attendant, sheet: sheetV)
                
            }

            sheetV.onStartTask = { [weak self] in
                // 处理开始派遣逻辑
                self?.startTask(for: attendant, workModel: mm, sheet: sheetV)
            }

            sheetV.present(in: nil)
        }
        
    }

    func attendantSectionView(_ view: MansionAttendantSectionView, didTapViewDetails attendant: AttendantUserList) {
        // 处理查看详情
        print("查看详情：\(attendant.nickName ?? "")")
        // 这里可以添加具体的查看详情逻辑
    }

    // MARK: - Task Management

    private func terminateTask(for attendant: AttendantUserList, sheet: MansionTaskSheetView) {
        // 终止派遣任务的网络请求
        var dict: [String: Any] = [:]
        dict["taskId"] = attendant.attendantTaskId

        NetworkUtility.request(target: .endMansionsUser(dict)) { result in
            if result.isError {
                return
            }

            sheet.dismiss(animated: true) {

            }
            ProgressHUDManager.showTextMessage("终止派遣成功")
            // 刷新数据
            self.loadData()
        }
    }

    private func startTask(for attendant: AttendantUserList, workModel: WorkListModel?, sheet: MansionTaskSheetView) {
        
        
        // 开始派遣任务的网络请求
        guard let workModel = workModel else { return }

        var dict: [String: Any] = [:]
        dict["workCode"] = workModel.taskId ?? ""
        dict["userId"] = attendant.userId

        NetworkUtility.request(target: .startMansionsUser(dict)) { result in
            if result.isError {
                return
            }
            ProgressHUDManager.showTextMessage("派遣成功")
            // 刷新数据
            self.loadData()
            sheet.dismiss(animated: true) {

            }
        }
    }

    // MARK: - System Worker Management

    private func hireSystemWorker() {
        // 雇佣系统跟班的网络请求
        NetworkUtility.request(target: .hireMansionsSystemWorker) { result in
            if result.isError {
                ProgressHUDManager.showTextMessage("雇佣失败")
                return
            }

            ProgressHUDManager.showTextMessage("雇佣成功")
            // 刷新数据
            self.loadData()
        }
    }
}
