//
//  MansionTaskSheetView.swift
//  YINDONG
//
//  Created by jj on 2025/6/19.
//

import UIKit
import HWPanModal

class MansionTaskSheetView: HWPanModalContentView {

    var model: MansionQuerData? {
        didSet {
            guard let model = model else { return }
            updateUI(with: model)
        }
    }

    var attendant: AttendantUserList? {
        didSet {
            updateAttendantInfo()
        }
    }

    var isWorking: Bool = false {
        didSet {
            updateWorkingState()
        }
    }

    var onTerminateTask: (() -> Void)?
    var onStartTask: (() -> Void)?

    var shortHeight : PanModalHeight {
        // 根据是否有进度条动态调整高度
        let baseHeight: CGFloat = 350 // 基础高度
        let progressHeight: CGFloat = 60 // 进度条区域高度
        let hasProgress = model?.taskId != 1 && model?.taskId != 4
        let totalHeight = baseHeight + (hasProgress ? progressHeight : 0) + AppTool.safeAreaBottomHeight
        return PanModalHeightMake(.content, totalHeight)
    }

    // MARK: - UI Components

    lazy var contentView: UIView = {
        let vv = UIView()
        vv.backgroundColor = .clear
        return vv
    }()

    lazy var titleImgView: UIImageView = {
        let imgV = UIImageView(image: UIImage(named: "mtask_1"))
        imgV.contentMode = .scaleAspectFit
        return imgV
    }()

    // 主背景容器
    lazy var mainBackgroundView: UIImageView = {
        let imgV = UIImageView()
        imgV.contentMode = .scaleToFill
        return imgV
    }()

    // 主内容容器 - 包含用户信息和进度条
    lazy var mainContentContainer: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 8
        stack.alignment = .fill
        return stack
    }()

    // 用户信息容器（上半部分）
    lazy var userInfoContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 分割虚线
    lazy var separatorLine: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 收益信息容器（下半部分）
    lazy var rewardInfoContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 用户头像
    lazy var avatarImageView: UIImageView = {
        let imgV = UIImageView()
        imgV.contentMode = .scaleAspectFill
        imgV.layer.cornerRadius = 20
        imgV.layer.masksToBounds = true
        return imgV
    }()

    // 用户昵称
    lazy var nicknameLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(14)
        label.textColor = .black
        label.text = "我是用户昵称"
        return label
    }()

    // 用户等级
    lazy var levelLabel: InfoItemBaseView = {
        let label = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 8)
        label.titleLab.font = .regular(11)
        label.titleLab.textColor = .init(hex: 0x88250E)
        label.layerCornerRadius = 7
        label.backgroundColor = .init(hex: 0x88250E, transparency: 0.1)
        return label
    }()

    // 派遣状态
    lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(12)
        label.textColor = .textColor6
        label.text = "挖矿生产中..."
        return label
    }()

    // 倒计时标签
    lazy var countdownLabel: InfoItemBaseView = {
        let label = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 8)
        label.titleLab.font = .medium(13)
        label.titleLab.textColor = .black
        label.titleLab.text = "00时00分"
        label.layer.cornerRadius = 9
        return label
    }()
    
    lazy var expInfoLabel: InfoItemBaseView = {
        let label = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 8)
        label.titleLab.font = .medium(12)
        label.titleLab.textColor = .black
        label.titleLab.text = "00时00分"
        label.layerCornerRadius = 10.5
        return label
    }()

    // 进度条容器 - 使用 StackView 来控制显示/隐藏
    lazy var progressContainer: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 8
        stack.alignment = .fill
        return stack
    }()

    // 进度条和右侧进度说明的容器
    lazy var progressRowContainer: UIView = {
        let view = UIView()
        return view
    }()

    // 进度条 - 使用 CustomProgressBar
    lazy var progressView: CustomProgressBar = {
        let progress = CustomProgressBar(frame: CGRectMake(0, 0, AppTool.screenWidth - 100, 8))
        progress.trackColor = UIColor(hex: 0xE0E0E0)
        progress.progressColor = UIColor(hex: 0x4CAF50)
        progress.borderWidth = 0
        progress.defaultProgress = 0
        return progress
    }()

    // 右侧进度说明 "200/300经验"
    lazy var progressStatusLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(11)
        label.textColor = .textColor3
        label.text = "200/300经验"
        label.textAlignment = .right
        return label
    }()

    // 进度条下面的收益说明 "经验值满可获得 🍀 x15"
    lazy var progressLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(12)
        label.textColor = .gray
        label.text = "经验值满可获得 🍀 x15"
        label.textAlignment = .left
        return label
    }()

    // 收益容器
    lazy var rewardContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear // 透明背景
        return view
    }()

    // 收益标题
    lazy var rewardTitleLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(14)
        label.textColor = .black
        label.text = "每小时预计收益"
        label.textAlignment = .center
        return label
    }()

    // 收益项目 CollectionView
    lazy var rewardCollectionView: UICollectionView = {
        let layout = CenteredCollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 16
        layout.minimumLineSpacing = 16
        layout.estimatedItemSize = CGSize(width: 80, height: 35)
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(MansionRewardCollectionViewCell.self, forCellWithReuseIdentifier: "MansionRewardCollectionViewCell")
        return collectionView
    }()

    // 收益数据数组 - 使用真实的 MansionRewardModel
    var rewardItems: [MansionRewardModel] = []

    // 主按钮
    lazy var actionButton: UIButton = {
        let btn = UIButton()
        btn.setTitle("进行派遣", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.titleLabel?.font = .medium(16)
        btn.backgroundColor = UIColor(hex: 0xFF6B6B)
        btn.layer.cornerRadius = 22
        btn.addTarget(self, action: #selector(actionButtonTapped), for: .touchUpInside)
        return btn
    }()

    // 提示文字
    lazy var tipLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(12)
        label.textColor = .gray
        label.text = "派遣后雇佣时间结束前会一直为你带来收益"
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    convenience init() {
        self.init(frame: .zero)
        setupUI()
    }

    // MARK: - Setup UI

    private func setupUI() {
        // 设置背景
        addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 添加标题图片
        contentView.addSubview(titleImgView)
        titleImgView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(16)
        }

        // 添加主背景容器
        contentView.addSubview(mainBackgroundView)
        mainBackgroundView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.equalTo(titleImgView.snp.bottom).offset(16)
            make.height.equalTo(247)
        }

        // 添加主内容容器到背景视图
        mainBackgroundView.addSubview(mainContentContainer)
        mainContentContainer.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
        }

        // 将用户信息和进度条添加到主内容容器
        mainContentContainer.addArrangedSubview(userInfoContainer)
        mainContentContainer.addArrangedSubview(progressContainer)

        // 用户信息容器固定高度
        userInfoContainer.snp.makeConstraints { make in
            make.height.equalTo(75)
        }

        // 用户头像
        userInfoContainer.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }
        
        userInfoContainer.addSubview(expInfoLabel)
        

        // 用户信息标签
        let userInfoStack = UIStackView(arrangedSubviews: [nicknameLabel, levelLabel, statusLabel])
        userInfoStack.axis = .vertical
        userInfoStack.spacing = 4
        userInfoStack.alignment = .leading
        
        levelLabel.snp.makeConstraints { make in
            make.height.equalTo(14)
        }
        
   
        userInfoContainer.addSubview(userInfoStack)
        userInfoStack.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.centerY.equalToSuperview()
        }
        
        expInfoLabel.snp.makeConstraints { make in
            make.centerY.equalTo(statusLabel)
            make.right.equalTo(-20)
            make.height.equalTo(21)
        }


        // 倒计时容器
        contentView.addSubview(countdownLabel)
        countdownLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.height.equalTo(18)
            make.top.equalTo(titleImgView.snp.bottom).offset(8)
        }

        // 设置进度条容器结构
        // 1. 进度条行容器（进度条 + 右侧进度说明）
        progressRowContainer.addSubview(progressView)
        progressRowContainer.addSubview(progressStatusLabel)

        progressView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.right.equalTo(progressStatusLabel.snp.left).offset(-12)
            make.height.equalTo(8)
        }

        progressStatusLabel.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
            make.width.greaterThanOrEqualTo(80)
        }

        // 2. 添加到 StackView
        progressContainer.addArrangedSubview(progressRowContainer)
        progressContainer.addArrangedSubview(progressLabel)

        // 设置进度条容器的内边距
        progressContainer.layoutMargins = UIEdgeInsets(top: 0, left: 20, bottom: 0, right: 20)
        progressContainer.isLayoutMarginsRelativeArrangement = true

        // 添加分割虚线 - 在主内容容器下面
        mainBackgroundView.addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(mainContentContainer.snp.bottom).offset(15)
            make.height.equalTo(1)
        }

        // 添加收益信息容器（下半部分）- 紧跟虚线
        mainBackgroundView.addSubview(rewardInfoContainer)
        rewardInfoContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(separatorLine.snp.bottom).offset(15)
            // 不再固定到底部，让内容自然排列
        }

        // 收益标题
        rewardInfoContainer.addSubview(rewardTitleLabel)
        rewardTitleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
            make.height.equalTo(20)
        }

        // 收益容器
        rewardInfoContainer.addSubview(rewardContainer)
        rewardContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(20)
            make.top.equalTo(rewardTitleLabel.snp.bottom).offset(12)
            make.height.equalTo(44)
            make.bottom.equalTo(-2)
        }

        // 收益项目 CollectionView
        rewardContainer.addSubview(rewardCollectionView)
        rewardCollectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 主按钮
        contentView.addSubview(actionButton)
        actionButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.equalTo(mainBackgroundView.snp.bottom).offset(16)
            make.height.equalTo(44)
        }

        // 提示文字
        contentView.addSubview(tipLabel)
        tipLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.equalTo(actionButton.snp.bottom).offset(8)
            make.bottom.lessThanOrEqualToSuperview().offset(-20)
        }

        // 设置虚线
        setupDashedLine()
    }


    // MARK: - Data Update Methods

    private func setupDashedLine() {
        // 创建虚线
        let dashedLayer = CAShapeLayer()
        dashedLayer.strokeColor = UIColor.lightGray.cgColor
        dashedLayer.lineWidth = 1
        dashedLayer.lineDashPattern = [4, 4] // 虚线样式：4点实线，4点空白

        separatorLine.layer.addSublayer(dashedLayer)

        // 延迟设置路径，确保frame已经设置
        DispatchQueue.main.async {
            let path = CGMutablePath()
            path.addLines(between: [CGPoint(x: 0, y: 0.5), CGPoint(x: self.separatorLine.bounds.width, y: 0.5)])
            dashedLayer.path = path
        }
    }

    private func updateUI(with model: MansionQuerData) {
        // 更新标题图片
        titleImgView.image = UIImage(named: "mtask_\(model.taskId)")

        // 根据taskId设置背景图片
        updateBackgroundImage(for: model.taskId)

        // 控制进度条显示/隐藏 (taskId 1和4没有进度条)
        let shouldShowProgress = model.taskId != 1 && model.taskId != 4
        progressContainer.isHidden = !shouldShowProgress

        // 根据taskId设置主题样式
        applyTheme(for: model.taskId)

        // 更新收益信息
        updateRewardInfo(with: model)

        // 通知PanModal更新高度
        panModalSetNeedsLayoutUpdate()
    }

    private func updateBackgroundImage(for taskId: Int) {
        // 根据是否有进度条选择不同的背景图片
        let hasProgress = taskId != 1 && taskId != 4
        let backgroundImageName = hasProgress ? "task_centerBg" : "task_centerBg_1"
        mainBackgroundView.image = UIImage(named: backgroundImageName)
        mainBackgroundView.snp.updateConstraints { make in
            make.height.equalTo(hasProgress ? 247 : 210)
        }
    }

    private func updateAttendantInfo() {
        guard let attendant = attendant else { return }

        avatarImageView.setImage(from: attendant.photoUrl)
        nicknameLabel.text = attendant.nickName
        levelLabel.titleLab.text = "Lv.\(attendant.attendantUserLevel)跟班"

        // 更新状态
        if let taskName = attendant.attendantTaskName, !taskName.isEmpty {
            statusLabel.text = "\(taskName)中..."
        } else {
            statusLabel.text = "休息中..."
        }
    }

    private func updateWorkingState() {
        if isWorking {
            // 工作中状态
            rewardTitleLabel.text = "已产生收益"
            actionButton.setTitle("终止派遣", for: .normal)
            tipLabel.text = "终止派遣也可获得当前已产生的收益"
            countdownLabel.isHidden = false
            mainBackgroundView.snp.updateConstraints { make in
                make.top.equalTo(titleImgView.snp.bottom).offset(30)
            }
        } else {
            // 未工作状态
            rewardTitleLabel.text = "每小时预计收益"
            actionButton.setTitle("进行派遣", for: .normal)
            tipLabel.text = "派遣后雇佣时间结束前会一直为你带来收益"
            countdownLabel.isHidden = true
            mainBackgroundView.snp.updateConstraints { make in
                make.top.equalTo(titleImgView.snp.bottom).offset(16)
            }
        }
    }

    func timeDiffFromNowTo(_ millis: Int64) -> String {
        let startDate = Date(timeIntervalSince1970: TimeInterval(millis) / 1000.0)
        let now = Date()
        let diff = abs(now.timeIntervalSince(startDate)) // 取绝对值，防止负数

        let hours = Int(diff) / 3600
        let minutes = (Int(diff) % 3600) / 60

        return String(format: "%02d时%02d分", hours, minutes)
    }
    
    private func updateRewardInfo(with model: MansionQuerData) {
        // 更新进度条文字
        if  model.minuteValue > 0 {
            expInfoLabel.titleLab.attributed.text = "收益: \(model.minuteValue, .foreground(.init(hex: 0xFF5B5B) ?? .white))\(model.minuteValueRemarks ?? "")/\(model.minuteRemarks ?? "")"
        } else {
            expInfoLabel.titleLab.attributed.text = "收益: \(model.minuteValueRemarks ?? "")/\(model.minuteRemarks ?? "")"
        }
        
        if model.startDate > 0 {
            countdownLabel.titleLab.text = timeDiffFromNowTo(Int64(model.startDate))
        }
        


        if model.expMaxValue > 0 {
            // 更新进度状态
            progressStatusLabel.text = "\(model.expCurValue)/\(model.expMaxValue)经验"
            let pp = CGFloat(model.expCurValue) / CGFloat(model.expMaxValue)
            progressView.setProgress(pp)
            
            UIImageView().setImageWithCallback(from: model.additionalRewardUrl) { isSuccess, originImg in
                self.progressLabel.attributed.text = "经验值满可获得\(.image(originImg ?? UIImage(), .custom(.center, size: CGSize(width: model.taskId == 5 ? 50 : 20, height: 20))))\(model.additionalNumRemarks ?? "")"
            }
        }
        
        // 更新收益项目
        updateRewardItems(with: model)
    }

    private func updateRewardItems(with model: MansionQuerData) {
        
        if !isWorking {
            var rr = MansionRewardModel()
            rr.rewardUrl = model.expectHourRewardUrl
            rr.rewardValue = model.expectHourRewardValue
            rr.rewardNumRemarks = model.minuteNumRemarks
            rewardItems = [rr]
        } else {
            rewardItems = model.rewardList
        }
        
        
        
        // 刷新 CollectionView
        rewardCollectionView.reloadData()
    }

    // MARK: - Theme Methods

    private func applyTheme(for taskId: Int) {
        switch taskId {
        case 1:
            applyTheme1()
        case 2:
            applyTheme2()
        case 3:
            applyTheme3()
        case 4:
            applyTheme4()
        case 5:
            applyTheme5()
        case 6:
            applyTheme6()
        default:
            applyTheme1()
        }
    }

}


// MARK: - Theme Styles
extension MansionTaskSheetView {

    private func applyTheme1() {
        // 挖矿金币 - 金色主题
        // 设置整体渐变背景
        applyGradientBackground(colors: [UIColor(hex: 0xF8DC9A)!, UIColor(hex: 0xFDF8EA)!])

        countdownLabel.backgroundColor = UIColor(hex: 0x8A674B)?.withAlphaComponent(0.1)
        expInfoLabel.backgroundColor = UIColor(hex: 0x8A674B)?.withAlphaComponent(0.1)
        expInfoLabel.titleLab.textColor = UIColor(hex: 0xC5732C)
        countdownLabel.titleLab.textColor = UIColor(hex: 0xC5732C)
        progressLabel.textColor = UIColor(hex: 0xC5732C)
        progressStatusLabel.textColor = UIColor(hex: 0xC5732C)
        progressView.progressColor = UIColor(hex: 0xFFA714)
        actionButton.backgroundColor = UIColor(hex: 0xFCD89D)
        actionButton.setTitleColor(UIColor(hex: 0x88250E), for: .normal)
        actionButton.layer.borderColor = UIColor(hex: 0xFFA714)?.cgColor
        actionButton.layer.borderWidth = 1
        tipLabel.textColor = UIColor(hex: 0xC5732C)
        

        // 更新虚线颜色
        updateDashedLineColor(UIColor(hex: 0xFFE286)?.withAlphaComponent(0.8))
    }

    private func applyTheme2() {
        // 辅助晶石 - 蓝绿主题
        // 设置整体渐变背景
        applyGradientBackground(colors: [UIColor(hex: 0xBAE8FF)!, UIColor(hex: 0x89EDE8)!])

        countdownLabel.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        countdownLabel.titleLab.textColor = UIColor(hex: 0x007EA3)
        
        expInfoLabel.backgroundColor = UIColor(hex: 0x007EA3)?.withAlphaComponent(0.2)
        expInfoLabel.titleLab.textColor = UIColor(hex: 0x007EA3)
        
        progressLabel.textColor = UIColor(hex: 0x007EA3)
        progressStatusLabel.textColor = UIColor(hex: 0x007EA3)
        progressView.progressColor = UIColor(hex: 0x86E0FF)
        actionButton.backgroundColor = UIColor(hex: 0x6FE6F1)
        actionButton.setTitleColor(UIColor(hex: 0x007EA3), for: .normal)
        actionButton.layer.borderColor = UIColor(hex: 0x14FFF8)?.cgColor
        actionButton.layer.borderWidth = 1
        tipLabel.textColor = UIColor(hex: 0x007EA3)

        // 更新虚线颜色
        updateDashedLineColor(UIColor(hex: 0x86E0FF)?.withAlphaComponent(0.8))
    }

    private func applyTheme3() {
        // 人气聚集 - 紫色主题
        // 设置整体渐变背景
        applyGradientBackground(colors: [UIColor(hex: 0xBFD0FF)!, UIColor(hex: 0xBFBDFF)!])

        countdownLabel.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        countdownLabel.titleLab.textColor = UIColor(hex: 0x772BC6)
        
        expInfoLabel.backgroundColor = UIColor(hex: 0x772BC6)?.withAlphaComponent(0.2)
        expInfoLabel.titleLab.textColor = UIColor(hex: 0x772BC6)
        
        progressLabel.textColor = UIColor(hex: 0x772BC6)
        progressStatusLabel.textColor = UIColor(hex: 0x772BC6)
        progressView.progressColor = UIColor(hex: 0xAF86FF)
        actionButton.backgroundColor = UIColor(hex: 0xAEB4FF)
        actionButton.setTitleColor(UIColor(hex: 0x772BC6), for: .normal)
        actionButton.layer.borderColor = UIColor(hex: 0x9792FF)?.cgColor
        actionButton.layer.borderWidth = 1
        tipLabel.textColor = UIColor(hex: 0x772BC6)

        // 更新虚线颜色
        updateDashedLineColor(UIColor(hex: 0xAF86FF)?.withAlphaComponent(0.8))
    }

    private func applyTheme4() {
        // 蓝色主题
        // 设置整体渐变背景
        applyGradientBackground(colors: [UIColor(hex: 0xA9BFFF)!, UIColor(hex: 0xCAE2FF)!])

        countdownLabel.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        countdownLabel.titleLab.textColor = UIColor(hex: 0x0051C6)
        
        expInfoLabel.backgroundColor = UIColor(hex: 0x0051C6)?.withAlphaComponent(0.2)
        expInfoLabel.titleLab.textColor = UIColor(hex: 0x0051C6)
        
        progressLabel.textColor = UIColor(hex: 0x0051C6)
        progressStatusLabel.textColor = UIColor(hex: 0x0051C6)
        progressView.progressColor = UIColor(hex: 0x86A2FF)
        actionButton.backgroundColor = UIColor(hex: 0x8DB7FF)
        actionButton.setTitleColor(UIColor(hex: 0x0051C6), for: .normal)
        actionButton.layer.borderColor = UIColor(hex: 0x8CAAFF)?.cgColor
        actionButton.layer.borderWidth = 1
        tipLabel.textColor = UIColor(hex: 0x0051C6)

        // 更新虚线颜色
        updateDashedLineColor(UIColor(hex: 0x86A2FF)?.withAlphaComponent(0.8))
    }

    private func applyTheme5() {
        // 粉红主题
        // 设置整体渐变背景
        applyGradientBackground(colors: [UIColor(hex: 0xFFC2B4)!, UIColor(hex: 0xFFDCCF)!])

        countdownLabel.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        countdownLabel.titleLab.textColor = UIColor(hex: 0xD63838)
        
        expInfoLabel.backgroundColor = UIColor(hex: 0xD63838)?.withAlphaComponent(0.2)
        expInfoLabel.titleLab.textColor = UIColor(hex: 0xD63838)
        
        progressLabel.textColor = UIColor(hex: 0xD63838)
        progressStatusLabel.textColor = UIColor(hex: 0xD63838)
        progressView.progressColor = UIColor(hex: 0xFFA286)
        actionButton.backgroundColor = UIColor(hex: 0xFFA893)
        actionButton.setTitleColor(UIColor(hex: 0xD63838), for: .normal)
        actionButton.layer.borderColor = UIColor(hex: 0xFFA58C)?.cgColor
        actionButton.layer.borderWidth = 1
        tipLabel.textColor = UIColor(hex: 0xD63838)

        // 更新虚线颜色
        updateDashedLineColor(UIColor(hex: 0xFFA286)?.withAlphaComponent(0.8))
    }

    private func applyTheme6() {
        // 橙色主题
        // 设置整体渐变背景
        applyGradientBackground(colors: [UIColor(hex: 0xFDD7A4)!, UIColor(hex: 0xFBF1B5)!])

        countdownLabel.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        countdownLabel.titleLab.textColor = UIColor(hex: 0xD6572E)
        expInfoLabel.backgroundColor = UIColor(hex: 0xD6572E)?.withAlphaComponent(0.2)
        expInfoLabel.titleLab.textColor = UIColor(hex: 0xD6572E)
        progressLabel.textColor = UIColor(hex: 0xD6572E)
        progressStatusLabel.textColor = UIColor(hex: 0xD6572E)
        progressView.progressColor = UIColor(hex: 0xFFA986)
        actionButton.backgroundColor = UIColor(hex: 0xFFC590)
        actionButton.setTitleColor(UIColor(hex: 0xD6572E), for: .normal)
        actionButton.layer.borderColor = UIColor(hex: 0xFFB48C)?.cgColor
        actionButton.layer.borderWidth = 1
        tipLabel.textColor = UIColor(hex: 0xD6572E)

        // 更新虚线颜色
        updateDashedLineColor(UIColor(hex: 0xFFA986)?.withAlphaComponent(0.8))
    }

    private func updateDashedLineColor(_ color: UIColor?) {
        guard let dashedLayer = separatorLine.layer.sublayers?.first as? CAShapeLayer else { return }
        dashedLayer.strokeColor = color?.cgColor ?? UIColor.lightGray.cgColor
    }

    private func applyGradientBackground(colors: [UIColor]) {
        // 计算当前高度
        let hasProgress = model?.taskId != 1 && model?.taskId != 4
        let baseHeight: CGFloat = 350
        let progressHeight: CGFloat = 60
        let totalHeight = baseHeight + (hasProgress ? progressHeight : 0) + AppTool.safeAreaBottomHeight

        contentView.backgroundColor = UIImage(dvt: colors, size: CGSizeMake(AppTool.screenWidth, totalHeight), direction: .top2bottom)?.imageToColor()
    }
}

// MARK: - Actions & Timer
extension MansionTaskSheetView {

    @objc private func actionButtonTapped() {
        if isWorking {
            // 终止派遣
            onTerminateTask?()
        } else {
            // 开始派遣
            onStartTask?()
        }
    }
}

// MARK: - PanModal Configuration
extension MansionTaskSheetView {

    override func anchorModalToLongForm() -> Bool {
        return false
    }

    override func cornerRadius() -> CGFloat {
        return 18
    }

    override func allowsDragToDismiss() -> Bool {
        return true
    }

    override func showDragIndicator() -> Bool {
        return false
    }

    override func longFormHeight() -> PanModalHeight {
        return shortHeight
    }

    override func shortFormHeight() -> PanModalHeight {
        return longFormHeight()
    }

    override func shouldRespond(toPanModalGestureRecognizer panGestureRecognizer: UIPanGestureRecognizer) -> Bool {
        let loc = panGestureRecognizer.location(in: self)
        return !CGRectContainsPoint(self.frame, loc);
    }
    
    override func panModalDidDismissed() {
        super.panModalDidDismissed()
    }
}

// MARK: - CollectionView DataSource & Delegate
extension MansionTaskSheetView: UICollectionViewDataSource, UICollectionViewDelegate {

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return rewardItems.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "MansionRewardCollectionViewCell", for: indexPath) as! MansionRewardCollectionViewCell

        let item = rewardItems[indexPath.item]
        cell.configure(
            with: item.rewardUrl,
            rewardType: item.rewardType,
            rewardNumRemarks: item.rewardNumRemarks,
            taskId: model?.taskId ?? 1
        )

        return cell
    }
}

// MARK: - MansionRewardCollectionViewCell
class MansionRewardCollectionViewCell: UICollectionViewCell {

    // SVGA 动画视图
    lazy var svgaView: SVGAnimationPlayer = {
        let view = SVGAnimationPlayer()

        view.isHidden = true
        return view
    }()

    // 静态图片视图
    lazy var imageView: UIImageView = {
        let imgView = UIImageView()
        imgView.contentMode = .scaleAspectFit
        return imgView
    }()

    // 数量文字
    lazy var numberLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(18)
        label.textColor = .init(hex: 0xC5732C)
        label.textAlignment = .left
        return label
    }()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        // 添加子视图到 contentView
        contentView.addSubview(svgaView)
        contentView.addSubview(imageView)
        contentView.addSubview(numberLabel)

        // 左右布局：图标在左，数字在右
        svgaView.snp.makeConstraints { make in
            make.left.centerY.equalTo(contentView)
            make.width.height.equalTo(32)
        }

        imageView.snp.makeConstraints { make in
            make.left.centerY.equalTo(contentView)
            make.width.height.equalTo(32)
        }

        numberLabel.snp.makeConstraints { make in
            make.left.equalTo(imageView.snp.right).offset(8)
            make.centerY.right.equalTo(contentView)
        }
    }

    // 配置收益项目
    func configure(with rewardUrl: String?, rewardType: Int, rewardNumRemarks: String?, taskId: Int) {
        // 设置数量文字
        numberLabel.text = rewardNumRemarks ?? ""

        // 根据 taskId 设置文字颜色
        switch taskId {
        case 1:
            numberLabel.textColor = UIColor(hex: 0xC5732C)
        case 2:
            numberLabel.textColor = UIColor(hex: 0x007EA3)
        case 3:
            numberLabel.textColor = UIColor(hex: 0x772BC6)
        case 4:
            numberLabel.textColor = UIColor(hex: 0x0051C6)
        case 5:
            numberLabel.textColor = UIColor(hex: 0xD63838)
        case 6:
            numberLabel.textColor = UIColor(hex: 0xD6572E)
        default:
            numberLabel.textColor = .black
        }

        // 处理图标显示
        if let url = rewardUrl, url.contains(".svga") {
            // 显示 SVGA 动画
            svgaView.isHidden = false
            imageView.isHidden = true
            // TODO: 加载 SVGA 动画
            // svgaView.setAnimation(from: url)
            svgaView.parseAndStartAnimation(from: URL(string: url)!)
        } else {
            // 显示静态图片
            svgaView.isHidden = true
            imageView.isHidden = false

            // 根据 rewardType 调整图片尺寸
            let imageSize: CGSize
            if rewardType == 5 {
                imageSize = CGSize(width: 55, height: 20) // 特殊奖励尺寸
            } else {
                imageSize = CGSize(width: 32, height: 32) // 普通图标尺寸
            }

            imageView.snp.remakeConstraints { make in
                make.left.centerY.equalTo(contentView)
                make.width.equalTo(imageSize.width)
                make.height.equalTo(imageSize.height)
            }

            // 加载图片
            if let url = rewardUrl {
                imageView.setImage(from: url)
            }
        }
    }
}
