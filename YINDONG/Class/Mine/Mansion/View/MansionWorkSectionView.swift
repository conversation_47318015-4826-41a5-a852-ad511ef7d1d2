//
//  MansionWorkSectionView.swift
//  YINDONG
//
//  Created by jj on 2025/6/18.
//

import UIKit

protocol MansionWorkSectionViewDelegate: AnyObject {
    func workSectionView(_ view: MansionWorkSectionView, didSelectWork work: WorkListModel)
}

class MansionWorkSectionView: UIView {
    
    // MARK: - Properties
    
    weak var delegate: MansionWorkSectionViewDelegate?
    private var workList: [WorkListModel] = []
    
    // MARK: - UI Components
    
    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "派遣工种"
        label.font = .medium(14)
        label.textColor = .init(hex: 0xFFE286)
        return label
    }()
    
    /// 可派遣数量标签
    private lazy var availableLabel: UIButton = {
        let label = UIButton(title: "派遣收益>")
        label.titleLabel?.font = .regular(12)
        label.setTitleColor(.init(hex: 0xF9BF93), for: .normal)
        label.addTarget(self, action: #selector(shouyiAct))
        return label
    }()
    
    /// 工种集合视图
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 12
        layout.minimumLineSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(MansionWorkCell.self, forCellWithReuseIdentifier: "MansionWorkCell")
        collectionView.isScrollEnabled = false
        return collectionView
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    @objc
    func shouyiAct() {
        let v = MansionHisPopView()
        v.show()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        // 添加标题
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalTo(16)
        }
        
        // 添加可派遣数量标签
        addSubview(availableLabel)
        availableLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel)
            make.right.equalToSuperview().inset(16)
        }
        
        // 添加集合视图
        addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    // MARK: - Configuration
    
    func configure(with workList: [WorkListModel]) {
        self.workList = workList
        
        // 更新集合视图高度
        updateCollectionViewHeight()
        self.collectionView.reloadData()
        
    }
    
    private func updateCollectionViewHeight() {
        let itemsPerRow = 3
        let spacing: CGFloat = 14
        let itemHeight: CGFloat = 54
        
        let totalRows = (workList.count + itemsPerRow - 1) / itemsPerRow
        let totalHeight = CGFloat(totalRows) * itemHeight + CGFloat(max(0, totalRows - 1)) * spacing
        
        collectionView.snp.remakeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(totalHeight + 32)
        }
    }
}

// MARK: - UICollectionViewDataSource
extension MansionWorkSectionView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return workList.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "MansionWorkCell", for: indexPath) as! MansionWorkCell
        cell.configure(with: workList[indexPath.item])
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension MansionWorkSectionView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        var work = workList[indexPath.item]
        if work.isUnlock == 0 || work.isDispatch == 1 {
            return
        }
        
        workList.forEach { mm in
            mm.isSeleted = false
        }
        
        work.isSeleted = true
        collectionView.reloadData()
        
        delegate?.workSectionView(self, didSelectWork: work)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension MansionWorkSectionView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let itemsPerRow: CGFloat = 3
        let spacing: CGFloat = 14
        let totalSpacing = spacing * (itemsPerRow - 1)
        let itemWidth = (AppTool.screenWidth - 32 - totalSpacing) / itemsPerRow
        let itemHeight: CGFloat = 54
        
        return CGSize(width: itemWidth, height: itemHeight)
    }
}

// MARK: - MansionWorkCell
class MansionWorkCell: UICollectionViewCell {
    
    // MARK: - UI Components
    lazy var svgaView: SVGAnimationPlayer = {
        let vv = SVGAnimationPlayer()
        vv.isAutoPlayEnabled = false
        vv.stopAnimation()
        vv.isHidden = true
        return vv
    }()
    
    /// 背景图片
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.layerCornerRadius = 8
        return imageView
    }()
    
    /// 文字标签
    private lazy var textLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(12)
        label.textAlignment = .center
        label.numberOfLines = 1
        return label
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
//        contentView.layer.cornerRadius = 8
//        contentView.clipsToBounds = true
        
        contentView.addSubview(svgaView)
        svgaView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(-17)
            make.width.equalTo(140.auto())
            make.height.equalTo(60.auto())
        }
        
        // 添加背景图片
        contentView.addSubview(backgroundImageView)
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加文字标签
        contentView.addSubview(textLabel)
        textLabel.snp.makeConstraints { make in
            make.left.equalTo(10)
            make.bottom.equalToSuperview().offset(-8)
        }
    }
    
    // MARK: - Configuration
    
    func configure(with work: WorkListModel) {
        // 设置背景图片
        backgroundImageView.setImage(from: work.background)
        
        if work.isUnlock == 0 || work.isDispatch == 1 {
            backgroundImageView.alpha = 0.6
        } else {
            backgroundImageView.alpha = 1
        }
        
        // 设置文字
        textLabel.text = "跟班\(work.remarks ?? "")可入"
        
        
        // 设置文字颜色
        if let colorHex = work.textColour, !colorHex.isEmpty {
            textLabel.textColor = .init(hexString: colorHex)
        } else {
            textLabel.textColor = .white
        }
        
        
        if work.isSeleted {
            svgaView.isHidden = false
            svgaView.isAutoPlayEnabled = true
            svgaView.parseAndStartAnimation(named: "work_new_select")
        } else {
            svgaView.isHidden = true
            svgaView.stopAnimation()
            svgaView.isAutoPlayEnabled = false
        }
        
    }
}
