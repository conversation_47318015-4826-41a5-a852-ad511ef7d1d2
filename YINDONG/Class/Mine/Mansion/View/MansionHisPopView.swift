//
//  MansionHisPopView.swift
//  YINDONG
//
//  Created by jj on 2025/6/20.
//

import UIKit
import Atributika
import AtributikaViews
import EmptyDataSet_Swift
import YYText

class MansionHisPopView: CustomPopupView {
    
    var page = 1

    lazy var titleLabel: UILabel = {
       let label = UILabel()
       label.text = "派遣收益"
       label.font = .medium(16)
       label.textColor = .white
       return label
   }()

    lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.separatorStyle = .none
        tableView.backgroundColor = .clear
        tableView.showsVerticalScrollIndicator = false
        tableView.delegate = self
        tableView.dataSource = self
        tableView.emptyDataSetSource = self
        tableView.emptyDataSetDelegate = self
        tableView.register(MansionHisCell.self, forCellReuseIdentifier: "MansionHisCell")
        return tableView
    }()
    
    var datas: [MansionPositionModel] = []
    
    override func configureUI() {
        super.configureUI()

        frame = CGRect(x: 0, y: 0, width: 299, height: 410)
        backgroundColor = .init(hex: 0x2C2B43)
        layer.cornerRadius = 12

        setupUI()
        loadData()
    }

    private func setupUI() {
        addSubview(titleLabel)
        addSubview(tableView)

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.centerX.equalToSuperview()
        }


        tableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(10)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-30)
        }
        
        let tipLab = UILabel(withText: "最多展示最近7天的数据", textColor: .textColor6, fontType: .regular(10))
        addSubview(tipLab)
        tipLab.snp.makeConstraints { make in
            make.bottom.equalTo(-8)
            make.centerX.equalToSuperview()
        }
        
    }
    
    func loadData() {
        var dict: [String: Any] = ["page": page]
        NetworkUtility.request(target: .hireMansionsHistory(dict), model: MansionPositionModel.self, isList: true, completion: { result in
            if result.isError { return }
            self.datas = result.modelArr
            self.tableView.reloadData()
        })
    }

    @objc private func closeButtonTapped() {
        dismiss()
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate
extension MansionHisPopView: UITableViewDataSource, UITableViewDelegate {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return datas.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "MansionHisCell", for: indexPath) as! MansionHisCell
        cell.configure(with: datas[indexPath.row])
        cell.dissMissAction = { [weak self] in
            self?.dismiss()
        }
        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }
}

// MARK: - EmptyDataSetSource & EmptyDataSetDelegate
extension MansionHisPopView: EmptyDataSetSource, EmptyDataSetDelegate {

    func image(forEmptyDataSet scrollView: UIScrollView) -> UIImage? {
        return UIImage(named: "nodata_base")
    }

    func title(forEmptyDataSet scrollView: UIScrollView) -> NSAttributedString? {
        let attributedString = NSMutableAttributedString(string: "暂无派遣记录")
        attributedString.yy_font = .regular(14)
        attributedString.yy_color = .init(hex: 0x999999)
        return attributedString
    }

    func spaceHeight(forEmptyDataSet scrollView: UIScrollView) -> CGFloat {
        return 10
    }

    func verticalOffset(forEmptyDataSet scrollView: UIScrollView) -> CGFloat {
        return -40
    }

    func emptyDataSetShouldAllowScroll(_ scrollView: UIScrollView) -> Bool {
        return true
    }
}

class MansionHisCell: UITableViewCell {
    var dissMissAction: (() -> Void)?

    lazy var titleLabel: InfoItemBaseView = {
        let label = InfoItemBaseView(yyRightImg: "xing_icon", leftTitle: "", isLeft: true, space: 2, imgWH: CGSizeMake(8, 8), margin: 0)
        label.yyTextLab.font = .medium(14)
        label.yyTextLab.textColor = .white
        
        return label
    }()

    private let timeLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(12)
        label.textColor = UIColor(hex: 0x999999)
        label.textAlignment = .right
        return label
    }()

    private let messageLabel: AttributedLabel = {
        let label = AttributedLabel()
        label.numberOfLines = 0
        label.font = .regular(12)
        label.textColor = UIColor(hex: 0xCCCCCC) ?? UIColor.white
        return label
    }()
    
    lazy var tipLab: UILabel = {
        let lab = UILabel(withText: "最多展示最近7天的数据")
        
        return lab
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        contentView.addSubview(titleLabel)
        contentView.addSubview(timeLabel)
        contentView.addSubview(messageLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview()
            make.right.lessThanOrEqualTo(timeLabel.snp.left).offset(-10)
            make.height.equalTo(20)
        }
        

        timeLabel.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.width.greaterThanOrEqualTo(100)
            make.centerY.equalTo(titleLabel)
        }

        messageLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.right.equalToSuperview()
            make.left.equalTo(10)
            make.bottom.equalToSuperview().offset(-12)
        }
    }

    func configure(with model: MansionPositionModel) {
        // 设置标题 - 根据 endType 和 attendantUserIm 设置不同的显示逻辑
        configureTitleLabel(with: model)

        // 设置时间
        timeLabel.text = formatDate(timestamp: model.createDate)

        // 设置富文本内容
        if let content = model.content2 {
            let attributedText = content.styleByAttrs(
                allFont: .regular(12),
                nomalColor: UIColor(hex: 0xCCCCCC),
                hrefColor: UIColor(hex: 0xF9BF93) ?? UIColor.white
            ).attributedString
            messageLabel.attributedText = attributedText
        }
    }

    private func configureTitleLabel(with model: MansionPositionModel) {
        
        var attributedText = NSMutableAttributedString()

        // 添加钻石图标（如果需要的话，这里可以根据 endType 调整）
        let content1 = model.content1 ?? ""
        let nickName = model.nickName ?? ""

        // 根据 endType 判断显示逻辑
        switch model.endType {
        case 0:
            // endType = 0 的逻辑
            configureTitle(attributedText: attributedText, content1: content1, nickName: nickName, attendantUserIm: model.attendantUserIm)

        case 1, 2, 3:
            // endType = 1, 2, 3 的逻辑
            configureTitle(attributedText: attributedText, content1: content1, nickName: nickName, attendantUserIm: model.attendantUserIm)

        default:
            // 默认逻辑
            configureTitle(attributedText: attributedText, content1: content1, nickName: nickName, attendantUserIm: model.attendantUserIm)
        }
        
        if model.attendantUserIm > 0, let range = AppTool.findRanges(of: attributedText.string, in: nickName).first {
            attributedText.yy_setTextHighlight(range, color: .init(hex: 0xFFE286), backgroundColor: .clear) { vv, string, rangg, rect in
                LWJumpManger.goUserPage(id: model.attendantUserIm)
                self.dissMissAction?()
            }
           
        }
//        titleLabel.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(ggg)))
        
        titleLabel.yyTextLab.attributedText = attributedText
      
    }


    private func configureTitle(attributedText: NSMutableAttributedString, content1: String, nickName: String, attendantUserIm: Int) {
        // 添加 content1 部分（如"终止派遣"）
        let content1Attr = NSMutableAttributedString(string: content1)
        content1Attr.yy_font = .regular(14)
        content1Attr.yy_color = .white
        attributedText.append(content1Attr)

        // 如果有昵称，添加空格和昵称
        if !nickName.isEmpty {
            let spaceAttr = NSMutableAttributedString(string: " ")
            spaceAttr.yy_font = .regular(14)
            spaceAttr.yy_color = .white
            attributedText.append(spaceAttr)

            let nickNameAttr = NSMutableAttributedString(string: nickName)
            nickNameAttr.yy_font = .regular(14)
            // 根据 attendantUserIm 设置颜色
            if attendantUserIm == 0 {
                nickNameAttr.yy_color = .white // 白色
            }
            attributedText.append(nickNameAttr)
        }
    }

    private func formatDate(timestamp: Int) -> String {
        let date = Date(timeIntervalSince1970: TimeInterval(timestamp / 1000))
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm:ss"
        return formatter.string(from: date)
    }
}
