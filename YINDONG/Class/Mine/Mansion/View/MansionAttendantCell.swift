//
//  MansionAttendantCell.swift
//  YINDONG
//
//  Created by jj on 2025/6/18.
//

import UIKit

protocol MansionAttendantCellDelegate: AnyObject {
    func attendantCell(_ cell: MansionAttendantCell, didTapDispatch attendant: AttendantUserList)
    func attendantCell(_ cell: MansionAttendantCell, didTapViewDetails attendant: AttendantUserList)
}

class MansionAttendantCell: UITableViewCell {
    
    // MARK: - Properties
    
    weak var delegate: MansionAttendantCellDelegate?
    private var attendant: AttendantUserList?
    
    // MARK: - UI Components
    
    /// 容器视图
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .init(hex: 0x1E1C35)
        view.layerCornerRadius = 12
        view.layerBorderColor = .white.withAlphaComponent(0.1)
        view.layerBorderWidth = 1
        return view
    }()
    
    /// 头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 25.5
        imageView.clipsToBounds = true
        imageView.layerBorderColor = .white
        imageView.layerBorderWidth = 1
        return imageView
    }()
    
    /// 昵称
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(14)
        label.textColor = .white
        return label
    }()
    
    /// 等级信息
    private lazy var levelLabel: InfoItemBaseView = {
        let label = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 8)
        label.titleLab.font = .regular(11)
        label.titleLab.textColor = .init(hex: 0xFCC294)
        label.backgroundColor = .white.withAlphaComponent(0.1)
        label.layerCornerRadius = 7
        return label
    }()
    
    /// 任务状态
    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(12)
        label.textColor = UIColor(hex: 0xFCC294)
        return label
    }()

    /// 操作按钮
    private lazy var actionButton: UIButton = {
        let button = UIButton()
        button.setTitle("进行派遣", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .regular(13)
        button.backgroundColor = UIColor.clear
        button.layerBorderWidth = 0.5
        button.layerBorderColor = .init(hex: 0xFFD99F)
        button.layer.cornerRadius = 14
        button.addTarget(self, action: #selector(actionButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        // 添加容器视图
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 0, left: 0, bottom: 12, right: 0))
        }
        
        // 添加头像
        containerView.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(52)
        }
        
        // 添加昵称
        containerView.addSubview(nameLabel)
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.top.equalTo(12)
        }
        
        // 添加等级信息
        containerView.addSubview(levelLabel)
        levelLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(4)
            make.height.equalTo(14)
        }
        
        // 添加任务状态
        containerView.addSubview(statusLabel)
        statusLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(levelLabel.snp.bottom).offset(4)
        }
        
        // 添加操作按钮
        containerView.addSubview(actionButton)
        actionButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.equalTo(76)
            make.height.equalTo(28)
        }
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(cellTapped))
        containerView.addGestureRecognizer(tapGesture)
        containerView.isUserInteractionEnabled = true
    }
    
    // MARK: - Configuration
    
    func configure(with attendant: AttendantUserList) {
        self.attendant = attendant

        // 设置头像
        avatarImageView.setImage(from: attendant.photoUrl)

        // 设置昵称
        nameLabel.text = attendant.nickName

        // 设置等级信息
        levelLabel.titleLab.text = "Lv.\(attendant.attendantUserLevel)跟班"

        // 设置任务状态（保持原有逻辑）
        if let taskName = attendant.attendantTaskName, !taskName.isEmpty {
            statusLabel.text = "\(taskName)..."
            actionButton.setTitle("查看详情", for: .normal)
            actionButton.setTitleColor(.init(hex: 0xFFE286), for: .normal)
            actionButton.layerBorderColor = .init(hex: 0xFFE286)
            actionButton.backgroundColor = .clear
            actionButton.layerBorderWidth = 1
        } else {
            statusLabel.text = "休息中..."
            actionButton.setTitle("进行派遣", for: .normal)
            actionButton.setTitleColor(.init(hex: 0x88250E), for: .normal)
            actionButton.layerBorderWidth = 0
            actionButton.defaultColor(size: CGSize(width: 76, height: 28), colors: [.init(hex: 0xFDD7A4), .init(hex: 0xFBF1B5)])
        }

        // 默认样式
        
    }

    // 新增方法：专门处理雇佣状态的显示
    func configureHiredState(timeString: String = "") {
        // 被雇佣状态
        statusLabel.text = "\(timeString)后解除雇佣"
        statusLabel.textColor = .init(hex: 0xFF5B5B)
        actionButton.setTitle("解除雇佣", for: .normal)
        actionButton.setTitleColor(.init(hex: 0xFF6B6B), for: .normal)
        actionButton.layerBorderColor = .init(hex: 0xFF6B6B)
    }

    // 更新倒计时显示（由外部定时器调用）
    func updateCountdown(timeString: String) {
        statusLabel.text = "\(timeString)后解除雇佣"
        statusLabel.textColor = .init(hex: 0xFF5B5B)
    }
    
    // MARK: - Actions
    
    @objc private func actionButtonTapped() {
        guard let attendant = attendant else { return }
        delegate?.attendantCell(self, didTapDispatch: attendant)
    }
    
    @objc private func cellTapped() {
        guard let attendant = attendant else { return }
        delegate?.attendantCell(self, didTapViewDetails: attendant)
    }
}
