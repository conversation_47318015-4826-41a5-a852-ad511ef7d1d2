//
//  MansionSytemPopView.swift
//  YINDONG
//
//  Created by jj on 2025/6/19.
//

import UIKit

class MansionSytemPopView: CustomPopupView {

    // MARK: - Properties

    var systemWorkerLevel: Int = 0 {
        didSet {
            updateUI()
        }
    }

    // MARK: - UI Components

    /// 系统跟班头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "sytem_shortIcon") // 系统跟班头像
        return imageView
    }()

    /// 系统跟班标题
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "系统跟工"
        label.font = .medium(16)
        label.textColor = .white
        return label
    }()

    /// 等级标签
    private lazy var levelLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(14)
        label.textColor = UIColor(hex: 0xFCC294)
        return label
    }()

    /// 雇佣时长标签
    private lazy var durationLabel: UILabel = {
        let label = UILabel()
        label.text = "单次雇佣24小时"
        label.font = .regular(12)
        label.textColor = UIColor(hex: 0xFCC294)
        return label
    }()

    /// 描述标签
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "套麻袋等级越高，可雇佣系统跟工等级越高"
        label.font = .regular(11)
        label.textColor = UIColor.white.withAlphaComponent(0.7)
        label.numberOfLines = 2
        label.textAlignment = .center
        return label
    }()

    /// 免费雇佣按钮
    private lazy var hireButton: UIButton = {
        let button = UIButton()
        button.setTitle("免费雇佣", for: .normal)
        button.setTitleColor(.init(hex: 0x88250E), for: .normal)
        button.titleLabel?.font = .medium(16)
        button.backgroundColor = UIColor(hex: 0xFDD7A4)
        button.layer.cornerRadius = 22
        button.addTarget(self, action: #selector(hireButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - Callbacks

    var onHireButtonTapped: (() -> Void)?

    override func configureUI() {
        super.configureUI()

        frame = CGRect(x: 0, y: 0, width: 269, height: 205)
        backgroundColor = .init(hex: 0x2C2B43)
        layer.cornerRadius = 12

        setupUI()
    }

    private func setupUI() {
        // 添加头像
        addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.top.equalTo(24)
            make.centerX.equalToSuperview()
        }

        // 添加标题
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
        }

        // 添加等级标签
        addSubview(levelLabel)
        levelLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.centerX.equalToSuperview()
        }

        // 添加雇佣时长
        addSubview(durationLabel)
        durationLabel.snp.makeConstraints { make in
            make.top.equalTo(levelLabel.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
        }

        // 添加描述
        addSubview(descriptionLabel)
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(durationLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
        }

        // 添加雇佣按钮
        addSubview(hireButton)
        hireButton.snp.makeConstraints { make in
            make.bottom.equalTo(-20)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(44)
        }
    }

    private func updateUI() {
        let displayLevel = calculateDisplayLevel(from: systemWorkerLevel)
        levelLabel.text = "Lv.\(displayLevel)跟班"
    }

    // MARK: - Level Calculation

    /// 根据 Android 逻辑计算显示等级
    private func calculateDisplayLevel(from level: Int) -> Int {
        switch level {
        case 1...4:
            return 1
        case 5...8:
            return 2
        case 9...11:
            return 3
        case 12...14:
            return 4
        default:
            return max(1, level - 10)
        }
    }

    // MARK: - Public Methods

    /// 配置系统跟班数据
    func configure(with model: MansionMainModel) {
        systemWorkerLevel = model.level

        // 根据雇佣状态更新按钮状态
        if model.systemWorkerHireStatus == 1 {
            // 已雇佣状态
            hireButton.setTitle("已雇佣", for: .normal)
            hireButton.backgroundColor = UIColor(hex: 0x666666)
            hireButton.isEnabled = false
        } else {
            // 未雇佣状态
            hireButton.setTitle("免费雇佣", for: .normal)
            hireButton.backgroundColor = UIColor(hex: 0xFDD7A4)
            hireButton.isEnabled = true
        }
    }

    // MARK: - Actions

    @objc private func hireButtonTapped() {
        onHireButtonTapped?()
        dismiss()
    }
}
