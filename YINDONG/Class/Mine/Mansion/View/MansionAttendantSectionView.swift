//
//  MansionAttendantSectionView.swift
//  YINDONG
//
//  Created by jj on 2025/6/18.
//

import UIKit

protocol MansionAttendantSectionViewDelegate: AnyObject {
    func attendantSectionViewDidTapHireSystem(_ view: MansionAttendantSectionView)
    func attendantSectionView(_ view: MansionAttendantSectionView, didTapDispatch attendant: AttendantUserList)
    func attendantSectionView(_ view: MansionAttendantSectionView, didTapViewDetails attendant: AttendantUserList)
}

class MansionAttendantSectionView: UIView {
    
    // MARK: - Properties
    
    weak var delegate: MansionAttendantSectionViewDelegate?
    private var attendantList: [AttendantUserList] = []
    
    // MARK: - UI Components
    
    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "当前跟班"
        label.font = .medium(14)
        label.textColor = .init(hex: 0xFFE286)
        return label
    }()
    
    /// 时间范围标签
    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.text = "(每日09:00-20:00可派遣)"
        label.font = .regular(11)
        label.textColor = .init(hex: 0xFFE286)
        return label
    }()
    
    /// 雇佣系统跟班按钮
    private lazy var hireButton: UIButton = {
        let button = UIButton()
        button.setTitle("雇佣系统短工", for: .normal)
        button.setTitleColor(.init(hex: 0xF9BF93), for: .normal)
        button.layerBorderColor = .init(hex: 0xF9BF93)
        button.layerBorderWidth = 0.5
        button.titleLabel?.font = .regular(11)
        button.layerCornerRadius = 9
        button.addTarget(self, action: #selector(hireButtonTapped), for: .touchUpInside)
        return button
    }()
    
    /// 跟班表格视图
    private lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(MansionAttendantCell.self, forCellReuseIdentifier: "MansionAttendantCell")
        tableView.isScrollEnabled = false
        return tableView
    }()
    
    lazy var emptyImgV: UIImageView = {
        let imgV = UIImageView(image: UIImage(named: "empty_ggIcon"))
        return imgV
    }()
    
    /// 空状态标签
    private lazy var emptyLabel: UILabel = {
        let label = UILabel()
        label.text = "可使用【套麻袋】抓跟班/系统短工\n为你打工哦~"
        label.font = .regular(13)
        label.textColor = UIColor.white.withAlphaComponent(0.6)
        label.textAlignment = .center
        label.isHidden = true
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        // 添加标题
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalTo(16)
        }
        
        // 添加时间标签
        addSubview(timeLabel)
        timeLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel.snp.right).offset(2)
            make.centerY.equalTo(titleLabel)
        }
        
        // 添加雇佣按钮
        addSubview(hireButton)
        hireButton.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalToSuperview().offset(-16)
            make.width.equalTo(82)
            make.height.equalTo(18)
        }
        
        // 添加表格视图
        addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalTo(-15)
            make.height.equalTo(200)
        }
        
        addSubview(emptyImgV)
        emptyImgV.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(60)
        }
        // 添加空状态标签
        addSubview(emptyLabel)
        emptyLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyImgV.snp.bottom).offset(4)
            make.left.right.equalToSuperview()
        }
    }
    
    // MARK: - Configuration
    
    func configure(with attendantList: [AttendantUserList]) {
        self.attendantList = attendantList
        
        let isEmpty = attendantList.isEmpty
        tableView.isHidden = isEmpty
        emptyLabel.isHidden = !isEmpty
        emptyImgV.isHidden = !isEmpty
        
        if !isEmpty {
            updateTableViewHeight()
            tableView.reloadData()
        }
    }
    
    private func updateTableViewHeight() {
        let cellHeight: CGFloat = 96
        var totalHeight = CGFloat(attendantList.count) * cellHeight + 15
        totalHeight = max(200, totalHeight)
        tableView.snp.updateConstraints { make in
            make.height.equalTo(totalHeight)
        }
    }
    
    // MARK: - Actions
    
    @objc private func hireButtonTapped() {
        delegate?.attendantSectionViewDidTapHireSystem(self)
    }
}

// MARK: - UITableViewDataSource
extension MansionAttendantSectionView: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return attendantList.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "MansionAttendantCell", for: indexPath) as! MansionAttendantCell
        cell.configure(with: attendantList[indexPath.row])
        cell.delegate = self
        return cell
    }
}

// MARK: - UITableViewDelegate
extension MansionAttendantSectionView: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 96
    }
}

// MARK: - MansionAttendantCellDelegate
extension MansionAttendantSectionView: MansionAttendantCellDelegate {
    func attendantCell(_ cell: MansionAttendantCell, didTapDispatch attendant: AttendantUserList) {
        delegate?.attendantSectionView(self, didTapDispatch: attendant)
    }
    
    func attendantCell(_ cell: MansionAttendantCell, didTapViewDetails attendant: AttendantUserList) {
        delegate?.attendantSectionView(self, didTapViewDetails: attendant)
    }
}
