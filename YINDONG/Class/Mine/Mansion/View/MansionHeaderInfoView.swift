//
//  MansionHeaderInfoView.swift
//  YINDONG
//
//  Created by jj on 2025/6/18.
//

import UIKit

class MansionHeaderInfoView: UIView {
    
    // MARK: - UI Components
    
    /// 用户头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 27
        imageView.clipsToBounds = true
        imageView.layerBorderColor = .white
        imageView.layerBorderWidth = 1
        return imageView
    }()
    
    /// 等级标签
    private lazy var levelLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(14)
        label.textColor = .white
        return label
    }()
    
    lazy var levelContentV: UIView = {
        let vv = UIView()
        vv.backgroundColor = .init(hex: 0x5F4233)
        return vv
    }()
    
    /// 技能等级图标
    private lazy var skillLevelIcon: SkillLevelRatingView = {
        let imageView = SkillLevelRatingView()
        return imageView
    }()
    
    /// 装备数量标签
    private lazy var equipmentCountLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(12)
        label.textColor = .init(hex: 0xFCC294)
        return label
    }()
    
    lazy var mangerBtn: UIButton = {
        let btn = UIButton(title: "管理", font: .medium(10))
        btn.defaultColor(size: CGSizeMake(28, 14), colors: [.init(hex: 0x4E301A), .init(hex: 0x70492C)])
        btn.layerBorderColor = .init(hex: 0xFFD99F)
        btn.layerBorderWidth = 0.5
        btn.addTarget(self, action: #selector(goManger))
        return btn
    }()
    
    /// 升级技能按钮
    private lazy var upgradeButton: UIButton = {
        let button = UIButton()
        button.tx_Img("open_skill_icon")
        button.addTarget(self, action: #selector(upgradeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Callbacks

    var onUpgradeButtonTapped: (() -> Void)?
    var onNeedRefresh: (() -> Void)?
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        
        backgroundColor = .init(hex: 0x211937)
        layerCornerRadius = 8
        layerBorderWidth = 0.5
        layerBorderColor = .white.withAlphaComponent(0.3)
        
        // 添加头像
        addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(54)
        }
        
        // 添加等级标签
    
        
        // 添加技能等级图标
        addSubview(levelContentV)
        levelContentV.snp.makeConstraints { make in
            make.height.equalTo(18)
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.top.equalTo(16)
        }
        
        let imgV = UIImageView(image: UIImage(named: "icon_skill_taomadai"))
        imgV.layerBorderColor = .white
        imgV.layerBorderWidth = 1
        imgV.layerCornerRadius = 12
        addSubview(imgV)
        imgV.snp.makeConstraints { make in
            make.width.height.equalTo(24)
            make.left.equalTo(levelContentV).offset(-3)
            make.centerY.equalTo(levelContentV)
        }
        
        levelContentV.addSubview(levelLabel)
        levelLabel.snp.makeConstraints { make in
            make.left.equalTo(30)
            make.right.equalTo(-5)
            make.centerY.equalToSuperview()
        }
        
        addSubview(skillLevelIcon)
        skillLevelIcon.snp.makeConstraints { make in
            make.left.equalTo(levelContentV.snp.right).offset(8)
            make.centerY.equalTo(levelLabel)
            make.width.equalTo(90)
            make.height.equalTo(20)
        }
        
        // 添加装备数量标签
        addSubview(equipmentCountLabel)
        equipmentCountLabel.snp.makeConstraints { make in
            make.left.equalTo(levelContentV)
            make.bottom.equalTo(avatarImageView.snp.bottom).offset(-2)
        }
        
        addSubview(mangerBtn)
        mangerBtn.snp.makeConstraints { make in
            make.centerY.equalTo(equipmentCountLabel)
            make.left.equalTo(equipmentCountLabel.snp.right).offset(4)
            make.width.equalTo(28)
            make.height.equalTo(14)
        }
        
        // 添加升级技能按钮
        addSubview(upgradeButton)
        upgradeButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
        }
        
   
    }
    
    // MARK: - Configuration
    
    var mm: MansionMainModel?
    
    func configure(with model: MansionMainModel) {
        self.mm = model
        // 设置等级
        levelLabel.text = "Lv.\(model.level)"
        upgradeButton.tx_Img(model.level == 0 ? "open_skill_icon" : "up_skill_icon")
        // 设置装备数量
        equipmentCountLabel.text = "当前装班数量：\(model.attendantCount)/\(Int(model.attendantCountMax))"
        
        // 设置用户头像
        skillLevelIcon.configureAndPlay(withLevel: model.level)
        avatarImageView.setImage(from: kuser.photoUrl)
        layoutIfNeeded()
        levelContentV.roundCorners([.topRight, .bottomRight], radius: 12)
    }
    
    // MARK: - Actions
    
    @objc private func upgradeButtonTapped() {
        onUpgradeButtonTapped?()
    }
    
    @objc
    func goManger() {
        let vc = MansionMangerListVC()
        vc.mm = mm

        // 设置刷新回调
        vc.onNeedRefresh = { [weak self] in
            // 通知外部需要刷新数据
            self?.onNeedRefresh?()
        }

        AppTool.getCurrentViewController().navigationController?.pushViewController(vc, animated: true)
    }
}
