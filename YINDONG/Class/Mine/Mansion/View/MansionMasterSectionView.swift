//
//  MansionMasterSectionView.swift
//  YINDONG
//
//  Created by jj on 2025/6/18.
//

import UIKit

class MansionMasterSectionView: UIView {
    
    // MARK: - UI Components
    
    /// 标题标签
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "主人"
        label.font = .medium(14)
        label.textColor = .init(hex: 0xFFE286)
        return label
    }()
    
    /// 主人信息容器
    private lazy var masterInfoContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .init(hex: 0x1E1C35)
        view.layerCornerRadius = 12
        view.layerBorderColor = .white.withAlphaComponent(0.1)
        view.layerBorderWidth = 1
        return view
    }()
    
    /// 主人头像
    private lazy var masterAvatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layerCornerRadius = 26
        return imageView
    }()
    
    /// 主人昵称
    private lazy var masterNameLabel: UILabel = {
        let label = UILabel()
        label.font = .medium(14)
        label.textColor = .white
        return label
    }()
    
    /// 主人等级
    private lazy var masterLevelLabel: InfoItemBaseView = {
        let label = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 8)
        label.titleLab.font = .regular(11)
        label.titleLab.textColor = .init(hex: 0xFCC294)
        label.backgroundColor = .white.withAlphaComponent(0.1)
        label.layerCornerRadius = 7
        return label
    }()
    
    private lazy var maxLab: InfoItemBaseView = {
        let label = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 8)
        label.titleLab.font = .regular(11)
        label.titleLab.textColor = .init(hex: 0xFCC294)
        label.backgroundColor = .white.withAlphaComponent(0.1)
        label.layerCornerRadius = 7
        return label
    }()
    
    /// 时间标签
    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.font = .regular(11)
        label.textColor = UIColor(hex: 0xFCC294)
        return label
    }()
    
    /// 跟身按钮
    private lazy var followButton: UIButton = {
        let button = UIButton()
        button.setTitle("赎身", for: .normal)
        button.setTitleColor(.init(hex: 0x88250E), for: .normal)
        button.titleLabel?.font = .regular(13)
        button.defaultColor1(size: CGSize(width: 58, height: 24), colors: [.init(hex: 0xFDD7A4), .init(hex: 0xFBF1B5)])
        button.layerCornerRadius = 12
        button.addTarget(self, action: #selector(followButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Callbacks
    
    var onFollowButtonTapped: (() -> Void)?
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        // 添加标题
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalTo(16)
        }
        
        // 添加主人信息容器
        addSubview(masterInfoContainer)
        masterInfoContainer.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalTo(-5)
            make.height.equalTo(84)
        }
        
        setupMasterInfoContainer()
    }
    
    private func setupMasterInfoContainer() {
        // 添加头像
        masterInfoContainer.addSubview(masterAvatarImageView)
        masterAvatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(52)
        }
        
        // 添加昵称
        masterInfoContainer.addSubview(masterNameLabel)
        masterNameLabel.snp.makeConstraints { make in
            make.left.equalTo(masterAvatarImageView.snp.right).offset(8)
            make.top.equalTo(masterAvatarImageView.snp.top).offset(5)
        }
        
        // 添加等级
        masterInfoContainer.addSubview(masterLevelLabel)
        masterLevelLabel.snp.makeConstraints { make in
            make.left.equalTo(masterNameLabel)
            make.top.equalTo(masterNameLabel.snp.bottom).offset(8)
            make.height.equalTo(14)
        }
        
        masterInfoContainer.addSubview(maxLab)
        maxLab.snp.makeConstraints { make in
            make.left.equalTo(masterLevelLabel.snp.right).offset(8)
            make.centerY.equalTo(masterLevelLabel)
            make.height.equalTo(14)
        }
        
        // 添加时间标签
        masterInfoContainer.addSubview(timeLabel)
        timeLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.top.equalTo(18)
        }
        
        // 添加跟身按钮
        masterInfoContainer.addSubview(followButton)
        followButton.snp.makeConstraints { make in
            make.right.equalTo(timeLabel)
            make.top.equalTo(timeLabel.snp.bottom).offset(8)
            make.width.equalTo(58)
            make.height.equalTo(24)
        }
    }
    
    // MARK: - Configuration
    
    func configure(with model: MansionMainModel) {
        masterAvatarImageView.setImage(from: model.masterPhotoUrl)
        masterNameLabel.text = model.masterNickName
        masterLevelLabel.titleLab.text = "Lv.\(model.level)主人"
        maxLab.titleLab.text = "跟班\(model.masterAttendantCount)/\(model.masterAttendantCountMax)"
        // 初始时间显示（具体的倒计时会通过 updateCountdown 方法更新）
        timeLabel.text = ""
    }

    // 更新倒计时显示
    func updateCountdown(timeString: String) {
        timeLabel.text = timeString
    }
    
    // MARK: - Actions
    
    @objc private func followButtonTapped() {
        onFollowButtonTapped?()
    }
}
