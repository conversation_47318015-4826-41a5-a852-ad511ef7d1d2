//
//  UserInfoModel.swift
//  YINDONG
//
//  Created by jj on 2025/3/14.
//

import UIKit

class UserInfoModel: SmartCodable {
    var accountList = [AccountListModel]()
    var atlasGiftNumCount: Int = 0
    var authentication: Int = 0
    var autograph: String?
    var backgroundUrl: String?
    var birthDay: Int = 0
    var chatPrice: Int = 0
    var city: Int = 0
    var cityName: String?
    var collectionDegree: Float = 0.0
    var constellation: String?
    var cpRankNum: Int = 0
    var createDate: Int = 0
    var emotionalList = [TagBaseModel]()
    var famesRankNum: Int = 0
    var famesValue: Int = 0
    var fansCount: Int = 0
    var fightingPoints: Float = 0.0
    var followStatus: String?   // 1我关注ta, 2ta关注我，1,2互相关注
    var giftNumCount: Int = 0
    var glamourLevel: LevelBaseModel?
    var glamourRankNum: Int = 0
    var illuminatedAtlasCount: Int = 0
    var illuminatedGiftCount: Int = 0
    var imNumber: Int = 0
    var isFriends: Int = 0
    var isMember: Int = 0
    var nickName: String?
    var nobleId: Int = 0
    var nobleUrl: String?
    var occupationList = [TagBaseModel]()
    var onLineDate: Int = 0
    var personLevel: Int = 0
    var personLevelExp: Int = 0
    var photoUrl: String?
    var powerPointsRankNum: Int = 0
    var sex: Gender = .unKnow
    var skillAttackedRecord = [String]()
    var skillLevel = [DataSkillLevelModel]()
    var userAlbumList = [UserAlbumListModel]()
    var userAtlasGiftList = [GiftListModel]()
    var userDecorateGoodsPropList = [DressBaseModel]()
    var userGiftRecordList = [String]()
    var userId: Int = 0
    var userInterestList = [TagBaseModel]()
    var userNumber: Int = 0
    var userTitleCount: Int = 0
    var userTitleList = [UserTitleListModel]()
    var userTitleWheat = [String]()
    var wantList = [TagBaseModel]()
    var wealthLevel: LevelBaseModel?
    var wealthRankNum: Int = 0
    var liveRoomNo: String?
    var liveRoomTypeContext: String?
    
    var userfriendInterestList = [TagBaseModel]()
    @SmartAny
    var picture = [String?]()
    var matchingValue: Int = 0
    var guideScriptValList: [String] = []
    
    required init() {}
}



class DataSkillLevelModel: SmartCodable {
    var runeList = [String]()
    var skillAddLevel: Int = 0
    var skillDescription: String?
    var skillLevel: Int = 0
    var skillSign: Int = 0
    var skillSort: Int = 0
    var skillTotalLevel: Int = 0
    var skillType: Int = 0
    var skillUrl: String?

    required init() {}
}


class GiftListModel: SmartCodable {
    var atlasBseDrawing: String?
    var atlasCover: String?
    var atlasId: Int = 0
    var atlasName: String?
    var atlasNum: Int = 0
    var atlasType: Int = 0
    var atlasTypeUrl: String?
    var baseDrawingGathering: String?
    var endDate: Int = 0
    var sort: Int = 0
    var startDate: Int = 0
    var userNum: Int = 0

    required init() {}
}

class AccountListModel: SmartCodable {
    var accountBalance: Float = 0.0
    var accountType: Int = 0
    var createBy: Int = 0
    var createDate: Int = 0
    var id: Int = 0
    var isDel: Int = 0
    var updateBy: Int = 0
    var updateDate: Int = 0
    var userId: Int = 0

    required init() {}
}

class UserTitleListModel: SmartCodable {
    var codUrl: String?
    var endDate: Int = 0
    var id: Int = 0
    var isWear: Int = 0
    var remarks: String?
    var updateDate: Int = 0

    required init() {}
}

class UserAlbumListModel: SmartCodable {
    var createBy: Int = 0
    var createDate: Int = 0
    var etag: String?
    var id: Int = 0
    var isDel: Int = 0
    var sort: Int = 0
    var status: Int = 0
    var updateBy: String?
    var updateDate: String?
    var url: String?
    var userId: Int = 0

    required init() {}
}


class LevelBaseModel: SmartCodable {
    var levelLevel: Int = 0
    var levelName: String?
    var levelType: Int = 0
    var levelUrl: String?
    var levelUrlStatic: String?
    var titleName: String?
    var titleUrl: String?
    var userId: Int = 0

    required init() {}
}
