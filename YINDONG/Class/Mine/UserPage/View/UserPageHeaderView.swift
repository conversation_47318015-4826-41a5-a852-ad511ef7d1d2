//
//  UserPageHeaderView.swift
//  YINDONG
//
//  Created by jj on 2025/3/18.
//

import UIKit

class UserPageHeaderView: UIView {
    
    var tags: [InfoItemBaseView] = []
    
    var model: UserInfoModel? {
        didSet {
            guard let model = model else { return }
            
            refesh(model)
        }
    }
    
    func refesh(_ m: UserInfoModel) {
        if let url = m.backgroundUrl, url.count > 2 {
            topImgV.setImage(from: m.backgroundUrl, tempUrl: "cover_defaultBg")
        } else {
            topImgV.image = UIImage(named: "cover_defaultBg")
        }
        iconImgV.avatarImageView.setImage(from: m.photoUrl)
        iconImgV.updateDecorationScale(multiplier: AvatarFrameView.defaultDecorationScale)
        if let a = m.userDecorateGoodsPropList.filter({ $0.goodsType == .avatarFrame }).first {
            iconImgV.loadDecoration(from: a.goodsUrlPreview ?? "")
        }
        nameLab.text = m.nickName
        tags[0].isHidden = m.birthDay <= 0
        tags[0].titleLab.text = m.birthDay.zodiacSign()
        
        tags[1].titleLab.text = m.cityName
        tags[1].isHidden = m.cityName?.count ?? 0 <= 0
        
        idInfoLab.text = "ID: \(m.userNumber) | 陪伴:\(m.createDate.daysSince())天"
        sexItem.reload(sex: m.sex, age: m.birthDay.ageDescription())
        tags[2].isHidden = true
        LWUserManger.getOnline(imid: m.imNumber) { isOnline in
            self.tags[2].isHidden = false
            if isOnline || (m.onLineDate >= Int((Date().timeIntervalSince1970 * 1000))) {
                self.tags[2].titleLab.text = "在线"
            } else {
                self.tags[2].titleLab.text = m.onLineDate.onlineTime(isHaveOnline: false)
            }
        }
        
        roomItem.titleLab.text = "\(m.liveRoomTypeContext ?? "")中"
        roomItem.isHidden = m.liveRoomNo?.count ?? 0 == 0
    }

    lazy var topImgV: UIImageView = {
        let banner = UIImageView()
        banner.contentMode = .scaleAspectFill
        banner.layerCornerRadius = 0
        return banner
    }()
    
    lazy var iconImgV: AvatarFrameView = {
        let banner = AvatarFrameView()
        banner.avatarImageView.layerCornerRadius = 32.5
        return banner
    }()
    
    lazy var userInfoView: UIView = {
        let vv = UIView()
        vv.backgroundColor = .white
        return vv
    }()
    
    lazy var nameLab: UILabel = {
        let nameLab = UILabel(withText: "", fontType: .bold(21))
        return nameLab
    }()
    
    lazy var sexItem: SexItemView = {
        let itemV = SexItemView()
        return itemV
    }()
    
    lazy var idInfoLab: UILabel = {
        let idInfoLab = UILabel(withText: "", fontType: .regular(10))
        return idInfoLab
    }()
    
    lazy var roomItem: InfoItemBaseView = {
        let item = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 8)
        item.layerCornerRadius = 12
        item.layerBorderColor = .themColor
        item.layerBorderWidth = 1
        item.isHidden = true
        item.titleLab.textColor = .themColor
        item.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(joinRoom)))
        return item
    }()
    
    @objc
    func joinRoom() {
        RoomManger.shared.joinRoom(id: model?.liveRoomNo)
    }
    
    convenience init() {
        self.init(frame: CGRect(x: 0, y: 0, width: AppTool.screenWidth, height: AppTool.screenWidth + 55))
        
        addSubview(topImgV)
        topImgV.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(((AppTool.screenWidth * 3) / 4 + AppTool.safeAreaTopHeight))
        }
        
        addSubview(userInfoView)
        userInfoView.snp.makeConstraints { make in
            make.top.equalTo(topImgV.snp.bottom).offset(-20)
            make.left.right.equalToSuperview()
            make.height.equalTo(105)
        }
        
        addSubview(iconImgV)
        iconImgV.snp.makeConstraints { make in
            make.bottom.equalTo(userInfoView.snp.top).offset(-20)
            make.left.equalTo(16)
            make.width.height.equalTo(65)
        }
        
        userInfoView.addSubview(nameLab)
        nameLab.snp.makeConstraints { make in
            make.top.left.equalTo(16)
        }
        
        let infoStack = UIStackView(arrangedSubviews: [], axis: .horizontal, spacing: 5, alignment: .fill, distribution: .equalSpacing)
        userInfoView.addSubview(infoStack)
        infoStack.snp.makeConstraints { make in
            make.left.equalTo(nameLab)
            make.top.equalTo(nameLab.snp.bottom).offset(5)
            make.height.equalTo(14)
        }
        
        infoStack.addArrangedSubview(sexItem)
        
        let colors: [UIColor?] = [.init(hex: 0xFBA167), .init(hex: 0xC27DFF), .init(hex: 0x5AD0D3)]
        for i in 0..<3 {
            let item = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 4)
            item.titleLab.font = .regular(10)
            item.titleLab.textColor = .white
            item.layerCornerRadius = 4
            item.backgroundColor = colors[i]
            item.isHidden = true
            infoStack.addArrangedSubview(item)
            tags.append(item)
        }
        
        userInfoView.addSubview(idInfoLab)
        idInfoLab.snp.makeConstraints { make in
            make.top.equalTo(infoStack.snp.bottom).offset(6)
            make.left.equalTo(nameLab)
        }
        
        let lineV = UIView()
        lineV.backgroundColor = .f9Color
        userInfoView.addSubview(lineV)
        lineV.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(8)
        }
        
        userInfoView.addSubview(roomItem)
        roomItem.snp.makeConstraints { make in
            make.centerY.equalTo(nameLab)
            make.right.equalToSuperview().inset(10)
            make.height.equalTo(24)
        }
        
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        userInfoView.roundCorners([.topLeft, .topRight], radius: 12)
    }

}
