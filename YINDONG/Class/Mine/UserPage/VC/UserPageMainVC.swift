//
//  UserPageMainVC.swift
//  YINDONG
//
//  Created by jj on 2025/3/18.
//

import UIKit
import JXPagingView
import JXSegmentedView

extension JXPagingListContainerView: JXSegmentedViewListContainer {}

class UserPageMainVC: YinDBaseVC {
    
    lazy var playView: LoopAnimationView = {
        let vv = LoopAnimationView(delegate: nil)
        vv.frame = CGRectMake(0, 0, AppTool.screenWidth, AppTool.screenHeight)
        vv.setUpViews()
        vv.isUserInteractionEnabled = false
        return vv
    }()
    
    var imId: String? = kuser.imNumber.string
    
    var dyCount: Int = 0
    
    lazy var pagingView: JXPagingView = JXPagingListRefreshView(delegate: self)

    lazy var userHeaderView = UserPageHeaderView()

    let dataSource: JXSegmentedTitleDataSource = JXSegmentedTitleDataSource()

    lazy var segmentedView: JXSegmentedView = JXSegmentedView(frame: CGRect(x: 0, y: 0, width: Int(AppTool.screenWidth), height: headerInSectionHeight))
    
    var titles: [String] = ["交友卡", "动态"]
    
    var headerInSectionHeight: Int = 50

    var infoModel: UserInfoModel?
    
    var cardVC: UserCardListVC?
    
    
    lazy var followButton: UIButton = {
        let btn = UIButton(frame: CGRect(x: 0, y: 0, width: 50, height: 22))
        btn.addTarget(self, action: #selector(followAction), for: .touchUpInside)
        btn.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        btn.layer.borderColor = UIColor.white.withAlphaComponent(0.6).cgColor
        btn.layer.borderWidth = 0.5
        btn.layer.cornerRadius = 11
        btn.setImage(UIImage(named: "icon_userinfo_guanzhu"), for: .normal)
        btn.setTitle("关注", for: .normal)
        btn.titleLabel?.font = UIFont.systemFont(ofSize: 9)
        btn.setTitleColor(UIColor.white, for: .normal)
        btn.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 4)
        btn.isHidden = true
        return btn
    }()


    lazy var bottomView: UserPageBottomView = {
        let vv = UserPageBottomView()
        return vv
    }()
    
    lazy var moreButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "icon_moreII")?.tint(with: .white), for: .normal)
        button.addTarget(self, action: #selector(moreAction))
        return button
    }()
    
    var isFollow: Bool {
        return infoModel?.followStatus?.contains("1") ?? false
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        navigationBarAlpha = 0
        navigationTitleColor = .white
        navigationBarTintColor = .white
        
        loadData()
        
        bottomView.cilkCallBack = { [weak self] title in
            guard let self = self else { return  }
            
            switch title {
            case "匹配":
                var dict = self.creatDict()
                dict["userId"] = self.infoModel?.userId ?? 0
                dict["remarks"] = ""
                dict["type"] = 3
                dict["privilegeSign"] = 3
                NetworkUtility.request(target: .userHello(dict)) { result in
                    if result.isError { return }
                    self.infoModel?.isFriends = 1
                    ProgressHUDManager.showTextMessage("匹配请求已发送")
                }
                
            case "聊天":
                LWJumpManger.goChat(id: self.imId)
                
            case "完善度":
                let editVC = EditMainVC()
                self.navigationController?.pushViewController(editVC)
                
            case "送礼":
                let vv = GiftPanelView(index: 0, type: .user)
                vv.user_id = self.imId
                vv.present(in: self.view)
                
            case "发布动态":
                let vc = CreatePostVC()
                self.navigationController?.pushViewController(vc)

            case "盘Ta":
                let pk = UserPkSheetView(uid: imId, name: infoModel?.nickName, onlyOne: true)
                pk.isMine = true
                pk.present(in: nil)

            default:
                break
            }
            
        }
        
        NotificationCenter.default.rx.notification(.updateInfoNotification).subscribe(onNext: { [weak self] _ in
            
            self?.relaodUI()
            
        }).disposed(by: baseDisposeBag)
        
        if imId?.int?.isIMMe == false {
            navigation.item.rightBarButtonItems = [moreButton, followButton].map({ button in
                UIBarButtonItem(customView: button)
            })
        }
        
        NotificationCenter.default.rx.notification(.dyDelNotification).subscribe(onNext: { [weak self] notification in
            guard let self = self else { return }
            self.dyCount -= 1
            if self.dyCount > 0 {
                self.dataSource.titles = ["交友卡", "动态(\(self.dyCount))"]
                self.segmentedView.reloadData()
            } else {
                self.dataSource.titles = ["交友卡", "动态"]
                self.segmentedView.reloadData()
            }
        }).disposed(by: baseDisposeBag)
        
        NotificationCenter.default.rx.notification(.dyAddNotification).subscribe(onNext: { [weak self] notification in
            guard let self = self else { return }
            self.dyCount += 1
            self.dataSource.titles = ["交友卡", "动态(\(self.dyCount))"]
            self.segmentedView.reloadData()
            
        }).disposed(by: baseDisposeBag)
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        relaodUI()
    }
    
    override func loadData() {
        
        NetworkUtility.request(target: .userInfoDetail(["imId": imId ?? ""]), model: UserInfoModel.self) { result in
            if result.isError { return }
            self.infoModel = result.model
            
            EditProfileSessionManager.shared.baseUser = result.model
            self.relaodUI()
            
            
        }
        
        LWUserManger.shared.getDyCount(callBack: { [weak self] m in
            guard let self = self else { return }
            
            self.dyCount = m
            if self.dyCount == 0 { return }
            self.dataSource.titles = ["交友卡", "动态(\(self.dyCount))"]
            self.segmentedView.reloadData()
                
        }, imId: imId)
        
      
        
    }
    
    func relaodUI() {
        self.cardVC?.model = infoModel
        self.cardVC?.loadData()
        self.userHeaderView.model = infoModel
        self.bottomView.infoModel = self.infoModel
        self.followButton.isHidden = infoModel?.followStatus?.contains("1") == true
        
        if let a = infoModel?.userDecorateGoodsPropList.filter({ $0.goodsType == .homeEffect }).first {
            playView.player(with: a.goodsUrlPreview ?? "")
        }
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        
        dataSource.titles = titles
        dataSource.titleSelectedColor = .textColor3 ?? .white
        dataSource.titleNormalColor = .textColor9 ?? .white
        dataSource.titleNormalFont = .medium(14)
        dataSource.titleSelectedFont = .medium(19)
        dataSource.isTitleColorGradientEnabled = true
        dataSource.isItemSpacingAverageEnabled = false

        
        segmentedView.backgroundColor = UIColor.clear
        segmentedView.delegate = self
        segmentedView.isContentScrollViewClickTransitionAnimationEnabled = false
        segmentedView.dataSource = dataSource
        segmentedView.contentEdgeInsetLeft = 20
        
        
        segmentedView.defaultSelectedIndex = 0
        
        let lineView = JXSegmentedIndicatorLineView()
        lineView.verticalOffset = 2
        lineView.indicatorHeight = 4
        lineView.indicatorWidth = 6
        lineView.indicatorColor = .themColor ?? .random
        segmentedView.indicators = [lineView]
        
        segmentedView.listContainer = pagingView.listContainerView
        pagingView.pinSectionHeaderVerticalOffset = Int(AppTool.navigationBarHeight)
        pagingView.listContainerView.listCellBackgroundColor = .clear
        pagingView.mainTableView.backgroundColor = .clear
        pagingView.backgroundColor = .clear
        view.addSubview(pagingView)
        pagingView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        view.addSubview(bottomView)
        bottomView.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(AppTool.safeAreaBottomHeight + 72)
        }
        
        view.addSubview(playView)
    }

  
    @objc
    func moreAction() {
        
        let menuViewController = PopoverMenuViewController()
        menuViewController.selectionPublisher.subscribe(onNext: { [weak self] string in
            guard let self = self else { return }
            if string.contains("关注") {
                self.followAction()
                return
            }
            
            if string.contains("举报") {
                let vc = ReportViewController()
                vc.uid = self.imId
                self.navigationController?.pushViewController(vc, animated: true)
                return
            }
            
            if string.contains("拉黑") {
                LWUserManger.shared.userBlackAction(isBlack: true, imid: self.imId?.int ?? 0) {
                    
                }
                return
            }
            
        }).disposed(by: baseDisposeBag)
        
        let a = PopoverMenuItem(title: "举报", image: nil)
        let a1 = PopoverMenuItem(title: "拉黑", image: nil)
        let a2 = PopoverMenuItem(title: isFollow ? "取消关注" : "关注", image: nil)
        
        menuViewController.items = isFollow ? [a2, a, a1] : [a, a1]
        menuViewController.modalPresentationStyle = .popover
        if let popoverController = menuViewController.popoverPresentationController {
            popoverController.sourceView = self.moreButton
            popoverController.permittedArrowDirections = .any
            popoverController.delegate = self
        }
        present(menuViewController, animated: true, completion: nil)
    }

    @objc
    func followAction() {
        
        LWUserManger.shared.userFollowAction(isFollow: infoModel?.followStatus == "1", imid: imId?.int ?? 0) {
            self.infoModel?.followStatus = self.infoModel?.followStatus == "1" ? "0" : "1"
            self.relaodUI()
        }
    }
    

}



extension UserPageMainVC: JXPagingViewDelegate, JXSegmentedViewDelegate {
    
    func pagingView(_ pagingView: JXPagingView, initListAtIndex index: Int) -> JXPagingViewListViewDelegate {
        if index == 0 {
            let vc = UserCardListVC()
            vc.model = infoModel
            cardVC = vc
            return vc
        }
        
        let vc = UserListDynamicVC()
        vc.isUserPage = true
        vc.uid = imId
        return vc
    }
    
    func tableHeaderViewHeight(in pagingView: JXPagingView) -> Int {
        return Int((AppTool.screenWidth * 3) / 4 + AppTool.safeAreaTopHeight + 90)
    }
    
    func tableHeaderView(in pagingView: JXPagingView) -> UIView {
        return userHeaderView
    }
    
    func heightForPinSectionHeader(in pagingView: JXPagingView) -> Int {
        return headerInSectionHeight
    }
    
    func viewForPinSectionHeader(in pagingView: JXPagingView) -> UIView {
        return segmentedView
    }
    
    func numberOfLists(in pagingView: JXPagingView) -> Int {
        return titles.count
    }
    
    func pagingView(_ pagingView: JXPagingView, mainTableViewDidScroll scrollView: UIScrollView) {
        
    }
    
    func segmentedView(_ segmentedView: JXSegmentedView, didSelectedItemAt index: Int) {
        self.navigationController?.interactivePopGestureRecognizer?.isEnabled = (index == 0)
    }
    
    func mainTableViewDidScroll(_ scrollView: UIScrollView) {
        
        var max: Double = AppTool.navigationBarHeight + 50.0
        let percentage = (Double(scrollView.contentOffset.y) / max)
        
        navigationBarAlpha = percentage
        if scrollView.contentOffset.y > max {
            navigationTitle = infoModel?.nickName
            navigationTitleColor = .black
            navigationBarTintColor = .black
            moreButton.setImage(UIImage(named: "icon_moreII")?.tint(with: .black), for: .normal)
        } else {
            navigationTitle = ""
            navigationTitleColor = .white
            navigationBarTintColor = .white
            moreButton.setImage(UIImage(named: "icon_moreII")?.tint(with: .white), for: .normal)
        }
    }
    
}
 


extension UserPageMainVC: UIPopoverPresentationControllerDelegate {
    func adaptivePresentationStyle(for controller: UIPresentationController) -> UIModalPresentationStyle {
        return .none
    }
}
