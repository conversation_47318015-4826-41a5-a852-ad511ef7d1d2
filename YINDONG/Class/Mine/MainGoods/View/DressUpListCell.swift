//
//  DressUpListCell.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit

/// 装扮列表项 Cell
class DressUpListCell: UICollectionViewCell {
    // MARK: - 子视图
    let iconContainerView = UIView()
    let overlayAvatarImageView = UIImageView()
    let itemImageView = UIImageView()
    let nameLabel = UILabel()
    
    let diamondCountLabel = InfoItemBaseView(rightImg: "zuan_coinicon", leftTitle: "", isLeft: true, space: 2, imgWH: CGSizeMake(14, 14), margin: 0)
    
    let tagLabel = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 6)
    
    let timeLab = InfoItemBaseView(rightImg: "icon_daoju_time", leftTitle: "", isLeft: true, space: 3, imgWH: CGSizeMake(10, 10), margin: 8)
    
    let wearLab = InfoItemBaseView(rightImg: nil, leftTitle: "使用中", margin: 6)
    
    // MARK: - 数据模型
    /// 本地 model 命名保持不变
    var item: DressBaseModel? {
        didSet { applyItem() }
    }
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupSubviews()
    }
    
    // MARK: - 视图搭建
    private func setupSubviews() {
        contentView.addSubview(iconContainerView)
        iconContainerView.layer.cornerRadius = 8
        iconContainerView.clipsToBounds = true
        iconContainerView.addSubview(overlayAvatarImageView)
        iconContainerView.addSubview(itemImageView)
        
        contentView.addSubview(nameLabel)
        nameLabel.font = .regular(14)
        nameLabel.textColor = UIColor(hexString: "#333333")
        
        contentView.addSubview(wearLab)
        wearLab.titleLab.font = .regular(11)
        wearLab.titleLab.textColor = .themColor
        wearLab.layerCornerRadius = 4
        wearLab.layerBorderColor = .themColor
        wearLab.layerBorderWidth = 1
        wearLab.isHidden = true
        
        // 使用 StackView 包含钻石和标签视图
        let bottomStack = UIStackView(arrangedSubviews: [diamondCountLabel, tagLabel])
        bottomStack.axis = .horizontal
        bottomStack.spacing = 0
        bottomStack.distribution = .fill
        contentView.addSubview(bottomStack)
        
        diamondCountLabel.titleLab.font = .medium(11)
        diamondCountLabel.titleLab.textColor = UIColor(hexString: "#989898")
        
        tagLabel.layerCornerRadius = 2
        tagLabel.layer.borderWidth = 1
        tagLabel.titleLab.font = .regular(8)
        tagLabel.titleLab.textColor = UIColor(hexString: "#5C76DC")
        
        timeLab.isHidden = true
        iconContainerView.addSubview(timeLab)
        timeLab.layerCornerRadius = 8
        timeLab.backgroundColor = UIColor(white: 0, alpha: 0.3)
        timeLab.titleLab.font = .regular(11)
        timeLab.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.height.equalTo(16)
            make.bottom.equalTo(-6)
        }
        
        iconContainerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(iconContainerView.snp.width)
        }
        itemImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(12)
        }
        
        let rate = 64.0/84.0
        overlayAvatarImageView.snp.makeConstraints { make in
            make.center.equalTo(itemImageView)
            make.width.equalTo(itemImageView.snp.height).multipliedBy(rate)
            make.height.equalTo(itemImageView.snp.height).multipliedBy(rate)
        }
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(iconContainerView.snp.bottom).offset(8)
            make.left.equalTo(iconContainerView)
            make.height.equalTo(20)
            make.right.equalTo(wearLab.snp.left).offset(-5)
        }
        
        wearLab.snp.makeConstraints { make in
            make.centerY.equalTo(nameLabel)
            make.right.equalToSuperview()
            make.height.equalTo(15)
            make.width.equalTo(48)
        }
        
        bottomStack.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(2)
            make.left.equalTo(iconContainerView)
            make.height.equalTo(16)
        }
        diamondCountLabel.snp.makeConstraints { make in
            make.height.equalToSuperview()
        }
        tagLabel.snp.makeConstraints { make in
            make.height.equalToSuperview()
        }
    }
    
    // MARK: - 数据应用
    private func applyItem() {
        guard let item = item else { return }
        // 图片加载
        if let url = item.goodsUrlShow?.url {
            itemImageView.kf.setImage(with: url)
        }
        // 背景色
        iconContainerView.backgroundColor = UIColor(hexString: item.showCode ?? "#FFFFFF")
        // 名称
        nameLabel.text = item.goodsName
        
        // 购买类型
        let type = item.purchaseType
        let showDiamond = (type == .diamond)
        diamondCountLabel.isHidden = !showDiamond
        tagLabel.isHidden = showDiamond
        
        switch type {
        case .diamond:
            diamondCountLabel.titleLab.text = "\(item.price3)"
        case .vip:
            tagLabel.titleLab.text = item.remarks ?? "会员专属"
            tagLabel.layer.borderColor = UIColor(hexString: "#5C76DC")?.cgColor
            tagLabel.titleLab.textColor = UIColor(hexString: "#5C76DC")
        case .activity, .roomLevel:
            tagLabel.titleLab.text = "活动获得"
            tagLabel.layer.borderColor = UIColor(hexString: "#5C76DC")?.cgColor
            tagLabel.titleLab.textColor = UIColor(hexString: "#5C76DC")
        case .cp:
            tagLabel.titleLab.text = "CP专属"
            tagLabel.layer.borderColor = UIColor(hexString: "#F6A705")?.cgColor
            tagLabel.titleLab.textColor = UIColor(hexString: "#F6A705")
        case .noble:
            tagLabel.titleLab.text = "贵族专属"
            tagLabel.layer.borderColor = UIColor(hexString: "#F6A705")?.cgColor
            tagLabel.titleLab.textColor = UIColor(hexString: "#F6A705")
        }
        
        if item.goodsType == .homeEffect || item.goodsType == .voiceRoomBackground || item.goodsType == .voiceRoomPublicBubble {
            itemImageView.snp.remakeConstraints { make in
                make.edges.equalToSuperview()
            }
        } else {
            itemImageView.snp.remakeConstraints { make in
                make.edges.equalToSuperview().inset(12)
            }
        }
        
        // 头像显示
        let showAvatar = item.goodsType == .avatarFrame
        overlayAvatarImageView.isHidden = !showAvatar
        if showAvatar {
            let sex = item.goodsUrlShowSex
            overlayAvatarImageView.image = sex == .male ? UIImage(named: "icon_daoju_nan") : UIImage(named: "icon_daoju_nv")
        }
    }
}
