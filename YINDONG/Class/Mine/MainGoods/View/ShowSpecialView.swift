//
//  ShowSpecialView.swift
//  YINDONG
//
//  Created by jj on 2025/6/25.
//

import UIKit
import Kingfisher
import SVGAPlayer
import Lottie
import QGVAPlayer
import BDAlphaPlayer

class ShowSpecialView: UIView, AnimationPlayerDelegate {
        
    lazy var basimgV: UIImageView = {
        let imgV = UIImageView()
        imgV.contentMode = .scaleAspectFill
        imgV.layerCornerRadius = 31.5
        return imgV
    }()
    
    lazy var playView: LoopAnimationView = {
        let vv = LoopAnimationView(delegate: nil)
        return vv
    }()
    
    lazy var ringPlayView: LoopAnimationView = {
        let vv = LoopAnimationView(delegate: self)
        vv.isLoop = false
        return vv
    }()
    
    lazy var bgView: RoomBgView = {
        let bgView = RoomBgView()
        return bgView
    }()
    
    lazy var bgView1: BaseChatBgView = {
        let bgV = BaseChatBgView()
        return bgV
    }()
    
    lazy var msgBgV1: BaseChatBgView = {
        let view = BaseChatBgView(frame: CGRect())
        return view
    }()
    lazy var msgBgV2: BaseChatBgView = {
        let view = BaseChatBgView(frame: CGRect())
        return view
    }()
    lazy var msgLabel1: UILabel = {
        let view = UILabel()
        view.textColor = .white
        view.font = .medium(14)
        view.textAlignment = .center
        return view
    }()
    
    lazy var msgLabel2: UILabel = {
        let view = UILabel()
        view.textColor = .white
        view.font = .medium(14)
        view.textAlignment = .center
        return view
    }()
    
    lazy var chatMsgBgImgV1: UIImageView = {
        let view = UIImageView(frame: CGRect())
        return view
    }()
    lazy var chatMsgBgImgV2: UIImageView = {
        let view = UIImageView(frame: CGRect())
        return view
    }()
    
    var model: DressBaseModel? {
        didSet {
            setupType()
        }
    }

    convenience init() {
        self.init(frame: .zero)
        
        addSubview(basimgV)
        addSubview(playView)
        
        basimgV.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(63)
        }
    }
    
    func setupType() {
        guard let model = model else { return }
        
        switch model.goodsType {
        case .avatarFrame:
            basimgV.setImage(from: kuser.photoUrl)
            playView.snp.makeConstraints { make in
                make.center.equalTo(basimgV)
                make.size.equalTo(basimgV).multipliedBy(AvatarFrameView.defaultDecorationScale)
            }
            
            layoutIfNeeded()
            playView.setUpViews()
            playView.player(with: model.goodsUrlPreview ?? "")
        case .mount:
            basimgV.setImage(from: model.goodsUrlShow)
            basimgV.snp.remakeConstraints { make in
                make.width.height.equalTo(100)
                make.center.equalToSuperview()
            }
        case .homeEffect:
            basimgV.image = UIImage(named: "icon_daoju_zytx_bg")
            basimgV.layerCornerRadius = 0
            basimgV.contentMode = .scaleToFill
            basimgV.snp.remakeConstraints { make in
                make.top.equalTo(20)
                make.centerX.equalToSuperview()
                make.width.equalTo(180)
                make.height.equalTo(322)
            }
            playView.snp.makeConstraints { make in
                make.edges.equalTo(basimgV)
            }
            layoutIfNeeded()
            playView.setUpViews()
            playView.player(with: model.goodsUrlPreview ?? "")
        case .chatBubble:
            setBubble(model: model)
        case .voiceRoomPublicBubble:
            setBubble(model: model, isLeft: true)
        case .voiceRoomBackground:
            playView.isHidden = true
            insertSubview(bgView, at: 0)
            basimgV.image = UIImage(named: "icon_daoju_yuyinfang_bg")
            basimgV.layerCornerRadius = 0
            basimgV.contentMode = .scaleToFill
            basimgV.snp.remakeConstraints { make in
                make.top.equalTo(20)
                make.centerX.equalToSuperview()
                make.width.equalTo(180)
                make.height.equalTo(322)
            }
            bgView.snp.makeConstraints { make in
                make.edges.equalTo(basimgV)
            }
            bgView.setBackground(urlString: model.goodsUrlPreview)
        }
        
    }
    
    //戒指单独处理
    var ringModel: RingBaseModel? {
        didSet {
            guard let ringModel = ringModel else { return }
            basimgV.image = nil
            let widthH = AppTool.screenWidth - 100
            playView.snp.makeConstraints { make in
                make.centerX.equalToSuperview()
                make.width.height.equalTo(widthH)
                make.centerY.equalToSuperview()
            }
            
            addSubview(ringPlayView)
            ringPlayView.snp.makeConstraints { make in
                make.centerX.equalToSuperview()
                make.centerY.equalToSuperview()
                make.width.equalToSuperview().multipliedBy(0.9)
                make.height.equalTo(180)
            }
            
            switch ringModel.channelType {
            case 0:
                playView.transform = CGAffineTransform(scaleX: 0.37, y: 0.37)
            case 2:
                playView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
            default:
                break
            }
            
            layoutIfNeeded()
            playView.setUpViews()
            ringPlayView.setUpViews()
            
            if let mm = ringModel.approachUrl, isValid(mm) {
                ringPlayView.player(with: mm)
                playView.player(with: ringModel.eastUrl ?? "")
                playView.isHidden = true
            } else {
                playView.player(with: ringModel.eastUrl ?? "")
            }
            

            
        }
    }
    
    
    func setBubble(model: DressBaseModel, isLeft: Bool = false) {
        
        basimgV.isHidden = true
        playView.isHidden = true
        addSubview(chatMsgBgImgV1)
        addSubview(chatMsgBgImgV2)
        chatMsgBgImgV1.addSubview(msgLabel1)
        chatMsgBgImgV2.addSubview(msgLabel2)
        addSubview(self.msgBgV1)
        addSubview(self.msgBgV2)
        
        chatMsgBgImgV1.snp.makeConstraints { make in
            make.top.equalTo(19)
            if isLeft {
                make.left.equalTo(50)
            } else {
                make.right.equalTo(-50)
            }
            
        }
        chatMsgBgImgV2.snp.makeConstraints { make in
            make.top.equalTo(chatMsgBgImgV1.snp.bottom).offset(8)
            if isLeft {
                make.left.equalTo(chatMsgBgImgV1)
            } else {
                make.right.equalTo(chatMsgBgImgV1)
            }
        }
        msgBgV1.snp.makeConstraints { make in
            make.edges.equalTo(chatMsgBgImgV1)
        }
        msgBgV2.snp.makeConstraints { make in
            make.edges.equalTo(chatMsgBgImgV2)
        }
        
        msgLabel1.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(15)
            make.top.bottom.equalToSuperview().inset(isLeft ? 8 :15)
            make.width.greaterThanOrEqualTo(45)
        }
        msgLabel2.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(15)
            make.top.bottom.equalToSuperview().inset(isLeft ? 8 : 15)
            make.width.greaterThanOrEqualTo(45)
        }
        msgLabel1.font = .medium(15)
        msgLabel2.font = .medium(15)
        msgLabel1.text = model.goodsName ?? ""
        msgLabel2.text = "你好，很高兴认识你"
        
    
        msgLabel1.textColor = UIColor(hexString: model.fontCode ?? "")
        msgLabel2.textColor = UIColor(hexString: model.fontCode ?? "")
        
        if isLeft {
            msgLabel1.textColor = .white
            msgLabel2.textColor = .white
            msgLabel1.attributed.text = "\(AppTool.appName, .foreground(.white.withAlphaComponent(0.6))): \(model.goodsName ?? "")"
            msgLabel2.attributed.text = "\(AppTool.appName, .foreground(.white.withAlphaComponent(0.6))): 大家晚上好～"
        }
        
        if let url = model.goodsUrlPreview, isValid(url) {
            let downloader = ImageDownloader.default
            let options = KingfisherOptionsInfo([.transition(.fade(0.2)), .scaleFactor(UIScreen.main.scale)])
            downloader.downloadTimeout = 15
            downloader.downloadImage(with: URL(string: url)!, options: options) {[weak self] result in
                guard let `self` = self else { return }
                switch result {
                case .success(let value):
                    let img = value.image
                    let resizableImage = img.createResizableImageCentered()
                    self.chatMsgBgImgV1.image = resizableImage
                    self.chatMsgBgImgV2.image = resizableImage
                case .failure(let error):
                    break
                }
            }
        }
        if let buddle = self.model {
            msgBgV1.play(withMinXMinY: buddle.goodsUrlTopLeft, minXMaxY: buddle.goodsUrlBottomLeft, maxXminY: buddle.goodsUrlTopRight, maxXMaxY: buddle.goodsUrlBottomRight)
            msgBgV2.play(withMinXMinY: buddle.goodsUrlTopLeft, minXMaxY: buddle.goodsUrlBottomLeft, maxXminY: buddle.goodsUrlTopRight, maxXMaxY: buddle.goodsUrlBottomRight)
            
            if let gradientLeft = buddle.gradientLeft,let gradientRight = buddle.gradientRight,gradientLeft.isEmpty == false,gradientRight.isEmpty == false{
                msgBgV1.gradientLayer.isHidden = false
                msgBgV1.gradientLayer.colors = [UIColor(hexString: gradientLeft)!.cgColor,UIColor(hexString: gradientRight)!.cgColor]
                
                msgBgV2.gradientLayer.isHidden = false
                msgBgV2.gradientLayer.colors = [UIColor(hexString: gradientLeft)!.cgColor,UIColor(hexString: gradientRight)!.cgColor]
            }
            msgBgV1.bgImgV.backgroundColor = .clear
            msgBgV2.bgImgV.backgroundColor = .clear
        }
    }
    
    
    func willPlay(format: AnimationFormat) {
        
    }
    
    func didFinishPlaying(format: AnimationFormat) {
        guard let ringModel = ringModel else { return }
        if let mm = ringModel.approachUrl, isValid(mm) {
            playView.isHidden = false
        }
    }
    
    func didFailPlaying(format: AnimationFormat) {
        
    }
}


class LoopAnimationView: UIView {
    
    var isLoop: Bool = true
    
    private var currentPlayer: AnimationPlayer?
    private var format: AnimationFormat = .svga
    private weak var activePlayerView: UIView? // Keep track of the currently visible player view
    
    // UI elements for different animations
    private var pngAnimationView: UIImageView!
    private var svgaView: SVGAnimationPlayer!
    private var lottieAnimationView: LottieAnimationView!
    private var vapPlayer: UIView!
    private var metalView: BDAlphaPlayerMetalView!
    
    weak var delegate: AnimationPlayerDelegate?
    
    var tempInfoModel: AnimationElement?

    var tempUrl: String?
    
    convenience init(delegate: AnimationPlayerDelegate? = nil) {
        self.init(frame: .zero)
        self.delegate = delegate
        
    }
    
    func setUpViews() {
        pngAnimationView = UIImageView()
        
        svgaView = SVGAnimationPlayer()
        svgaView.isUserInteractionEnabled = false
        svgaView.delegate = self
        svgaView.contentMode = .scaleToFill
        svgaView.setImgClear()
        
        lottieAnimationView = LottieAnimationView()
        lottieAnimationView.isUserInteractionEnabled = false
        
        metalView = BDAlphaPlayerMetalView(delegate: self, offsetType: .left)
        metalView.isUserInteractionEnabled = false
        
        vapPlayer = UIView()
        vapPlayer.enableOldVersion(true)
        vapPlayer.hwd_enterBackgroundOP = .stop
        vapPlayer.contentMode = .scaleToFill
        vapPlayer.isUserInteractionEnabled = false
        vapPlayer.setMute(true)
        
        vapPlayer.frame = self.bounds
        lottieAnimationView.frame = self.bounds
        svgaView.frame = self.bounds
        metalView.frame = self.bounds
        pngAnimationView.frame = self.bounds
        
        pngAnimationView.isHidden = true
        svgaView.isHidden = true
        lottieAnimationView.isHidden = true
        vapPlayer.isHidden = true
        metalView.isHidden = true
        
        addSubview(pngAnimationView)
        addSubview(svgaView)
        addSubview(lottieAnimationView)
        addSubview(vapPlayer)
        addSubview(metalView)
    }
    
    func player(with url: String) {
        self.tempUrl = url
        getFormat(url: url)
        Async.main(after: 0) {
            self.setupPlayer(for: self.format)
            self.currentPlayer?.play(url: url)
        }
    }
    
    private func setupPlayer(for format: AnimationFormat) {
        var newActivePlayerView: UIView?
        
        switch format {
        case .png:
            newActivePlayerView = pngAnimationView
            if !(currentPlayer is PNGAnimationPlayer) { // Avoid re-creating if same type
                currentPlayer = PNGAnimationPlayer(imageView: pngAnimationView)
            }
        case .svga:
            newActivePlayerView = svgaView
            if !(currentPlayer is SVGAAnimationPlayer) {
                currentPlayer = SVGAAnimationPlayer(svgaView: svgaView)
                svgaView.loops = isLoop ? 1000 : 1
            }
        case .lottie:
            newActivePlayerView = lottieAnimationView
            if !(currentPlayer is LottieAnimationPlayer) {
                currentPlayer = LottieAnimationPlayer(lottieView: lottieAnimationView)
                lottieAnimationView.loopMode = isLoop ? .loop : .playOnce
            }
        case .vap:
            newActivePlayerView = vapPlayer
            if !(currentPlayer is VapAnimationPlayer) {
                currentPlayer = VapAnimationPlayer(vapPlayer: vapPlayer)
                currentPlayer?.loopCount = isLoop ? 1000 : 1
            }
        case .mp4:
            newActivePlayerView = metalView
            if !(currentPlayer is MP4AnimationPlayer) {
                currentPlayer = MP4AnimationPlayer(metalView: metalView)
                currentPlayer?.loopCount = isLoop ? 1000 : 1
                currentPlayer?.frame = self.frame
            }
        }
        currentPlayer?.delegate = self
        
        // Transition: Make new view visible and bring to front, then hide old one.
        if let newView = newActivePlayerView {
            newView.isHidden = false
            bringSubviewToFront(newView) // Ensure it's on top
            
            if let oldActiveView = activePlayerView, oldActiveView != newView {
                oldActiveView.isHidden = true
            }
            activePlayerView = newView
        } else {
            // If no valid format, hide the currently active view
            activePlayerView?.isHidden = true
            activePlayerView = nil
            currentPlayer = nil // No valid player
        }
    }
    
    private func getFormat(url: String) {
        if url.pathExtension == "png" {
            format = .png
        } else if url.contains(".zip") || url.contains(".json") {
            format = .lottie
        } else if url.contains(".mp4") && url.contains("vap=1") {
            format = .vap
        } else if url.contains(".mp4") {
            format = .mp4
        } else {
            format = .svga
        }
    }
    
    func stopCurrentAnimation() {
        if let player = currentPlayer as? SVGAAnimationPlayer {
            player.svgaView.stopAnimation()
        } else if let player = currentPlayer as? LottieAnimationPlayer {
            player.lottieView.stop()
        } else if let player = currentPlayer as? VapAnimationPlayer {
            player.vapPlayer.stopHWDMP4()
        } else if let player = currentPlayer as? MP4AnimationPlayer {
            player.metalView.stop()
        } else if let player = currentPlayer as? PNGAnimationPlayer {
            player.imageView.image = nil
        }
        
        activePlayerView?.isHidden = true
        activePlayerView = nil
        currentPlayer = nil
    }
}

// MARK: - AnimationPlayerDelegate Methods
extension LoopAnimationView: AnimationPlayerDelegate {
    func willPlay(format: AnimationFormat) {
        delegate?.willPlay(format: format)
    }
    
    func didFinishPlaying(format: AnimationFormat) {
        delegate?.didFinishPlaying(format: format)
    }
    
    func didFailPlaying(format: AnimationFormat) {
        delegate?.didFailPlaying(format: format)
    }
}

extension LoopAnimationView :BDAlphaPlayerMetalViewDelegate {
    
    func metalView(_ metalView: BDAlphaPlayerMetalView, didFinishPlayingWithError error: Error?) {
        if error != nil  {
            // 如果发生错误，调用失败回调
            delegate?.didFailPlaying(format: .mp4)
            print("播放失败，错误信息：\(error?.localizedDescription)")
        } else {
            // 如果没有错误，播放成功，调用完成回调
            delegate?.didFinishPlaying(format: .mp4)
            if isLoop {
                self.player(with: tempUrl ?? "")
            }
        }
        
    }
    
}

extension LoopAnimationView: SVGAPlayerDelegate {
    func svgaPlayerDidFinishedAnimation(_ player: SVGAPlayer!) {
        delegate?.didFinishPlaying(format: .svga)
    }
}
