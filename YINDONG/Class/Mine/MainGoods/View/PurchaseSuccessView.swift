//
//  PurchaseSuccessView.swift
//  YINDONG
//
//  Created by jj on 2025/6/25.
//

import UIKit

class PurchaseSuccessView: CustomPopupView {
    
    // MARK: - UI
    private let titleLab   = UILabel()
    private let imgBgV     = UIView()
    private let pngImgV    = UIImageView()
    private let svgaView   = AvatarFrameView()
    private let dayLab     = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 6)
    private let nameLab    = UILabel()
    private let subTitleLab = UILabel()
    
    private let cancelBtn  = UIButton()
    private let nextBtn    = UIButton()
    
    // MARK: - UI 构建
    override func configureUI() {
        super.configureUI()
        
        frame = CGRect(x: 0, y: 0, width: 299, height: 276)
        backgroundColor = .white
        layer.cornerRadius = 16
        
        // —— 标题
        titleLab.font = .medium(16)
        titleLab.textColor = UIColor(white: 0.2, alpha: 0.85)
        titleLab.text = "购买成功"
        addSubview(titleLab)
        titleLab.snp.makeConstraints {
            $0.top.equalToSuperview().offset(24)
            $0.centerX.equalToSuperview()
            $0.height.equalTo(22)
        }
        
        // —— 图片背景 71×71
        imgBgV.layer.cornerRadius = 8
        imgBgV.clipsToBounds = true
        addSubview(imgBgV)
        imgBgV.snp.makeConstraints {
            $0.top.equalTo(titleLab.snp.bottom).offset(16)
            $0.centerX.equalToSuperview()
            $0.size.equalTo(CGSize(width: 71, height: 71))
        }
        
        // pngImg 覆盖整个 imgBgV
        pngImgV.clipsToBounds = true
        pngImgV.contentMode = .scaleAspectFit
        imgBgV.addSubview(pngImgV)
        pngImgV.snp.makeConstraints { $0.edges.equalToSuperview() }
        
        // SVGA 47×47 居中
        imgBgV.addSubview(svgaView)
        svgaView.snp.makeConstraints { $0.center.equalToSuperview(); $0.size.equalTo(CGSize(width: 47, height: 47)) }
        
        // —— 天数角标
        dayLab.isHidden = true
        dayLab.titleLab.font = .regular(11)
        dayLab.titleLab.textColor = .white
        dayLab.backgroundColor = UIColor(white: 0, alpha: 0.3)
        dayLab.layer.cornerRadius = 4
        dayLab.clipsToBounds = true
        
        imgBgV.addSubview(dayLab)
        dayLab.snp.makeConstraints {
            $0.trailing.bottom.equalToSuperview()
            $0.height.equalTo(16)
        }
        
        // —— 名称
        nameLab.font = .systemFont(ofSize: 14)
        nameLab.textColor = UIColor(white: 0.2, alpha: 1)
        addSubview(nameLab)
        nameLab.snp.makeConstraints {
            $0.top.equalTo(imgBgV.snp.bottom).offset(8)
            $0.centerX.equalTo(imgBgV)
            $0.height.equalTo(20)
        }
        
        // —— 副标题
        subTitleLab.font = .systemFont(ofSize: 12)
        subTitleLab.textColor = UIColor(white: 0.6, alpha: 1)
        addSubview(subTitleLab)
        subTitleLab.snp.makeConstraints {
            $0.top.equalTo(nameLab.snp.bottom).offset(5)
            $0.centerX.equalTo(imgBgV)
            $0.height.equalTo(17)
        }
        
        // —— 两按钮 42 pt 高，左右边距 20，间距 21
        let btnStack = UIStackView(arrangedSubviews: [cancelBtn, nextBtn])
        btnStack.axis = .horizontal
        btnStack.distribution = .fillEqually
        btnStack.spacing = 21
        addSubview(btnStack)
        
        btnStack.snp.makeConstraints {
            $0.leading.trailing.equalToSuperview().inset(20)
            $0.bottom.equalToSuperview().inset(24)
            $0.height.equalTo(42)
        }
        
        // 左：取消
        cancelBtn.layer.cornerRadius = 21
        cancelBtn.backgroundColor = UIColor(red: 0.965, green: 0.969, blue: 0.976, alpha: 1)
        cancelBtn.setTitle("确定", for: .normal)
        cancelBtn.setTitleColor(.textColor3, for: .normal)
        cancelBtn.titleLabel?.font = .systemFont(ofSize: 14)
        cancelBtn.addTarget(self, action: #selector(cancelClick), for: .touchUpInside)
        
        // 右：去看看
        nextBtn.layer.cornerRadius = 21
        nextBtn.backgroundColor = UIColor(red: 1, green: 0.357, blue: 0.357, alpha: 1)
        nextBtn.setTitle("去看看", for: .normal)
        nextBtn.setTitleColor(.white, for: .normal)
        nextBtn.titleLabel?.font = .systemFont(ofSize: 14)
        nextBtn.addTarget(self, action: #selector(nextClick), for: .touchUpInside)
    }
    
    // MARK: - 数据填充
    /// purchaseType=0 为钻石购买
    /// goodsType: 0头像框 1坐骑 2主页特效 3聊天气泡 4语音房公屏气泡 5语音房背景
    func setDressUpData(item: DressBaseModel, day: String) {
        
        dayLab.isHidden = false
        dayLab.titleLab.text = day
        imgBgV.backgroundColor = UIColor(hexString: item.previewCode ?? "")
        if item.goodsType == .avatarFrame { // 头像框
            pngImgV.isHidden = true
            svgaView.isHidden = false
            svgaView.avatarImageView.layerCornerRadius = 31.5
            svgaView.avatarImageView.setImage(from: kuser.photoUrl)
            svgaView.loadDecoration(from: item.goodsUrlShow ?? "")
            svgaView.updateDecorationScale(multiplier: AvatarFrameView.defaultDecorationScale)
        } else {
            pngImgV.isHidden = false
            svgaView.isHidden = true
            pngImgV.setImage(from: item.goodsUrlShow)
        }
        nameLab.text = item.goodsName
        subTitleLab.text = "已放入背包中"
    }
    
    /// 戒指
    func setRingData(name: String,
                     imgUrl: String,
                     imgBgHex: String,
                     day: Int = 0) {
        dayLab.isHidden = true
        imgBgV.backgroundColor = UIColor(hexString: imgBgHex)
        svgaView.loadDecoration(from: imgUrl)
        nameLab.text = name
        subTitleLab.text = "已放入背包中"
    }
    
    // MARK: - Actions
    @objc private func cancelClick() {
        dismiss()
    }
    
    @objc private func nextClick() {
        dismiss()
        let bk = MyPackMainVC()
        AppTool.getCurrentViewController().navigationController?.pushViewController(bk, animated: true)
    }
    
}
