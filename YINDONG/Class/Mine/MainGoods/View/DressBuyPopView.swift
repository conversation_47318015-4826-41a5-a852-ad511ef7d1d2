import UIKit
import SnapKit
import LSTPopView

// MARK: - 抽象基类
class BaseDressPopView: CustomPopupView {

    // MARK: 公共 UI —— 顶部
    let containerView   = UIView()
    let headBgV         = ShowSpecialView()      // 大图 / 预览背景
    let timeLab         = InfoItemBaseView(rightImg: "icon_daoju_time", leftTitle: "", isLeft: true, space: 3, imgWH: CGSizeMake(10, 10), margin: 8)

    // MARK: 公共 UI —— 底部（标题 / 价格 / 时长选择）
    let titleStack      = UIStackView()
    let nameLab         = UILabel()
    let priceImgV       = UIImageView(image: UIImage(named: "zuan_coinicon"))
    let priceLab        = UILabel()

    let chooseTitleLab  = UILabel()
    let daysStack       = UIStackView()

    // MARK: Action 区（子类自定义）
    let actionArea      = UIView()

    let priceWrap = UIView()
    
    // MARK: 约束
    private var headHeightConstraint: Constraint!

    // MARK: 数据
    enum BuyDayType: Int {
        case day3 = 0, day7, day15
        
        var text: String {
            switch self {
            case .day3:
                return "3天"
            case .day7:
                return "7天"
            case .day15:
                return "15天"
            }
        }
    }
    var dayType: BuyDayType = .day3 { didSet { refreshPrice() } }

    var item: DressBaseModel? { didSet { bindModel() } }
    let buyBtn = UIButton(title: "购买", font: .bold(14))

    // MARK: 生命周期
    override func configureUI() {
        super.configureUI()
        frame = CGRect(x: 0, y: 0, width: 299, height: 300)
        
        buildHeader()
        buildBody()
        buildActionArea()            // 子类如需改底部，只覆写此方法
    }

    // MARK: =========== 子类接口 ===========
    /// 覆写此方法，自行往 `actionArea` 塞按钮 / 文本 / ……
     func buildActionArea() {
        // 默认：单个购买按钮
        buyBtn.backgroundColor = .themColor
        buyBtn.layer.cornerRadius = 18
        buyBtn.addTarget(self, action: #selector(buyAction), for: .touchUpInside)

        actionArea.addSubview(buyBtn)
        buyBtn.snp.makeConstraints {
            $0.center.equalToSuperview()
            $0.width.equalToSuperview().multipliedBy(0.66)
            $0.height.equalTo(36)
        }
        
    }

    /// 购买点击（子类覆盖真实逻辑）
    @objc open func buyAction() {}

    // MARK: =========== 私有实现 ===========
    private func buildHeader() {
        
        containerView.layerCornerRadius = 16
        addSubview(containerView)
        containerView.snp.makeConstraints {
            $0.top.centerX.equalToSuperview()
            $0.width.equalTo(299)
        }

        containerView.addSubview(headBgV)
        headBgV.clipsToBounds = true
        headBgV.snp.makeConstraints {
            headHeightConstraint = $0.height.equalTo(145).constraint
            $0.top.leading.trailing.equalToSuperview()
        }

        // 预览提示
        headBgV.addSubview(timeLab)
        timeLab.layer.cornerRadius = 8
        timeLab.backgroundColor = UIColor(white: 0, alpha: 0.3)
        timeLab.isHidden = true
        timeLab.titleLab.font = .regular(11)
        timeLab.titleLab.textColor = .white
        timeLab.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(-5)
            make.height.equalTo(16)
        }
        
    }

    func buildBody() {
        // ======= 标题 & 价格 =======
        titleStack.axis = .horizontal
        titleStack.distribution = .fill
        titleStack.alignment = .fill
        titleStack.addArrangedSubview(nameLab)

        
        priceWrap.addSubview(priceImgV)
        priceWrap.addSubview(priceLab)
        priceImgV.snp.makeConstraints {
            $0.centerY.equalToSuperview()
            $0.right.equalTo(priceLab.snp.left).offset(-2)
            $0.size.equalTo(CGSize(width: 14, height: 14))
        }
        priceLab.snp.makeConstraints { $0.edges.equalToSuperview() }
        titleStack.addArrangedSubview(priceWrap)

        nameLab.font = .medium(16)
        nameLab.textColor = .textColor3
        priceLab.font = .medium(16)
        priceLab.textColor = .themColor

        containerView.addSubview(titleStack)
        titleStack.snp.makeConstraints {
            $0.top.equalTo(headBgV.snp.bottom).offset(16)
            $0.leading.trailing.equalToSuperview().inset(16)
            $0.height.equalTo(22)
        }

        // ======= “选择购买时长” =======
        chooseTitleLab.text = "选择购买时长"
        chooseTitleLab.font = .systemFont(ofSize: 12)
        chooseTitleLab.textColor = UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1)
        containerView.addSubview(chooseTitleLab)
        chooseTitleLab.snp.makeConstraints {
            $0.top.equalTo(titleStack.snp.bottom).offset(16)
            $0.leading.equalToSuperview().offset(16)
        }

        // ======= 天数按钮 =======
        daysStack.axis = .horizontal
        daysStack.distribution = .fillEqually
        daysStack.spacing = 15
        ["3天", "7天", "15天"].enumerated().forEach { idx, t in
            let b = UIButton(type: .custom)
            b.setTitle(t, for: .normal)
            b.tag = idx
            b.titleLabel?.font = .systemFont(ofSize: 14)
            b.layer.cornerRadius = 8
            b.backgroundColor = UIColor(red:0.965, green:0.969, blue:0.976, alpha:1)
            b.setBackgroundImage(UIImage(named: "icon_daoju_buy_day_select"), for: .selected)
            b.setTitleColor(.themColor, for: .selected)
            b.setTitleColor(.textColor3, for: .normal)
            b.addTarget(self, action: #selector(dayTap(_:)), for: .touchUpInside)
            daysStack.addArrangedSubview(b)
        }
        containerView.addSubview(daysStack)
        daysStack.snp.makeConstraints {
            $0.top.equalTo(chooseTitleLab.snp.bottom).offset(8)
            $0.leading.trailing.equalToSuperview().inset(16)
            $0.height.equalTo(32)
        }

        // ======= Action Area =======
        containerView.addSubview(actionArea)
        actionArea.snp.makeConstraints {
            $0.top.equalTo(daysStack.snp.bottom).offset(24)
            $0.leading.trailing.bottom.equalToSuperview()
            $0.height.greaterThanOrEqualTo(36)
        }
    }

    // MARK: 数据绑定
    func bindModel() {
        guard let m = item else { return }
        nameLab.text = m.goodsName
        headBgV.backgroundColor = UIColor(hexString: m.previewCode ?? "#FFFFFF")
        headHeightConstraint.update(offset: popupHeaderHeight(for: m.goodsType.rawValue))
        headBgV.model = m

        // 预览提示
        if m.goodsType == .mount {
            timeLab.isHidden = false
            timeLab.titleLab.text = "点击预览"
            timeLab.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(previewTap)))
        } else {
            timeLab.isHidden = true
        }
        refreshPrice()
        layoutIfNeeded()
        self.height = CGRectGetMaxY(self.actionArea.frame) + 20
    }

    private func refreshPrice() {
        switch dayType {
        case .day3:  priceLab.text = "\(item?.price3 ?? 0)"
        case .day7:  priceLab.text = "\(item?.price7 ?? 0)"
        case .day15: priceLab.text = "\(item?.price15 ?? 0)"
        }

        for case let b as UIButton in daysStack.arrangedSubviews {
            b.isSelected = (b.tag == dayType.rawValue)
        }
    }

    // MARK: 交互
    @objc private func dayTap(_ sender: UIButton) {
        guard let t = BuyDayType(rawValue: sender.tag) else { return }
        dayType = t
    }
    @objc private func previewTap() {
        // 调用 SVGA 预览等
    }
}

// MARK: - 具体弹窗：购买
class DressBuyPopView: BaseDressPopView {
    
    override func buyAction() {
        guard let m = item else { return }
        
        let tipZs = "\(priceLab.text ?? "")钻石"
        let msgVc = MessagePopupView(title: "提示", content: "确定花费 \(tipZs) 购买\(m.goodsName ?? "")(\(dayType.text))吗")
        msgVc.contentLabel.attributed.text = "确定花费 \(tipZs, .foreground(.themColor ?? .white)) 购买\(m.goodsName ?? "")(\(dayType.text))吗？"
        msgVc.onAction = { [weak self] isC, vv in
            if isC { return }
            self?.popupController?.dismissStyle = .NO
            self?.popupController?.dismiss()
            self?.buyTapAction()
        }
        msgVc.isNotAnim = true
        msgVc.show()
    }
    
    func buyTapAction() {
        guard let item = item else { return }
        
        var dict: [String: Any] = [:]
        dict["giftid"] = item.id
        dict["consumptionAmount"] = priceLab.text ?? ""
        NetworkUtility.request(target: .userBuyEffets(dict)) { result in
            if result.isError { return }
            LWUserManger.shared.getUserDis()
            Async.main(after: 0.1) {
                let vc = PurchaseSuccessView()
                vc.setDressUpData(item: item, day: self.dayType.text)
                vc.show()
            }
        }
    }

}

// MARK: - 工具：根据商品类型给头图高度
fileprivate func popupHeaderHeight(for type: Int) -> CGFloat {
    return (type == 2 || type == 5) ? 345 : 145   // 321+24 == 345
}
