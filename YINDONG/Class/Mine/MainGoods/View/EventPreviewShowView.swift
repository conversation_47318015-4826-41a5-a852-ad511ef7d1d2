//
//  EventPreviewShowView.swift
//  YINDONG
//
//  Created by jj on 2025/6/25.
//

import UIKit

class EventPreviewShowView: BaseDressPopView {
    
    lazy var tipLab: UILabel = {
        let lab = UILabel(withText: "", textColor: .textColor6, fontType: .regular(14))
        return lab
    }()
    
    override func buildBody() {
        containerView.addSubview(nameLab)
        containerView.addSubview(tipLab)
        
        nameLab.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(headBgV.snp.bottom).offset(16)
        }
        
        tipLab.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(nameLab.snp.bottom).offset(8)
            make.bottom.equalTo(-10)
        }
    }
    
    override func bindModel() {
        super.bindModel()
        tipLab.text = item?.remarks
        self.height = CGRectGetMaxY(self.tipLab.frame) + 40
    }
}
