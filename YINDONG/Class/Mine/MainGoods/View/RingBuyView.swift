//
//  RingBuyView.swift
//  YINDONG
//
//  Created by jj on 2025/6/25.
//

import UIKit
import LSTPopView

class RingBuyView: BaseDressPopView {
    
    var isMake: Bool = false
    
    lazy var tipLab: UILabel = {
        let lab = UILabel(withText: "", textColor: .textColor6, fontType: .regular(12))
        lab.numberOfLines = 0
        return lab
    }()
    
    lazy var bottomLab: UILabel = {
        let lab = UILabel(withText: "", textColor: .textColor6, fontType: .regular(12))
        return lab
    }()
    
    var model: RingBaseModel? {
        didSet {
            guard let mode = model else { return }
            headBgV.backgroundColor = UIColor(hexString: mode.previewCode ?? "#FFFFFF")
            priceLab.text = "\(mode.actualPrice ?? 0)"
            nameLab.text = mode.name
            let cc = model?.goodsRemarks?.replacingOccurrences(of: "\\n", with: "\n") ?? ""
            let tip = "戒指物语\n\(cc)"
            tipLab.text = tip
            headBgV.ringModel = mode
            bottomLab.text = mode.goodsTypeRemarks
            buyBtn.isHidden = mode.isCashPurchase == 1
            
            priceWrap.isHidden = false
            if mode.isCashPurchase == 1 {
                priceWrap.isHidden = true
                titleStack.snp.remakeConstraints {
                    $0.top.equalTo(headBgV.snp.bottom).offset(16)
                    $0.centerX.equalToSuperview()
                    $0.height.equalTo(22)
                }
            }
            
            layoutIfNeeded()
            self.height = CGRectGetMaxY(self.actionArea.frame) + 20
            
        }
    }

    override func buildBody() {
        
        titleStack.axis = .horizontal
        titleStack.distribution = .fill
        titleStack.alignment = .fill
        titleStack.addArrangedSubview(nameLab)

        priceWrap.addSubview(priceImgV)
        priceWrap.addSubview(priceLab)
        priceImgV.snp.makeConstraints {
            $0.centerY.equalToSuperview()
            $0.right.equalTo(priceLab.snp.left).offset(-2)
            $0.size.equalTo(CGSize(width: 14, height: 14))
        }
        priceLab.snp.makeConstraints { $0.edges.equalToSuperview() }
        titleStack.addArrangedSubview(priceWrap)

        nameLab.font = .medium(16)
        nameLab.textColor = .textColor3
        priceLab.font = .medium(16)
        priceLab.textColor = .themColor

        containerView.addSubview(titleStack)
        titleStack.snp.makeConstraints {
            $0.top.equalTo(headBgV.snp.bottom).offset(16)
            $0.leading.trailing.equalToSuperview().inset(16)
            $0.height.equalTo(22)
        }
        
        containerView.addSubview(tipLab)
        tipLab.snp.makeConstraints { make in
            make.top.equalTo(titleStack.snp.bottom).offset(10)
            make.left.right.equalToSuperview().inset(15)
        }
        
        containerView.addSubview(actionArea)
        actionArea.snp.makeConstraints {
            $0.top.equalTo(tipLab.snp.bottom).offset(24)
            $0.leading.trailing.bottom.equalToSuperview()
            $0.height.greaterThanOrEqualTo(36)
        }
        
        containerView.addSubview(bottomLab)
        bottomLab.text = ""
        bottomLab.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalTo(actionArea)
        }
        
    }

    override func buyAction() {
        guard let model = model else { return }
        
        if isMake {
            ProgressHUDManager.showTextMessage("我知道你很急，但你先别急")
            return
        }
        
        let tipZs = "\(model.actualPrice ?? 0)钻石"
        let msgVc = MessagePopupView(title: "提示", content: "确定花费 \(tipZs) 购买\(model.name ?? "")吗")
        msgVc.contentLabel.attributed.text = "确定花费 \(tipZs, .foreground(.themColor ?? .white)) 购买\(model.name ?? "")吗？"
        msgVc.onAction = { [weak self] isC, vv in
            if isC { return }
            self?.popupController?.dismiss()
            self?.buyTapAction()
        }
        msgVc.isNotAnim = true
        msgVc.show()
        
    }
    
    
    func buyTapAction() {
        guard let model = model else { return }
        var dict: [String: Any] = [:]
        dict["giftid"] = model.id ?? 0
        dict["consumptionAmount"] = model.actualPrice ?? 0
        
        
        NetworkUtility.request(target: .userBuyRing(dict)) { result in
            if result.isError { return }
            LSTPopView.removeAllPopView()
            Async.main(after: 0.1) {
                let vc = PurchaseSuccessView()
                vc.setRingData(name: model.name ?? "", imgUrl: model.quietUrl ?? "", imgBgHex: model.previewCode ?? "")
                vc.show()
            }
        }
    }
    
}
