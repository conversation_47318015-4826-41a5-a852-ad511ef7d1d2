//
//  WearPrePopView.swift
//  YINDONG
//
//  Created by jj on 2025/6/25.
//

import UIKit

class WearPrePopView: BaseDressPopView {
    
    var refeshBack: (() -> Void)?
        
    let wearLab = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 6)

    override func buildActionArea() {
        
        buyBtn.backgroundColor = .themColor
        buyBtn.layer.cornerRadius = 20
        buyBtn.addTarget(self, action: #selector(buyAction), for: .touchUpInside)

        actionArea.addSubview(buyBtn)
        buyBtn.snp.makeConstraints {
            $0.center.equalToSuperview()
            $0.width.equalToSuperview().multipliedBy(0.66)
            $0.height.equalTo(40)
        }
        
    }
    
    override func buildBody() {
        containerView.addSubview(nameLab)
        containerView.addSubview(wearLab)
        
        wearLab.layerCornerRadius = 8
        wearLab.backgroundColor = UIColor(white: 0, alpha: 0.3)
        wearLab.titleLab.font = .regular(11)
        wearLab.isHidden = true
        wearLab.snp.makeConstraints { make in
            make.top.right.equalToSuperview().inset(16)
            make.height.equalTo(16)
        }
        
        nameLab.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(headBgV.snp.bottom).offset(16)
        }
        
        containerView.addSubview(actionArea)
        actionArea.snp.makeConstraints {
            $0.top.equalTo(nameLab.snp.bottom).offset(24)
            $0.leading.trailing.bottom.equalToSuperview()
            $0.height.equalTo(40)
        }
    }
    
    override func bindModel() {
        super.bindModel()
        
        guard let m = item else { return }
        wearLab.isHidden = true
        buyBtn.setTitle(m.isWear == true ? "卸下" : "装扮", for: .normal)
        timeLab.isHidden = false
        timeLab.titleLab.text = m.effDateString
        timeLab.imgV.isHidden = m.effHide
        
        if m.goodsType == .mount {
            wearLab.isHidden = false
            wearLab.titleLab.text = "点击预览"
        }
    }
    
    override func buyAction() {
        guard let m = item else { return }
        let dict: [String: Any] = ["id": m.id]
        let api: ApiService = m.isWear ? .userUninstallDress(dict) : .userUpDress(dict)
        
        NetworkUtility.request(target: api) { result in
            if result.isError { return }
            self.dismiss()
            self.refeshBack?()
            
            LWUserManger.shared.getUserDis()
        }
    }

}
