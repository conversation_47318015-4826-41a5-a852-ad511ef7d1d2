//
//  DressBaseModel.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit

/// 装饰类型
enum DecorationType: Int, SmartCaseDefaultable {
    /// 头像框
    case avatarFrame           = 0
    /// 坐骑
    case mount                 = 1
    /// 主页特效
    case homeEffect            = 2
    /// 聊天气泡
    case chatBubble            = 3
    /// 语音房公屏气泡
    case voiceRoomPublicBubble = 4
    /// 语音房背景
    case voiceRoomBackground   = 5
    
    /// 中文描述
    var description: String {
        switch self {
        case .avatarFrame:           return "头像框"
        case .mount:                 return "坐骑"
        case .homeEffect:            return "主页特效"
        case .chatBubble:            return "聊天气泡"
        case .voiceRoomPublicBubble: return "语音房公屏气泡"
        case .voiceRoomBackground:   return "语音房背景"
        }
    }
}


enum DecorationPayType: Int, SmartCaseDefaultable {
    /// 0 – 钻石购买
    case diamond      = 0
    /// 1 – 会员
    case vip          = 1
    /// 2 – 活动
    case activity     = 2
    /// 3 – CP 专属
    case cp           = 3
    /// 4 – 贵族专属
    case noble        = 4
    /// 5 – 房间等级专属
    case roomLevel    = 5
}


class DressBaseModel: SmartCodable {
    var fontCode: String?
    var goodsName: String?
    var goodsType: DecorationType = .avatarFrame
    var goodsTypeName: String?
    var goodsUrlBottomLeft: String?
    var goodsUrlBottomRight: String?
    var goodsUrlPreview: String?
    var goodsUrlShow: String?
    var goodsUrlShowSex: Gender = .female
    var goodsUrlTopLeft: String?
    var goodsUrlTopRight: String?
    var gradientLeft: String?
    var gradientRight: String?
    var id: Int = 0
    var previewCode: String?
    var price3: Int = 0
    var price7: Int = 0
    var price15: Int = 0
    var purchaseType: DecorationPayType = .diamond
    var remarks: String?
    var showCode: String?
    //1752741994000
    var effectiveDate: Int64 = 0
    //是否使用中
    var isWear: Bool = false
    
    var effHide: Bool {
        if purchaseType == .noble {
            return true
        }
        
        if purchaseType == .vip {
            return true
        }
        
        if purchaseType == .roomLevel {
            return true
        }
        
        return false
    }
    
    //计算剩余时间字符串
    var effDateString: String {
        
        if purchaseType == .noble {
            return "贵族专属"
        }
        
        if purchaseType == .vip {
            return "会员专属"
        }
        
        if purchaseType == .roomLevel {
            return remarks ?? ""
        }
        guard effectiveDate > 0 else { return "—" }
        // 目标时间
        let target = Date(timeIntervalSince1970: TimeInterval(effectiveDate) / 1000)
        // 当前时间
        let now = Date()
        // 已过期
        guard target > now else { return "已过期" }
        // 向上取整的剩余天数
        let secondsLeft = target.timeIntervalSince(now)
        let daysLeft = Int(ceil(secondsLeft / 86_400))   // 86_400 = 24*60*60
        return "剩余\(daysLeft)天"
    }
    
    required init() {
        
    }
    
}

class RingBaseModel: SmartCodable {
    
    ///// 实际支付价格为0，则为活动获得
    var actualPrice : Int?
    /// 戒指入场特效
    var approachUrl : String?
    var channelType : Int = 0
    var charmValue : Int?
    var eastUrl : String?
    var giftFunction : Int?
    var goodsLabel : String?
    var goodsRemarks : String?
    var goodsTypeRemarks : String?
    var id : Int?
    var isMember : Int?
    var isShelf : Int?
    var isValuable : Int?
    var name : String?
    var previewCode : String?
    var price : Int?
    var quietUrl : String?
    var showCode : String?
    var type : Int?
    //是否可购买,0是可购买
    var isCashPurchase:Int = 0
    ///我的背包用
    var userGiftCount:Int?
    var userGiftCountStr:String?
    
    required init() {
        
    }
    
    
}
