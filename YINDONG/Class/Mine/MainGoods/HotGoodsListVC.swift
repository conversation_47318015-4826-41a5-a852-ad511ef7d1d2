//
//  HotGoodsListVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit
import JXSegmentedView

struct HotGoodsModel: SmartCodable {
    //道具
    var fragmentPopular = [GiftBaseModel]()
    //戒指
    var giftInfoPopular = [RingBaseModel]()
    //装扮
    var propInfoPopular = [DressBaseModel]()
    
}

enum ItemCategory: Int {
    /// 道具
    case prop       = 0
    /// 戒指
    case ring       = 1
    /// 装扮
    case dressUp    = 2
    
    /// 中文描述
    var desc: String {
        switch self {
        case .prop:    return "道具"
        case .ring:    return "戒指"
        case .dressUp: return "装扮"
        }
    }
}

class HotGoodsListVC: YinDBaseVC {
    
    var model: HotGoodsModel?
    
    var items: [ItemCategory] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // Do any additional setup after loading the view.
        loadData()
    }
    
    override func loadData() {
        
        NetworkUtility.request(target: .userHotGoodsList, model: HotGoodsModel.self) { result in
            if result.isError { return }
            self.model = result.model
            self.filterData()
            
        }
    }
    
    func filterData() {
        guard let model = model else { return }
        if !model.fragmentPopular.isEmpty {
            items.append(.prop)
        }
        
        if !model.giftInfoPopular.isEmpty {
            items.append(.ring)
        }
        
        if !model.propInfoPopular.isEmpty {
            items.append(.dressUp)
        }
        self.collectionView.reloadData()
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        
        collectionView.register(cellWithClass: GoodsCell.self)
        collectionView.register(cellWithClass: DressUpListCell.self)
        collectionView.register(
            SectionHeaderView.self,
            forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader,
            withReuseIdentifier: SectionHeaderView.identifier
        )
        collectionView.dataSource = self
        collectionView.delegate = self
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    override func configLayout() -> UICollectionViewFlowLayout {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 11.0 // 水平间距
        layout.minimumLineSpacing = 16 // 垂直间距
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16) // 边距
        return layout
    }
}

extension HotGoodsListVC: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}



extension HotGoodsListVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    // MARK: - Section Header Implementation
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        if kind == UICollectionView.elementKindSectionHeader {
            let headerView = collectionView.dequeueReusableSupplementaryView(
                ofKind: kind,
                withReuseIdentifier: SectionHeaderView.identifier,
                for: indexPath
            ) as! SectionHeaderView
            headerView.title = items[indexPath.section].desc
            return headerView
        }
        return UICollectionReusableView()
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        return CGSize(width: collectionView.frame.width, height: 40)
    }
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return items.count
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if items[section] == .prop {
            return model?.fragmentPopular.count ?? 0
        } else if items[section] == .ring {
            return model?.giftInfoPopular.count ?? 0
        } else if items[section] == .dressUp {
            return model?.propInfoPopular.count ?? 0
        }
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        
        let item = items[indexPath.section]
        
        if item == .prop {
            let item = model?.fragmentPopular[indexPath.item]
            let vc = GoodsBuyPopView()
            vc.type = item?.payType == 2 ? .gold : .diamond
            vc.model = item
            vc.show()
        }
        
        
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        
        if items[indexPath.section] == .prop {
            let cell = collectionView.dequeueReusableCell(withClass: GoodsCell.self, for: indexPath)
            let item = model?.fragmentPopular[indexPath.row]
            cell.type = item?.payType == 2 ? .gold : .diamond
            cell.item = item
            return cell
        }
        
        if items[indexPath.section] == .ring {
            
            let cell = collectionView.dequeueReusableCell(withClass: DressUpListCell.self, for: indexPath)
            guard let item = model?.giftInfoPopular[indexPath.row] else { return UICollectionViewCell() }
            
            cell.itemImageView.image = nil
            cell.itemImageView.setImage(from: item.quietUrl)
            
            cell.iconContainerView.backgroundColor = UIColor(hexString: item.showCode ?? "")
            cell.nameLabel.text = item.name ?? ""
            
            if item.isCashPurchase == 0{
                cell.diamondCountLabel.isHidden = false
                cell.tagLabel.isHidden = true
                cell.diamondCountLabel.titleLab.text = "\(item.actualPrice ?? 0)"
            }else{
                cell.diamondCountLabel.isHidden = true
                cell.tagLabel.isHidden = false
                cell.tagLabel.titleLab.text = "活动获得"
                cell.tagLabel.layer.borderColor = UIColor(hexString: "#5C76DC")?.cgColor
                cell.tagLabel.titleLab.textColor = UIColor(hexString: "#5C76DC")
            }
            return cell
        }
        
        
        if items[indexPath.section] == .dressUp {
            let cell = collectionView.dequeueReusableCell(withClass: DressUpListCell.self, for: indexPath)
            cell.item = model?.propInfoPopular[indexPath.row]
            return cell
        }
        
        return UICollectionViewCell()
    }
    
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        // 计算每个单元格的宽度，使其能够3个平铺显示
        let width = (AppTool.screenWidth - 54.5) / 3 // 减去间距后平分
        return CGSize(width: width, height: 170)
    }

}
