//
//  MyPackMainVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/25.
//

import UIKit
import JXSegmentedView

class MyPackMainVC: YinDBaseVC {
    
    private var segmentedDataSource: JXSegmentedTitleDataSource = {
        var segmeDataSource = JXSegmentedTitleDataSource()
        segmeDataSource.titleNormalColor = .textColor9 ?? .white
        segmeDataSource.titleSelectedColor = .textColor3 ?? .white
        segmeDataSource.titleNormalFont = .regular(16)
        segmeDataSource.titleSelectedFont = .medium(22)
        segmeDataSource.isTitleColorGradientEnabled = true
        segmeDataSource.isItemSpacingAverageEnabled = false
        segmeDataSource.itemWidth = JXSegmentedViewAutomaticDimension
        segmeDataSource.titles = ["道具", "戒指", "装扮"]
        return segmeDataSource
    }()
    
    //初始化JXSegmentedView
    private lazy var segmentedView : JXSegmentedView = {
        let segmentedView = JXSegmentedView()
        segmentedView.delegate = self
        segmentedView.dataSource = self.segmentedDataSource
        segmentedView.listContainer = listContainerView
        let lineIndicator = JXSegmentedIndicatorImageView()
        lineIndicator.image = UIImage(named: "title_selIcon")
        lineIndicator.indicatorWidth = 16
        lineIndicator.indicatorHeight = 4
        lineIndicator.verticalOffset = 2
        segmentedView.indicators = [lineIndicator]
        return segmentedView
    }()
    
    // 列表视图高度封装的类
    private lazy var listContainerView: JXSegmentedListContainerView! = {
        let listContainerView = JXSegmentedListContainerView(dataSource: self)
        listContainerView.listCellBackgroundColor = .clear
        return listContainerView
    }()

    override func viewDidLoad() {
        super.viewDidLoad()

        navigationTitle = "我的背包"
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        view.backgroundColor = .white
        
        view.addSubview(segmentedView)
        view.addSubview(listContainerView)
        
        segmentedView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(AppTool.navigationBarHeight)
            make.height.equalTo(49)
        }
        
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom).offset(15)
            make.left.right.bottom.equalToSuperview()
        }
        
    }
    

}

/// 列表视图数据源
extension MyPackMainVC: JXSegmentedListContainerViewDataSource, JXSegmentedViewDelegate {
    
    // 返回列表的数量
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return segmentedDataSource.titles.count
    }
    
    // 返回遵从协议的实例
    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        let vc = MyPackListVC()
        vc.type = index
        return vc
    }
}
