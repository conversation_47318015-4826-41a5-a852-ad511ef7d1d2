//
//  MyPackListVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/25.
//

import UIKit
import JXSegmentedView

class MyPackListVC: YinDBaseVC {
    
    var type: Int = 0
    
    var rows = [Any]()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        collectionView.register(cellWithClass: DressUpListCell.self)
        collectionView.register(cellWithClass: GoodsCell.self)
        
        collectionView.delegate = self
        collectionView.dataSource = self
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        loadData()
    }
    
    override func configLayout() -> UICollectionViewFlowLayout {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 11.0 // 水平间距
        layout.minimumLineSpacing = 16 // 垂直间距
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16) //
        return layout
    }
    
    override func loadData() {
        var api: ApiService = .userKnapsackList
        
        if type == 1 {
            api = .userRingList
        }
        
        if type == 2 {
            api = .userDecorateList
        }
        
        NetworkUtility.request(target: api) { result in
            if result.isError { return }
            let json = result.dataJson?.rawString() ?? ""
            if self.type == 0 {
                self.rows = [GiftBaseModel].deserialize(from: json) ?? []
            }
            
            if self.type == 1 {
                self.rows = [RingBaseModel].deserialize(from: json) ?? []
            }
            
            if self.type == 2 {
                self.rows = [DressBaseModel].deserialize(from: json) ?? []
            }
            self.collectionView.reloadData()
        }
        
    }
    
}

extension MyPackListVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    // 设置每个节的单元格数量
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        
        return rows.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        
        if type == 2 {
            let cell = collectionView.dequeueReusableCell(withClass: DressUpListCell.self, for: indexPath)
            if let item = rows[indexPath.row] as? DressBaseModel {
                cell.item = item
                cell.tagLabel.isHidden = true
                cell.timeLab.isHidden = item.effectiveDate == 0
                cell.timeLab.titleLab.text = item.effDateString
                cell.timeLab.imgV.isHidden = item.effHide
                cell.diamondCountLabel.isHidden = false
                cell.diamondCountLabel.imgV.isHidden = true
                cell.diamondCountLabel.titleLab.text = item.goodsTypeName
                cell.wearLab.isHidden = !item.isWear
                
                cell.wearLab.snp.updateConstraints { make in
                    make.width.equalTo(item.isWear ? 48 : 0)
                }

                
            }
            return cell
        }
        
        if type == 1 {
            let cell = collectionView.dequeueReusableCell(withClass: DressUpListCell.self, for: indexPath)
            let item = rows[indexPath.row] as? RingBaseModel
            cell.itemImageView.image = nil
            cell.itemImageView.setImage(from: item?.quietUrl)
            cell.iconContainerView.backgroundColor = UIColor(hexString: item?.showCode ?? "")
            cell.nameLabel.text = item?.name ?? ""
            cell.diamondCountLabel.imgV.isHidden = true
            cell.diamondCountLabel.titleLab.text = "剩余\(item?.userGiftCount ?? 1)个"
            cell.diamondCountLabel.isHidden = false
            cell.tagLabel.isHidden = true
            return cell
        }
        
        if type == 0 {
            let cell = collectionView.dequeueReusableCell(withClass: GoodsCell.self, for: indexPath)
            let item = rows[indexPath.item] as? GiftBaseModel
            cell.item = item
            cell.priceLabel.titleLab.text = "剩余\(item?.userGiftCount ?? 1)个"
            cell.priceLabel.imgV.isHidden = true
            return cell
        }
        
        return UICollectionViewCell()
    }
    
    // 设置每个单元格的大小
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let columns = 3
        let padding = 11.0 * CGFloat(columns - 1) + 32
        let width = (AppTool.screenWidth - CGFloat(padding)) / CGFloat(columns) - 1
        let height = width + 46
        
        return CGSize(width: width, height: height)
    }
    
    // 设置每个节的边距
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0, left: 16, bottom: AppTool.safeAreaBottomHeight, right: 16)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let item = rows[indexPath.row]
        if type == 2, let item = item as? DressBaseModel {
            let vc = WearPrePopView()
            vc.item = item
            vc.refeshBack = { [weak self] in
                self?.loadData()
            }
            vc.show()
        }
        
        if type == 1, let item = item as? RingBaseModel {
            let vc = RingBuyView()
            vc.isMake = true
            vc.model = item
            vc.show()
        }
        
        if type == 0, let item = item as? GiftBaseModel {
           
        }
        
        
    }
}

extension MyPackListVC: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}
