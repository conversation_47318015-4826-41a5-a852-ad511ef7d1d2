//
//  RingListVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit
import JXSegmentedView

class RingListVC: YinDBaseVC {

    var rows = [RingBaseModel]()

    
    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        loadData()
    }
    
    override func loadData() {
        
        NetworkUtility.request(target: .userDrRingList(["type": 4]), model: RingBaseModel.self, isList: true) { result in
            if result.isError { return }
            self.rows = result.modelArr
            self.collectionView.reloadData()
        }
    }
    
    override func lw_setupUI() {
        super.lw_setupUI()
        collectionView.register(cellWithClass: DressUpListCell.self)

        collectionView.delegate = self
        collectionView.dataSource = self
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
    }
    
    override func configLayout() -> UICollectionViewFlowLayout {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 11.0 // 水平间距
        layout.minimumLineSpacing = 16 // 垂直间距
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16) //
        return layout
    }

}

extension RingListVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    // 设置每个节的单元格数量
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {

        return rows.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withClass: DressUpListCell.self, for: indexPath)
        let item = rows[indexPath.row]
        
        cell.itemImageView.image = nil
        cell.itemImageView.setImage(from: item.quietUrl)

        cell.iconContainerView.backgroundColor = UIColor(hexString: item.showCode ?? "")
        cell.nameLabel.text = item.name ?? ""

        if item.isCashPurchase == 0{
            cell.diamondCountLabel.isHidden = false
            cell.tagLabel.isHidden = true
            cell.diamondCountLabel.titleLab.text = "\(item.actualPrice ?? 0)"
        }else{
            cell.diamondCountLabel.isHidden = true
            cell.tagLabel.isHidden = false
            cell.tagLabel.titleLab.text = "活动获得"
            cell.tagLabel.layer.borderColor = UIColor(hexString: "#5C76DC")?.cgColor
            cell.tagLabel.titleLab.textColor = UIColor(hexString: "#5C76DC")
        }
        return cell
    }
    
    // 设置每个单元格的大小
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let columns = 3
        let padding = 11.0 * CGFloat(columns - 1) + 32
        let width = (AppTool.screenWidth - CGFloat(padding)) / CGFloat(columns) - 1
        let height = width + 46
        
        return CGSize(width: width, height: height)
    }

    // 设置每个节的边距
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0, left: 16, bottom: AppTool.safeAreaBottomHeight, right: 16)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let item = rows[indexPath.row]
        
            let vv = RingBuyView()
            vv.model = item
            vv.show()
        

    }
}

extension RingListVC: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }
}
