//
//  MainGoodsVC.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit
import JXSegmentedView

class MainGoodsVC: YinDBaseVC {
    
    lazy var rightBtn: UIButton = {
        let view = UIButton(frame: CGRectMake(0, 0, 20, 20))
        view.addTarget(self, action: #selector(onItemClick), for: .touchUpInside)
        view.tx_Img("icon_daoju_shop_beibao")
        return view
    }()
    
    @objc func onItemClick() {
        let packVC = MyPackMainVC()
        navigationController?.pushViewController(packVC)
    }
    
    lazy var payBottomView: CurrencyBottomView = {
        let view = CurrencyBottomView()
        return view
    }()
    
    let titles = ["热门", "道具","戒指","装扮"]
    
    lazy var segmentedDataSource: JXSegmentedTitleImageDataSource = {
        let dataSource = JXSegmentedTitleImageDataSource()
        dataSource.titleImageType = .topImage
        dataSource.imageSize = CGSize(width: 47.0, height: 47.0)
        dataSource.isImageZoomEnabled = false
        dataSource.titleNormalColor = .init(hex: 0x989898) ?? .white
        dataSource.titleSelectedColor = .textColor3 ?? .white
        dataSource.itemSpacing = 0
        return dataSource
    }()
    
    lazy var segmentedView: JXSegmentedView = {
        let view = JXSegmentedView()
        view.dataSource = segmentedDataSource
        
        view.listContainer = listContainerView
        let indicator = LWJXSegmentedIndicatorImageView()
        indicator.indicatorWidth = 47
        indicator.indicatorHeight = 47
        indicator.indicatorPosition = .top
        indicator.verticalOffset = 5
        indicator.selectIndex = selectIndex
        view.indicators = [indicator]
        return view
    }()
    
    lazy var listContainerView: JXSegmentedListContainerView! = {
        return JXSegmentedListContainerView(dataSource: self)
    }()

    var selectIndex: Int = 0
    
    override func viewDidLoad() {
        super.viewDidLoad()

        navigationTitle = "商城"
    }

    override func lw_setupUI() {
        super.lw_setupUI()
        
        navigation.item.rightBarButtonItems = [UIBarButtonItem(customView: rightBtn)]

        segmentedDataSource.titles = titles
        segmentedDataSource.normalImageInfos = ["icon_shop_hot","icon_shop_daoju","icon_shop_jiezhi","icon_shop_zhuangban"]
        segmentedDataSource.selectedImageInfos = ["icon_shop_hot","icon_shop_daoju","icon_shop_jiezhi","icon_shop_zhuangban"]
        segmentedDataSource.itemWidth = AppTool.screenWidth / CGFloat(segmentedDataSource.titles.count)
        
        segmentedDataSource.loadImageClosure = {(imageView, normalImageInfo) in
            imageView.image = UIImage(named: normalImageInfo)
        }

        segmentedView.defaultSelectedIndex = selectIndex
        segmentedView.reloadData()
        
        self.view.addSubview(segmentedView)
        self.view.addSubview(listContainerView)
        self.view.addSubview(payBottomView)
        
        segmentedView.snp.makeConstraints { make in
            make.top.equalTo(AppTool.navigationBarHeight)
            make.left.right.equalTo(self.view)
            make.height.equalTo(80.0)
        }
        listContainerView.snp.makeConstraints { make in
            make.left.right.equalTo(self.view)
            make.top.equalTo(self.segmentedView.snp.bottom)
            make.bottom.equalTo(self.payBottomView.snp.top)
        }
        payBottomView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(40)
            make.bottom.equalTo(view.safeAreaLayoutGuide)
        }
    }

}

extension MainGoodsVC: JXSegmentedListContainerViewDataSource {
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        if let titleDataSource = segmentedView.dataSource as? JXSegmentedBaseDataSource {
            return titleDataSource.dataSource.count
        }
        return 0
    }

    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        
        if index == 0 {
            let vc = HotGoodsListVC()
            return vc
        }
        
        if index == 1{
            let vc = SkillGoodsMainVC()
            vc.isGoods = true
            return vc
        }
        if index == 2 {
            let vc = RingListVC()
            return vc
        }
        
        let vc = DecorationMainVC()
        return vc
    }
}

/// 带图像指示器的 SegmentedIndicator
class LWJXSegmentedIndicatorImageView: JXSegmentedIndicatorBaseView {
    // MARK: - 配置图标名称
    private static let imageNames = [
        "icon_shop_nav_remen",
        "icon_shop_nav_daoju",
        "icon_shop_nav_jiezhi",
        "icon_shop_nav_zhuangban"
    ]
    /// 预加载所有图片的 CGImage
    private static let images: [CGImage] = imageNames.compactMap { UIImage(named: $0)?.cgImage }
    
    /// 当前选中索引，会自动切换图像
    var selectIndex: Int = 0 {
        didSet {
            let idx = Swift.max(0, Swift.min(selectIndex, LWJXSegmentedIndicatorImageView.images.count - 1))
            layer.contents = LWJXSegmentedIndicatorImageView.images[idx]
        }
    }
    
    // MARK: - 初始化
    open override func commonInit() {
        super.commonInit()
        indicatorWidth = 20
        indicatorHeight = 20
        layer.contentsGravity = .resizeAspect
        // 初始化第一张图片
        if let first = LWJXSegmentedIndicatorImageView.images.first {
            layer.contents = first
        }
    }
    
    // MARK: - 刷新状态
    open override func refreshIndicatorState(model: JXSegmentedIndicatorSelectedParams) {
        super.refreshIndicatorState(model: model)
        backgroundColor = .clear
        let width = getIndicatorWidth(itemFrame: model.currentSelectedItemFrame, itemContentWidth: model.currentItemContentWidth)
        let height = getIndicatorHeight(itemFrame: model.currentSelectedItemFrame)
        let x = model.currentSelectedItemFrame.minX + (model.currentSelectedItemFrame.width - width) / 2
        let y: CGFloat
        switch indicatorPosition {
        case .top:
            y = verticalOffset
        case .bottom:
            y = model.currentSelectedItemFrame.height - height - verticalOffset
        case .center:
            y = (model.currentSelectedItemFrame.height - height) / 2 + verticalOffset
        }
        frame = CGRect(x: x, y: y, width: width, height: height)
    }
    
    // MARK: - 切换选中
    open override func selectItem(model: JXSegmentedIndicatorSelectedParams) {
        super.selectItem(model: model)
        selectIndex = model.currentSelectedIndex
        let width = getIndicatorWidth(itemFrame: model.currentSelectedItemFrame, itemContentWidth: model.currentItemContentWidth)
        var newFrame = frame
        newFrame.origin.x = model.currentSelectedItemFrame.minX + (model.currentSelectedItemFrame.width - width) / 2
        if canSelectedWithAnimation(model: model) {
            UIView.animate(withDuration: scrollAnimationDuration, delay: 0, options: .curveEaseOut) {
                self.frame = newFrame
            }
        } else {
            frame = newFrame
        }
    }
    
    // MARK: - 滑动过渡
    open override func contentScrollViewDidScroll(model: JXSegmentedIndicatorTransitionParams) {
        super.contentScrollViewDidScroll(model: model)
        guard canHandleTransition(model: model) else { return }
        let left = model.leftItemFrame
        let right = model.rightItemFrame
        let percent = CGFloat(model.percent)
        let targetWidth = getIndicatorWidth(itemFrame: left, itemContentWidth: model.leftItemContentWidth)
        let leftX = left.minX + (left.width - targetWidth) / 2
        let rightX = right.minX + (right.width - targetWidth) / 2
        frame.origin.x = JXSegmentedViewTool.interpolate(from: leftX, to: rightX, percent: percent)
    }
}
