//
//  MenuItemManager.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit

// MARK: - 菜单项配置管理器
class MenuItemManager {
    static let shared = MenuItemManager()
    private init() {}
    
    /// 获取功能区域菜单项
    func getFunctionMenuItems() -> [MenuItemModel] {
        let allItems = getAllMenuItems()
        return allItems.filter { $0.category == .function }
    }
    
    /// 获取服务区域菜单项
    func getServiceMenuItems() -> [MenuItemModel] {
        let allItems = getAllMenuItems()
        return allItems.filter { $0.category == .service }
    }
    
    /// 获取所有可用菜单项
    private func getAllMenuItems() -> [MenuItemModel] {
        var items: [MenuItemModel] = []
        
        // 功能区域
//        items.append(MenuItemModel(type: .family))
//        items.append(MenuItemModel(type: .cpRoom))
        items.append(MenuItemModel(type: .backpack, badgeCount: getBackpackBadgeCount()))
//        items.append(MenuItemModel(type: .title))
        
        if !appCodelw {
            items.append(MenuItemModel(type: .workshop))
//            items.append(MenuItemModel(type: .checkin, isNew: isCheckinNew()))
//            items.append(MenuItemModel(type: .newbieGift, isNew: true))
        }
        
//        items.append(MenuItemModel(type: .skill))
//        items.append(MenuItemModel(type: .store))
        
        // 服务区域
        
        items.append(MenuItemModel(type: .share))
        items.append(MenuItemModel(type: .help))
        items.append(MenuItemModel(type: .settings))
        
        return items.filter { $0.isEnabled }
    }
    
    /// 处理菜单项点击事件
    func handleMenuItemTap(_ type: MenuItemType) {
        
        let viewController = AppTool.getCurrentViewController()
        
        switch type {
        // 功能区域
        case .family:
            // TODO: 跳转家族页面
            print("跳转家族页面")
            
        case .cpRoom:
            // TODO: 跳转CP小屋
            print("跳转CP小屋")
            
        case .backpack:
            let bk = MyPackMainVC()
            viewController.navigationController?.pushViewController(bk, animated: true)
            
        case .title:
            // TODO: 跳转称号页面
            print("跳转称号页面")
            
        case .workshop:
            let vc = MansionMainVC()
            viewController.navigationController?.pushViewController(vc, animated: true)
            
        case .checkin:
            // TODO: 跳转签到页面
            print("跳转签到页面")
            
        case .newbieGift:
            // TODO: 跳转新人礼包
            print("跳转新人礼包")
            
        case .skill:
            let vc = SkillMainListVC()
            viewController.navigationController?.pushViewController(vc, animated: true)
            
        case .store:
            // TODO: 跳转商店
            print("跳转商店")
            let vc = MainGoodsVC()
            viewController.navigationController?.pushViewController(vc, animated: true)
            
        case .share:
            let items = [URL(string: WebURL.shareAppUrl.fullURL)!]
            let ac = UIActivityViewController(activityItems: items, applicationActivities: nil)
            viewController.present(ac, animated: true)
            
        case .help:
            LWJumpManger.goUrl(WebURL.H5_Helper.fullURL)
            
        case .settings:
            let settingsVC = SettingsVC()
            viewController.navigationController?.pushViewController(settingsVC, animated: true)
        }
    }
    
    // MARK: - Private Methods
    
    private func getBackpackBadgeCount() -> Int? {
        // TODO: 获取背包未读数量
        return nil
    }
    
    private func isCheckinNew() -> Bool {
        // TODO: 判断签到是否有新内容
        return false
    }
}
