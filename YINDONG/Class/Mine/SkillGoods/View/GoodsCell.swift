//
//  GoodsCell.swift
//  YINDONG
//
//  Created by jj on 2025/5/19.
//

import UIKit

class GoodsCell: UICollectionViewCell {
    
    var type: CurrencyType?
    
    // MARK: - UI组件
    let containerView = UIView()
    
    let extraLabel: UIImageView = {
        let label = UIImageView()
//        label.isHidden = true
        return label
    }()
    
    let iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    let titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .textColor3
        label.textAlignment = .center
        label.font = .regular(14)
        return label
    }()
    
    
    // 价格标签
    let priceLabel: InfoItemBaseView = {
        let label = InfoItemBaseView(rightImg: "", leftTitle: "", isLeft: true, space: 2, imgWH: CGSizeMake(14, 14), margin: 0)
        label.titleLab.textColor = UIColor(hexString: "#989898")
        label.titleLab.font = .medium(11)
        label.titleLab.textAlignment = .left
        return label
    }()
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    

    // MARK: - UI设置
    private func setupUI() {
        // 设置购买卡片UI
        backgroundColor = .clear
        
        // 白色卡片容器 - 只包含图标和标题
        containerView.backgroundColor = .f9Color
        containerView.layer.cornerRadius = 10
        containerView.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowOpacity = 0.6
        containerView.layer.shadowRadius = 4
        contentView.addSubview(containerView)
        
        // 添加子视图
        contentView.addSubview(extraLabel) // 额外送标签放到容器外部
        // 图标和标题放在容器内
        containerView.addSubview(iconImageView)
        contentView.addSubview(titleLabel)
        
        // 价格和购买按钮放在容器外部
        contentView.addSubview(priceLabel)
    
        // 设置约束
        setupConstraints()
    }
    
    private func setupConstraints() {
        // 白色容器仅包住图标和标题
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(5)
            make.centerX.equalToSuperview()
            make.width.equalTo(contentView.snp.width).offset(-10)
            make.height.equalTo(110) // 只包含图标和标题的高度
        }
        
        // 额外赠送标签
        extraLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.right.equalToSuperview().offset(-5)
            make.height.equalTo(20)
            make.width.equalTo(80)
        }
        
        // 图标居中
        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.centerX.equalTo(containerView)
            make.width.height.equalTo(85)
        }
        
        // 标题在图标下方
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(containerView.snp.bottom).offset(8)
            make.left.equalTo(containerView)
        }
        
        // 价格容器在容器外部
        priceLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(2)
            make.left.equalTo(titleLabel)
            make.height.equalTo(20)
        }
    
    }
    
    var item: GiftBaseModel? {
        didSet{
            guard let item = item else { return }
            configure(with: item)
        }
    }
    
    // MARK: - 配置
    func configure(with item: GiftBaseModel) {
        // 使用服务器返回的数据设置 UI
        titleLabel.text = item.name
        
        iconImageView.setImage(from: item.quietUrl)
        // 设置价格
        priceLabel.titleLab.text = "\(item.actualPrice ?? "")"
        containerView.backgroundColor = item.getStoneBgColor()
        
        // 根据类型设置货币图标
        priceLabel.imgV.image = UIImage(named: type?.iconName ?? "")
        if isValid(item.labelUrl) {
            extraLabel.setImage(from: item.labelUrl)
        } else {
            extraLabel.image = nil
        }
        
    }

}
