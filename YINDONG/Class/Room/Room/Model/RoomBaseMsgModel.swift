//
//  RoomBaseMsgModel.swift
//  YINDONG
//
//  Created by jj on 2025/4/24.
//

import UIKit

class RoomBaseMsgModel: SmartCodable {
    var atUsers = [SeatInfo]()
    var receiverSeats = [SeatInfo]()
    var buddle: MsgBuddleModel?
    var ext: String?
    var honor: String?
    var imageUrl: String?
    var liveRoomNo: String?
    var lotteryName: String?
    var msgType: RoomMessageType = .unknown
    var type: RoomMessageType = .unknown
    
    var giftValues: [RoomGiftCountModel]?
    
    @SmartAny
    var roomBaseMsg: V2TIMMessage?
    
    var iMsgType: RoomMessageType {
        if msgType == .unknown {
            return type
        }
        return msgType
    }
    
    var showName: String {
        if let name = nickName, !name.isEmpty {
            return name
        }
        
        if let name = mainName, !name.isEmpty {
            return name
        }
        if let name = userName, !name.isEmpty {
            return name
        }
        return ""
    }
    
    var nobleLevel: Int = 0
    var role: Int = 0
    var sex: Gender = .unKnow
    var text: String?
    var txtContent: String?
    @SmartAny
    var content: [String: Any]?
    var userId: String?
    var imId: String?
    var vLevel: Int = 0
    
    var nickName: String?
    var userName: String?
    var bossName: String?
    var giftFunction: Int = 0
    var giftName: String?
    var giftNum: Int = 0
    var giftQuietUrl: String?
    var giftType: Int = 0
    var isHurt: Int?
    var mainImId: Int = 0
    var mainName: String?
    var mainPhotoUrl: String?
    var source: Int = 0
    var photoUrl: String?
    
    var gifts: [GiftItemModel] = []
    var giftList: [GiftItemModel] = []
    var hurtValue: Int = 0
    var typeCode: Int?
    
    
    var giftId: String?
    var giftValue: Int?
    var giftPrice: Int?
    var giftPic: String?
    var effectUrl: String?
    
    
    ///屠龙需要的信息
    var isCriticalStrike: Int? //大于 0 暴击
    var fromNickName: String?
    var fromPhotoUrl: String?
    var toImUserIdArry: [String]?
    var isCriticalStrikeText: String? {
        return isCriticalStrike ?? 0 > 0 ? "暴击" : nil
    }
    var redEnvelopesId: String?
    
    ///技能相关的
    var charm: Int?
    var inMask: Int?
    var otherUserImNumber: Int?
    var otherUserNickNumber: String?
    var personPoints: Int?
    var powerPoints: Int?
    var runeFengyin: Int?
    var runeTianmi: Int?
    var skillType: SkillType?
    var triggerBreak: Int?
    var userImNumber: Int?
    var userNickName: String?
    var value: Int?
    var yuanbao: Int?
    var negativeEffectName: String?     // 净化的负面效果名
    var randomSkillName: String?         // 被封印的技能名
    var chaosTime: Int?                   // 封印时长（分钟）

    func breakSkillHitDesc() -> String {
        return runeStoneHitDesc() + runeStoneHitDesc2() + breakDescCore()    
    }
      
      // MARK: - ② Rune Stone（甜蜜）------------------------------------------
      private func runeStoneHitDesc() -> String {
          // Kotlin: runeTianmi == 0 时触发
          if (runeTianmi ?? 1) == 0 {
              return "，恋爱指数+1"
          }
          return ""
      }
      
      // MARK: - ③ Rune Stone（封印）------------------------------------------
      private func runeStoneHitDesc2() -> String {
          // Kotlin: runeFengyin == 0 时触发
          if (runeFengyin ?? 1) == 0 {
              // GameUtils.getSkillName → 枚举里的 displayName
              return "，\(skillType?.title ?? "")被封印5分钟"
          }
          return ""
      }
      
      // MARK: - ④ 突破技核心描述 ---------------------------------------------
      private func breakDescCore() -> String {
          guard (triggerBreak ?? 1) == 0 else { return "" }   // 只在 triggerBreak = 0 时生效
          
          switch skillType {
          case .skill1:   // 旋风腿
              return "，被打出破财效果元宝-\(yuanbao ?? 0)"
              
          case .skill5:   // 么么哒
              let negative = negativeEffectName ?? ""
              return "，产生净化效果解除身上的\(negative)负面效果"
              
          case .skill2:   // 点穴手
              let name  = skillType?.title ?? ""
              let time  = chaosTime ?? 0
              return "，被打出封印效果\(name)技能被封印\(time)分钟"
              
          case .skill3:   // 变猪头
              return "，被打出致盲效果下一次使用技能必能失败"
              
          default:
              return ""
          }
      }
    
    required init() {
        
    }
    
    static func empty() -> RoomBaseMsgModel {
        let model = RoomBaseMsgModel()
        model.userId = kuser.imNumber.string
        model.sex = kuser.sex
        return model
    }
}

struct GiftItemModel: SmartCodable {
    
    var actualPrice: Int = 0
    var giftType: Int = 0
    var name: String?
    var num: Int = 0
    var quietUrl: String?
    var source: Int = 0
    
    
}

struct MsgBuddleModel: SmartCodable {
    var goodsUrlBottomLeft: String?
    var goodsUrlBottomRight: String?
    var goodsUrlTopLeft: String?
    var goodsUrlTopRight: String?
    var gradientLeft: String?
    var gradientRight: String?
    var previewCode: String?
}

///攻击 boss 消息
struct BossAttackMsg: SmartCodable {
    var msgType: RoomMessageType = .attackBoss
    var isHurt: Int?
    var hurtValue: Int?
    var hurtType: Int?
    var gifts: [GiftItemModel] = []
    
    ///路径点固定
    var startValueX: Double = Bool.random() ? Double.random(in: 0...1) : -Double.random(in: 0...1)
    var endValueX: Double = Bool.random() ? Double.random(in: 0...1) : -Double.random(in: 0...1)
    var endValueY: Double = Bool.random() ? Double.random(in: 0...1) : -Double.random(in: 0...1)
    var centerValueX: Double = Double.random(in: 0...1)
    var centerValueY: Double = Double.random(in: 0...1)
}


///构建 基础消息
struct MsgRoomTextMsg: SmartCodable {
    var userId: String?
    var atUsers = [SeatInfo]()
    var msgType: RoomMessageType = .kickRoom
    var txtContent: String?
    var sex: Gender?
    var ext: String?
    var honor: String?
    var nobleLevel: Int?
    var role: Int?
    var text: String?
    
    
    var giftName: String?
    var giftId: String?
    var giftValue: Int?
    var giftCount: Int?
    var giftPrice: Int?
    var giftPic: String?
    var effectUrl: String?
    var receiverSeats = [SeatInfo]()
    var isValuable: Bool?
    var sendGiftTime: Int?
    var isShowCombo: Int?
    var photoUrl: String?
    var nickName: String?
    
    var giftValues: [RoomGiftCountModel]?
    
    ///快速构造 5002 消息
    static func creatGift(_ m: GiftBaseModel?) -> MsgRoomTextMsg {
        var msg = MsgRoomTextMsg(userId: kuser.imNumber.string, msgType: .giftAnimation, role: RoomManger.shared.curretRole?.role)
        msg.giftId = m?.id.string
        msg.giftName = m?.name
        msg.giftPic = m?.quietUrl
        msg.effectUrl = m?.eastUrl
        msg.giftPrice = m?.actualPrice?.int
        msg.giftValue = m?.isPrice() == true ? m?.realGiftValue() : 0
        msg.sendGiftTime = Int(Date().timeIntervalSinceNow) * 1000
        msg.isValuable = m?.isPrice() ?? false
        msg.photoUrl = kuser.photoUrl
        msg.nickName = kuser.nickName
        msg.honor = UserTitleModel.getFirstModel(LWUserManger.shared.userTitles)?.codUrl
        msg.nobleLevel = LWUserManger.shared.nobInfo?.levelId ?? 1
        msg.role = RoomManger.shared.curretRole?.role ?? 0
        msg.sex = kuser.sex
        return msg
    }
    
    ///快速构造 7 消息
    static func creatGiftEnd(_ m: RoomBaseMsgModel?, count: Int) -> RoomBaseMsgModel? {
        guard let msg = m else { return nil }
        msg.msgType = .gift
        msg.atUsers = m?.receiverSeats ?? []
        msg.imageUrl = m?.giftPic
        let names = msg.atUsers.compactMap({ return $0.userName }).joined(separator: "、")
        msg.txtContent = "给\(names) 送出\(msg.giftName ?? "")x\(count)"
        return msg
    }
    
}

