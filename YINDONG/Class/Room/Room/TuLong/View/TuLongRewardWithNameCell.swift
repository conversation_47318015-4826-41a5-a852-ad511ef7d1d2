//
//  TuLongRewardWithNameCell.swift
//  YINDONG
//
//  Created by jj on 2025/6/23.
//

import UIKit

class TuLongRewardWithNameCell: TuLongRewardCell {
    
    // MARK: - UI 组件
    
    /// 礼物名称标签
    lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.textColor = .init(hex: 0xFFF0C3)
        label.font = .medium(8)
        label.textAlignment = .center
        label.backgroundColor = UIImage(dvt: [.init(hex: 0x670000), .init(hex: 0xCD0000)], size: CGSizeMake(48, 12), direction: .left2right)?.imageToColor()
        label.layer.cornerRadius = 2
        label.layer.masksToBounds = true
        label.numberOfLines = 1
        return label
    }()
    
    /// 右上角数量气泡
    lazy var bubbleView: InfoItemBaseView = {
        let label = InfoItemBaseView(rightImg: nil, leftTitle: "", margin: 3)
        label.titleLab.textColor = .init(hex: 0x956C51)
        label.titleLab.font = .medium(6)
        label.backgroundColor = .init(hex: 0xFFD773)
        label.layer.cornerRadius = 6
        label.layer.masksToBounds = true
        return label
    }()
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupAdditionalUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupAdditionalUI()
    }
    
    // MARK: - UI 设置
    
    private func setupAdditionalUI() {
        
        bgImgView.snp.remakeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(36)
        }
        
        giftImageView.snp.remakeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(30)
        }
        // 添加名称标签
        contentView.addSubview(nameLabel)
        nameLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(12)
        }
        
        // 添加气泡视图
        contentView.addSubview(bubbleView)
        bubbleView.snp.makeConstraints { make in
            make.top.equalTo(bgImgView)
            make.right.equalToSuperview().offset(2)
            make.height.equalTo(12)
        }
    }
    
    // MARK: - 配置数据
    
    override func configure(with reward: TuLongRewardGiftModel) {
        // 调用父类配置方法
        super.configure(with: reward)
        
        // 设置礼物名称
        nameLabel.text = reward.giftName ?? "礼物名称"
        
        // 设置数量气泡
        if let a = reward.giftNum {
            bubbleView.isHidden = false
            bubbleView.titleLab.text = a
        } else {
            bubbleView.isHidden = true
        }
    }
    
    // MARK: - 重写准备重用
    
    override func prepareForReuse() {
        super.prepareForReuse()
        nameLabel.text = ""
        bubbleView.isHidden = true
    }
}

