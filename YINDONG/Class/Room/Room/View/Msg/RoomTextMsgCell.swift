//
//  RoomTextMsgCell.swift
//  YINDONG
//
//  Created by jj on 2025/5/19.
//

import UIKit

class RoomTextMsgCell: UITableViewCell {
    
    // 气泡背景
    lazy var messageBackgroundView: BaseChatBgView = {
        let view = BaseChatBgView()
        view.creatRoom()
//        view.backgroundColor = .white.withAlphaComponent(0.1)
//        view.layer.cornerRadius = 12
        return view
    }()
    
    // 你原有的用户信息条
    lazy var userInfoV: UIStackView = {
        let vv = UIStackView(arrangedSubviews: [], axis: .horizontal, spacing: 2, alignment: .center, distribution: .fill)
        return vv
    }()
    
    lazy var icoimgV: UIImageView = {
        let imgV = UIImageView()
        imgV.contentMode = .scaleAspectFill
        imgV.layerCornerRadius = 8
        return imgV
    }()
    
    lazy var roomQxIcon: UIImageView = {
        let imgV = UIImageView()
        return imgV
    }()
    
    lazy var nameLab: UILabel = {
        let nameLab = UILabel(withText: "", textColor: .init(hex: 0xE9E9F0), fontType: .regular(11))
        return nameLab
    }()
    
    lazy var hourV: SVGAnimationPlayer = {
        let vv = SVGAnimationPlayer()
        vv.isHidden = true
        return vv
    }()
    
    // 消息内容
    lazy var msgLabel: YYLabel = {
        let lab = YYLabel()
        lab.numberOfLines = 0
        lab.textColor = .white
        lab.font = .systemFont(ofSize: 16)
        return lab
    }()
    
    lazy var pigImgV: UIImageView = {
        let imgV = UIImageView(image: UIImage(named: "room_zhutou"))
        imgV.isHidden = true
        return imgV
    }()
    
    
    var messageEntity: RoomBaseMsgModel? {
        didSet {
            // 设置 icoimgV、nameLab、hourV、msgLabel 内容
            guard let messageEntity = messageEntity else { return }
            icoimgV.image = nil
            
            pigImgV.isHidden = true
            if messageEntity.userId?.int?.isIMMe == false, messageEntity.ext == "hyly" {
                pigImgV.isHidden = false
            }
            
            if messageEntity.userId?.int?.isIMMe == true, RoomManger.shared.skillUseInfo.value?.talkRubbishCount ?? 0 > 0 {
                pigImgV.isHidden = false
                
            }
            
        
            
            if isValid(messageEntity.nickName) && isValid(messageEntity.photoUrl) {
                icoimgV.setImage(from: messageEntity.photoUrl)
                nameLab.text = messageEntity.nickName
            } else {
                icoimgV.setImage(from: messageEntity.roomBaseMsg?.faceURL)
                nameLab.text = messageEntity.roomBaseMsg?.nickName
            }
        
            
            if isValid(messageEntity.honor) {
                hourV.isHidden = false
                hourV.loadAnimation(named: messageEntity.honor ?? "")
            } else {
                hourV.isHidden = true
            }
            
            let isManger = RoomManger.shared.snapModel?.managers.contains(where: { $0 == messageEntity.userId })
            roomQxIcon.isHidden = true
            if isManger == true {
                roomQxIcon.isHidden = false
                roomQxIcon.image = UIImage(named: "room_glIcon")
            }
            
            if RoomManger.shared.roomUserID == messageEntity.userId {
                roomQxIcon.isHidden = false
                roomQxIcon.image = UIImage(named: "room_ownericon")
            }
            
            var text = messageEntity.txtContent ?? ""
            
            if messageEntity.userId?.int?.isIMMe == true, messageEntity.ext == "hyly" {
                text = text.replacingOccurrences(ofPattern: "胡言乱语中", withTemplate: messageEntity.text ?? "")
            }
            
            let attributedText = NSMutableAttributedString(string: text)
            attributedText.yy_font = .regular(12)
            attributedText.yy_color = .white
            let preix = messageEntity.iMsgType == .gift ? "" : "@"
            messageEntity.atUsers.forEach { seatInfo in
                let textName = "\(preix)\(seatInfo.userName ?? "")"
                let ranges = AppTool.findRanges(of: attributedText.string, in: textName)
                ranges.forEach { range in
                    attributedText.yy_setTextHighlight(range, color: .themColor, backgroundColor: .clear) { vv, att, range, rect in
                        RoomManger.shared.seatSheetCardSubject.accept(seatInfo.userId)
                    }
                }
            }
            
            
            
            let maxSize = CGSize(width: 160.auto(), height: .greatestFiniteMagnitude)
            let layout  = YYTextLayout(containerSize: maxSize, text: attributedText)
            msgLabel.textLayout = layout
            
            
            if let buddle = messageEntity.buddle {
                setBubble(model: buddle)
//                messageBackgroundView.layer.cornerRadius = 0
                messageBackgroundView.layer.cornerRadius = 0
                messageBackgroundView.layer.masksToBounds = false
            } else {
                messageBackgroundView.backgroundColor = .white.withAlphaComponent(0.1)
                messageBackgroundView.clear()
                messageBackgroundView.layer.cornerRadius = 12
                messageBackgroundView.layer.masksToBounds = true
            }
            
            // 强制 layout，拿到 userInfoV 的最终 width
            layoutIfNeeded()
            let infoWidth = userInfoV.frame.width
            let textWidth = layout?.textBoundingSize.width ?? 0
            
            let finalWidth = max(textWidth, infoWidth)
            let finalSize = CGSize(width: finalWidth, height: layout?.textBoundingSize.height ?? 0)
            
            msgLabel.snp.updateConstraints { make in
                make.size.equalTo(finalSize)
                
            }
            
            
            
        }
    }
    
    func setBubble(model: MsgBuddleModel) {
        
        messageBackgroundView.play(withMinXMinY: model.goodsUrlTopLeft, minXMaxY: model.goodsUrlBottomLeft, maxXminY: model.goodsUrlTopRight, maxXMaxY: model.goodsUrlBottomRight)
               
        if let gradientLeft = model.gradientLeft,let gradientRight = model.gradientRight,gradientLeft.isEmpty == false,gradientRight.isEmpty == false{
            messageBackgroundView.gradientLayer.isHidden = false
            messageBackgroundView.gradientLayer.colors = [UIColor(hexString: gradientLeft)!.cgColor,UIColor(hexString: gradientRight)!.cgColor]
            
        }
        messageBackgroundView.bgImgV.backgroundColor = .clear
        
    }
    
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(messageBackgroundView)
        messageBackgroundView.addSubview(userInfoV)
        messageBackgroundView.addSubview(msgLabel)
        
        messageBackgroundView.snp.makeConstraints { make in
            make.top.equalTo(contentView)
            make.bottom.equalTo(contentView).inset(10)
            make.left.equalTo(contentView).inset(15)
        }
        
        userInfoV.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.equalTo(10)
            make.height.equalTo(20)
        }
        
        // 保证 userInfoV 结构不变
        userInfoV.addArrangedSubview(icoimgV)
        userInfoV.addArrangedSubview(roomQxIcon)
        
        userInfoV.addArrangedSubview(nameLab)
        userInfoV.addArrangedSubview(hourV)
        
        messageBackgroundView.addSubview(pigImgV)
        pigImgV.snp.makeConstraints { make in
            make.edges.equalTo(icoimgV)
        }
        
        icoimgV.snp.makeConstraints { make in
            make.width.height.equalTo(16)
        }
        
        roomQxIcon.snp.makeConstraints { make in
            make.width.height.equalTo(14)
        }
        
        hourV.snp.makeConstraints { make in
            make.width.equalTo(44)
            make.height.equalTo(18)
        }
        
        msgLabel.snp.makeConstraints { make in
            make.left.equalTo(30)
            make.right.equalTo(-10)
            make.bottom.equalTo(-10)
            make.top.equalTo(userInfoV.snp.bottom).offset(5)
            make.size.equalTo(CGSize(width: 80, height: 80))
        }
        
        roomQxIcon.snp.makeConstraints { make in
            make.width.height.equalTo(14)
        }
        
        
        
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

