//
//  RoomMessageCell.swift
//  YINDONG
//
//  Created by jj on 2025/4/24.
//

import UIKit
import YYText
import Atributika
import AtributikaViews
import Kingfisher

class RoomMessageCell: UITableViewCell {
    
    let maxMsgWidth = AppTool.screenWidth * 0.65
    
    lazy var bgContentView: UIView = {
        let vv = UIView()
        vv.backgroundColor = .white.withAlphaComponent(0.1)
        vv.layerCornerRadius = 12
        return vv
    }()
    
    // MARK: UI
    lazy var msgLabel: YYLabel = {
        let lab = YYLabel()
        lab.numberOfLines = 0
        lab.textColor = .white
        lab.textVerticalAlignment = .center
        lab.preferredMaxLayoutWidth = maxMsgWidth - 30
        return lab
    }()
    
    var attributedText = NSMutableAttributedString(string: "")
    
    // MARK: Init
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        backgroundColor = .clear
        
        contentView.addSubview(bgContentView)
        bgContentView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalTo(15)
            make.bottom.equalTo(-10)
        }
        
        bgContentView.addSubview(msgLabel)
        msgLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(10)   // 上下左右 10
        }
    }
    
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    
    var entity: RoomBaseMsgModel?
    // MARK: Public
    func configure(with entity: RoomBaseMsgModel) {
        self.entity = entity
        
        attributedText = NSMutableAttributedString(string: entity.txtContent ?? "")
        attributedText.yy_font = .regular(13)
        attributedText.yy_color = .white
        
        
        
        switch entity.iMsgType {
        case .globalNotice, .globalNotice2, .globalNotice3, .treeFreeOrderAnn, .attributedGeneral:
            quanFU()
        case .welcome:
            attributedText = NSMutableAttributedString(string: "")
            let name = entity.atUsers.first?.userName ?? ""
            addText(text: "欢迎 \(name) 来到房间")
            
        case .roomNotice, .systemLocal:
            attributedText = NSMutableAttributedString(string: "")
            attributedText.yy_font = .regular(13)
            attributedText.yy_color = .white
            addImgView(img: UIImage(named: entity.iMsgType == .roomNotice ? "msg_room_icon" : "msg_system_icon")!,showCornerRadius: false)
            addText(text: entity.txtContent ?? "")

            
        default:
            if attributedText.string.count == 0 {
                addText(text: "未知消息")
            }
        }
        
        entity.atUsers.forEach { seatInfo in
            let ranges = AppTool.findRanges(of: attributedText.string, in: seatInfo.userName ?? "")
            ranges.forEach { range in
                attributedText.yy_setTextHighlight(range, color: .themColor, backgroundColor: .clear) { vv, att, range, rect in
                    
                    if entity.iMsgType == .welcome {
                        RoomManger.shared.seatSheetCardSubject.accept(entity.atUsers.first?.userId)
                        return
                    }
                    
                    LWJumpManger.goUserPage(id: entity.atUsers.first?.userId.int)
                    
                }
            }
        }
        
        
        
        let maxSize = CGSize(width: maxMsgWidth - 30, height: .greatestFiniteMagnitude)
        let layout  = YYTextLayout(containerSize: maxSize, text: attributedText)
        msgLabel.textLayout = layout
    }
    
    func quanFU() {
        var content = ""
        
        if let info = entity, let giftName = info.giftName {
            var nickName = info.showName
            
            var rName = ""
            
            if RoomManger.shared.roomID == "\(info.liveRoomNo ?? "")" {
                rName = "本房间"
            }
            
            if info.iMsgType == .globalNotice {
                content = "恭喜 \(nickName) 在\(rName)许愿瓶中获得稀有礼物 \(giftName)"
            }
            
            if info.iMsgType == .globalNotice2 {
                content = "恭喜 \(nickName) 在\(rName)攻击幸运兔时获得稀有礼物 \(giftName)"
            }
            
            if info.iMsgType == .globalNotice3 {
                content = "恭喜 \(nickName) 在\(rName)家族狂欢中获得稀有礼物 \(giftName)"
            }
            
            addImgView(img: UIImage(named: "msg_all_affiche_icon")!,showCornerRadius: false)
            
            addText(text: content)
            
            
            let range = AppTool.findRanges(of: attributedText.string, in: nickName)
            
            attributedText.yy_setTextHighlight(range[0], color: .themColor, backgroundColor: .clear) { vv, att, range, rect in
                LWJumpManger.goUserPage(id: info.userId?.int)
            }
            
            if info.iMsgType == .globalNotice, let name = info.giftName {
                let range = (attributedText.string as NSString).range(of: name )
                attributedText.yy_setTextHighlight(range, color: .themColor, backgroundColor: .clear) { [weak self] vv, att, range, rect in
                    guard let `self` = self else { return }
                    //                    self.vModel?.vmDelegate?.showLuckyTree(index:index)
                }
            }
            
            if let url = info.giftQuietUrl {
                addMiniImgView(imgUrl: url)
            }
            
            if info.giftNum > 0 {
                addText(text: "x\(info.giftNum)")
            }
            
            
            
        }
    }
    
    
    ///添加图片
    func addImgView(img: UIImage, showCornerRadius: Bool = true) {
        let imgView = UIImageView(image: img)
        imgView.contentMode = .scaleAspectFit
        if showCornerRadius == true {
            imgView.layer.cornerRadius = img.size.height/2
        }
        let imgText = NSMutableAttributedString.yy_attachmentString(withContent: imgView, contentMode: .center, attachmentSize: img.size, alignTo: .regular(15), alignment: .center)
        attributedText.append(imgText)
        attributedText.append(NSMutableAttributedString(string: "  "))
    }
    
    func addMiniImgView(imgUrl:String) {
        let imgView = UIImageView(frame: CGRectMake(0, 0, 14, 14))
        imgView.contentMode = .scaleAspectFit
        imgView.layer.cornerRadius = 7
        imgView.setImage(from: imgUrl)
        
        let imgText = NSMutableAttributedString.yy_attachmentString(withContent: imgView, contentMode: .center, attachmentSize: imgView.size, alignTo: .regular(14), alignment: .center)
        attributedText.append(imgText)
    }
    
    func addText(text: String) {
        let textAtt = NSMutableAttributedString(string: text)
        textAtt.yy_font = .regular(12)
        textAtt.yy_color = .white
        attributedText.append(textAtt)
    }
    
    
}

