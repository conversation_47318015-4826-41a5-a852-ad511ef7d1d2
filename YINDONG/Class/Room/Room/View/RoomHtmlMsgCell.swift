//
//  RoomHtmlMsgCell.swift
//  YINDONG
//
//  Created by jj on 2025/5/14.
//

import UIKit
import Atributika
import AtributikaViews

class RoomHtmlMsgCell: UITableViewCell {
    
    // Renamed vModel to viewModel (standard Swift convention)
    
    
    // Renamed rowItem to messageEntity for more descriptiveness
    var messageEntity: RoomBaseMsgModel? {
        didSet {
            // Renamed txtContent to textContent
            if let textContent = messageEntity?.text {
      
                let text = textContent
                
                let paragraphStyle = NSMutableParagraphStyle()
                paragraphStyle.firstLineHeadIndent = 34 // Indent for the icon
                paragraphStyle.lineSpacing = 5
                   
                // Renamed attr to styledText for clarity
                let styledText = text
                    .styleByAttrs(hrefColor: .white, paragraphStyle: paragraphStyle).attributedString
                
                contentLabel.attributedText = styledText
                
            } else {
                // Clear the label if messageEntity or textContent is nil
                contentLabel.attributedText = nil
            }
        }
    }
    
    // Renamed iconImgV to iconImageView (standard Swift convention)
    lazy var iconImageView: UIImageView = {
        let imageView = UIImageView(frame: CGRect(x: 8, y: 8, width: 30, height: 16)) //
        imageView.contentMode = .scaleAspectFit
        imageView.image = UIImage(named: "msg_affiche_icon")
        return imageView
    }()
    
    // Renamed bgView to messageBackgroundView for better context
    lazy var messageBackgroundView: UIView = {
        let view = UIView(frame: .zero)
        view.backgroundColor = .white.withAlphaComponent(0.1)
        view.layer.cornerRadius = 12
        return view
    }()
    
    // contentLabel name is already quite descriptive
    lazy var contentLabel: AttributedTextView = {
        let textView = AttributedTextView(frame: .zero)
        textView.textColor = .white
        textView.font = .regular(12)
        // Using renamed constants below
        textView.preferredMaxLayoutWidth = AppTool.screenWidth * 0.65 - 30
        textView.isScrollEnabled = false
        textView.isSelectable = false // Kept as per original, though `true` might be desired for links
        textView.alwaysBounceVertical = true // Consider if this is needed when not scrollable
        textView.numberOfLines = 0
        
        var linkHighlightViewFactory = RoundedRectLinkHighlightViewFactory()
        linkHighlightViewFactory.fillColor = UIColor.clear
        linkHighlightViewFactory.enableAnimations = false
        
        textView.linkHighlightViewFactory = linkHighlightViewFactory
        textView.textContainerInset = .zero // Simplified UIEdgeInsets
        return textView
    }()
    
    // Renamed constants for clarity and consistency
    let backgroundContainerViewPadding: CGFloat = 15
    let contentLabelInternalPadding: CGFloat = 8 // Padding inside the background view, for the content label
    
    let backgroundViewTopPadding: CGFloat = 10
    let backgroundViewBottomPadding: CGFloat = 10
    let contentLabelVerticalPadding: CGFloat = 8 // Consistent padding for top/bottom of label
    let contentLabelHorizontalPadding: CGFloat = 12 // Consistent padding for left/right of label
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(messageBackgroundView)
        messageBackgroundView.addSubview(contentLabel)
        messageBackgroundView.addSubview(iconImageView) // Icon is added to background view
        
        // Constraints using SnapKit
        messageBackgroundView.snp.makeConstraints { make in
            make.top.equalTo(contentView)
            make.bottom.equalTo(contentView).inset(backgroundViewBottomPadding)
            make.left.right.equalTo(contentView).inset(backgroundContainerViewPadding)
        }
        
        
        contentLabel.snp.makeConstraints { make in
            make.top.bottom.equalTo(messageBackgroundView).inset(contentLabelVerticalPadding)
            make.left.right.equalTo(messageBackgroundView).inset(contentLabelHorizontalPadding)
        }
        
        configureEventHandlers() // Renamed for clarity
    }
    
    // Renamed for clarity and convention
    func configureEventHandlers() {
        contentLabel.onLinkTouchUpInside = { [weak self] _, value in // Added [weak self]
            guard let self = self else { return } // Safely unwrap self
            
            if let href = value as? String {
                
                if href == "type=tree" {
                    /// Navigate to wishing bottle/lucky tree
                    
                    return
                }
                
                let components = href.split(separator: "=")
                if components.count == 2 {
                    let key = String(components[0])
                    let valueString = String(components[1]) // Renamed 'value' to 'valueString' to avoid conflict
                    if key == "userId" {
                        if let imNumber = Int(valueString) {
                            /// Post notification to show user card
                            //                            NotificationCenter.default.post(name: .ShowUserInfoNotification, object: nil, userInfo: [Notification.Keys.imNumber: imNumber])
                        } else {
                            TLog("Invalid imNumber format for userId: \(valueString)")
                        }
                    }
                } else if !href.isEmpty { // Avoid logging for empty strings if that's possible
                    TLog("Link string format is incorrect: \(href)")
                }
            }
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
}
