//
//  UserDisguiseModel.swift
//  YINDONG
//
//  Created by jj on 2025/5/29.
//

import UIKit

class UserDisguiseModel: SmartCodable {
    var effectiveDate: String?
    var id: Int = 0
    var isMember: Int = 0
    var level: String?
    var popularityValue: Int = 0
    var userDecorateGoodsPropList = [DressBaseModel]()
    var userDecorateList: String?
    var userId: Int = 0
    
    
    //获取自己的聊天气泡
    func getBluueInfo() -> ChatBubbleModel? {
        
        if let model = userDecorateGoodsPropList.filter({ $0.goodsType == .chatBubble }).first {
            
            var chatModel = ChatBubbleModel()
            chatModel.buddleBL = model.goodsUrlBottomLeft
            chatModel.buddleBR = model.goodsUrlBottomRight
            chatModel.buddleFontColor = model.fontCode
            chatModel.buddleTL = model.goodsUrlTopLeft
            chatModel.buddleTR = model.goodsUrlTopRight
            chatModel.buddleUrl = model.goodsUrlPreview
            
            return chatModel
        }
        
        return nil
    }
    
    func getAvaterInfo() -> DressBaseModel? {
        let model = userDecorateGoodsPropList.filter({ $0.goodsType == .avatarFrame }).first
        return model
    }
    
    required init() {
        
    }
}

struct ChatBubbleModel: SmartCodable {
    var buddleBL: String?
    var buddleBR: String?
    var buddleFontColor: String?
    var buddleTL: String?
    var buddleTR: String?
    var buddleUrl: String?
    var friendsId: Int = 0
    //判断是否为表情包
    var isEmoji: Int = 0
    //匹配需要用到 暂时不处理
    var isTreeHoleConversation: Bool = false
    var sex: Int = 0
    var status: Int = 0
}

// MARK: - UserDisguiseModel + LocalStorage
extension UserDisguiseModel {

    // 为防止串 key，这里统一集中管理
    private enum StorageKey {
        static let disguiseCache = "com.cp.user.disguise.cache"
    }

    // MARK: 保存（覆盖）
    @discardableResult
    static func save(_ model: UserDisguiseModel?) -> Bool {
        guard let model = model else { return false }
        let json = model.toJSONString()
        UserDefaults.standard.set(json, forKey: StorageKey.disguiseCache)
        return true
    }

    // MARK: 读取
    @discardableResult
    static func load() -> UserDisguiseModel? {
        guard
            let jsonStr = UserDefaults.standard.string(forKey: StorageKey.disguiseCache),
            !jsonStr.isEmpty
        else { return nil }

        return UserDisguiseModel.deserialize(from: jsonStr)
    }

    // MARK: 更新（闭包修改 + 自动保存）
    /// - Parameter block: 在 block 里对 `model` 做修改；方法会**自动保存**修改后的数据
    /// - Returns: 更新后的模型（若读取失败则返回 `nil`）
    @discardableResult
    static func update(_ block: (inout UserDisguiseModel) -> Void) -> UserDisguiseModel? {
        guard var model = load() else { return nil }
        block(&model)
        _ = save(model)
        return model
    }

    // MARK: 清除
    static func clear() {
        UserDefaults.standard.removeObject(forKey: StorageKey.disguiseCache)
    }

}
