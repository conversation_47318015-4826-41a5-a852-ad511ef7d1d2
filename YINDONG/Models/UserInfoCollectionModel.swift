//
//  UserInfoCollectionModel.swift
//  YINDONG
//
//  Created by jj on 2025/6/24.
//

import UIKit

class UserInfoCollectionModel: SmartCodable {
    
//    var attendantList = [LW_RootClassDataAttendantList]()
    var fansCount: Int = 0
    var followCount: Int = 0
    var friendCount: Int = 0
//    var glamourLevel: LW_RootClassDataGlamourLevel?
    var personLevel: Int = 0
    var skillAttackedRecord = [String]()
    var dyCount: Int = 0
    
//    var skillLevel = [LW_RootClassDataSkillLevel]()
//    var wealthLevel: LW_RootClassDataWealthLevel?
    
    
    required init() {
        
    }

}
