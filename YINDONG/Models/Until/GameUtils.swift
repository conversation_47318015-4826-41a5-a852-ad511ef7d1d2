//
//  GameUtils.swift
//  YINDONG
//
//  Created by jj on 2025/6/16.
//

import UIKit

/// 游戏工具类
class GameUtils {
    
    /// 获取技能动效文件路径
    static func getSkillLottieFileName(skillType: SkillType, success: Bool) -> String? {
        let prefixMap: [SkillType: String] = [
            .skill1: "foot",
            .skill5: "kiss",
            .skill2: "hole",
            .skill3: "talk",
            .skill4: "catch"
        ]
        guard let name = prefixMap[skillType] else { return nil }
        return "skill_\(name)_\(success ? "success" : "fail").json"
    }
    
    
    static func skillUserDesc(isMe: Bool, bean: ChatSkillModel) -> String {
        switch bean.skillType {
        case .skill1:
            return footText(isMe, bean) + commonExtra(isMe, bean)

        case .skill5:
            return kissText(isMe, bean) + commonExtra(isMe, bean)

        case .skill3:
            return holeText(isMe, bean) + commonExtra(isMe, bean)

        case .skill2:
            return talkText(isMe, bean) + commonExtra(isMe, bean)

        case .skill4:
            return bean.success
            ? (isMe ? "将其抓捕为跟班" : "被对方抓捕为跟班")
            : (isMe ? "使用了套麻袋，可惜失败了" : "向你使用了套麻袋，可惜失败了")

        }
    }

    /// 判断是否为亲吻技能
    static func isKissSkill(skillType: SkillType) -> Bool {
        return skillType == .skill5
    }

    // MARK: - 单技能主文本（极简，无重复）-----------------------------------------

    static func footText(_ me: Bool, _ b: ChatSkillModel) -> String {
        if b.success {
            return me
            ? "成功将其踢出当前房间\(b.value)分钟且对方经验值-\(b.getLongPersonPoints())，我的战神值+\(b.getLongPowerPoints())"
            : "被踢出当前房间\(b.value)分钟且经验值-\(b.getLongPersonPoints())"
        } else {
            return me ? "使用了旋风腿" : "向你使用了旋风腿"
        }
    }

    static func kissText(_ me: Bool, _ b: ChatSkillModel) -> String {
        if b.success {
            return me
            ? "对方魅力值+\(b.getLongCharm())"
            : "我的魅力值+\(b.getLongCharm())"
        } else {
            return me ? "使用了么么哒，可惜失败了" : "向你使用了么么哒，可惜失败了"
        }
    }

    static func holeText(_ me: Bool, _ b: ChatSkillModel) -> String {
        if b.success {
            return me
            ? "成功将其禁言\(b.value)分钟"
            : "当前房间禁言\(b.value)分钟"
        } else {
            return me ? "使用了点穴手" : "向你使用了点穴手"
        }
    }

    static func talkText(_ me: Bool, _ b: ChatSkillModel) -> String {
        if b.success {
            return me
            ? "使其头顶猪头胡言乱语\(b.value)次，我的经验值+\(b.getLongPersonPoints())"
            : "被头顶猪头胡言乱语\(b.value)次"
        } else {
            return me ? "使用了变猪头，可惜失败了" : "向你使用了变猪头，可惜失败了"
        }
    }

    // MARK: - 🌟 通用「突破技 & 肉盾」拼接 ----------------------------------------
    static func commonExtra(_ me: Bool, _ b: ChatSkillModel) -> String {
        // Rune 石描述 —— 成功与失败顺序不同
        let rune = b.success
        ? "\(b.getRuneStoneHitDesc())\(b.getRuneStoneHitDesc2(isSelf: me))"
        : "\(b.getRuneStoneHitDesc())\(b.getRuneStoneHitDesc2(isSelf: me))"
        
        // 技能专属额外文本
        let extra: String = {
            if b.success {
                switch b.skillType {
                case .skill1:
                    return b.triggerBreak == 0 ? "，打出破财效果元宝\(me ? "+" : "-")\(b.yuanbao)" : ""
                case .skill5:
                    return b.triggerBreak == 0
                    ? "，产生净化效果解除\(me ? "对方" : "身上")的\(b.negativeEffectName ?? "")负面效果" : ""
                case .skill3:
                    if b.runeFengyin == 0, b.triggerBreak == 0,
                       b.randomSkillName == b.skillType.title {
                        return ""
                    }
                    return b.triggerBreak == 0
                    ? "，\(b.randomSkillName)技能被封印\(b.chaosTime)分钟" : ""
                case .skill2:
                    return b.triggerBreak == 0 ? "，打出致盲效果对方下一次使用技能必能失败" : ""
                default: return ""
                }
            } else {  // 失败场景
                switch b.skillType {
                case .skill1, .skill2:
                    return b.meatShield == 0 ? "触发肉盾效果PK失败了" : "，可惜失败了"
                default:
                    return ""
                }
            }
        }()
        
        return rune + extra
    }

    // MARK: - SkillModel 对应的文本方法

    static func footTextForSkill(_ me: Bool, _ b: ChatSkillModel) -> String {
        if b.success {
            return me
            ? "成功将其踢出当前房间\(b.value)分钟且对方经验值-\(b.personPoints)，我的战神值+\(b.powerPoints)"
            : "被踢出当前房间\(b.value)分钟且经验值-\(b.personPoints)"
        } else {
            return me ? "使用了旋风腿" : "向你使用了旋风腿"
        }
    }

    static func kissTextForSkill(_ me: Bool, _ b: ChatSkillModel) -> String {
        if b.success {
            return me
            ? "对方魅力值+\(Int(b.charm))"
            : "我的魅力值+\(Int(b.charm))"
        } else {
            return me ? "使用了么么哒，可惜失败了" : "向你使用了么么哒，可惜失败了"
        }
    }

    static func holeTextForSkill(_ me: Bool, _ b: ChatSkillModel) -> String {
        if b.success {
            return me
            ? "成功将其禁言\(b.value)分钟"
            : "当前房间禁言\(b.value)分钟"
        } else {
            return me ? "使用了点穴手" : "向你使用了点穴手"
        }
    }

    static func talkTextForSkill(_ me: Bool, _ b: ChatSkillModel) -> String {
        if b.success {
            return me
            ? "使其头顶猪头胡言乱语\(b.value)次，我的经验值+\(b.personPoints)"
            : "被头顶猪头胡言乱语\(b.value)次"
        } else {
            return me ? "使用了变猪头，可惜失败了" : "向你使用了变猪头，可惜失败了"
        }
    }
    
    
    
    static func skillHitDesc(model: RoomBaseMsgModel,
                      breakSkillHitDesc: String) -> String {
        
        let skillName = model.skillType?.title ?? ""
        
        switch model.skillType ?? .skill1 {

        case .skill1:   // 旋风腿
            let minutes = model.value ?? 0
            return "的\(skillName)击中，持续\(minutes)分钟\(breakSkillHitDesc)"

        case .skill2:   // 点穴手
            let minutes = model.value ?? 0
            return "的\(skillName)击中，禁言\(minutes)分钟\(breakSkillHitDesc)"

        case .skill3:   // 变猪头
            let times = model.value ?? 0
            return "的\(skillName)击中，顶着猪头胡言乱语\(times)次\(breakSkillHitDesc)"

        case .skill4:   // 套麻袋
            return "的\(skillName)击中，沦为他/她的跟班"

        case .skill5:   // 么么哒
            let charmPlus = model.charm ?? 0
            return "的\(skillName)击中，魅力值+\(charmPlus)\(breakSkillHitDesc)"
        }
    }
    

}


/*
 
 var skillType: SkillType = .skill1
 var otherUserImNumber: String = ""       // 被攻击者 IM 编号
 var otherUserNickNumber: String = ""     // 被攻击者 昵称编号
 var userImNumber: String = ""            // 攻击者 IM 编号
 var userNickName: String = ""            // 攻击者昵称
 var value: String = ""                   // 技能附加数值
 var inMask: Int = 0                      // 是否处于面具状态
 var selfAvatarId: Int = 0                // 自己的头像ID
 var otherAvatarId: Int = 0               // 对方头像ID
 var isChangeDesc: Bool = false           // 是否使用自定义描述
 var charm: Double? = 0.0                 // 魅力值
 
 var triggerBreak: Int = 0                // 是否打出突破技 0是 1否
 var yuanbao: Int = 0                     // 元宝数
 var negativeEffectName: String? = ""     // 净化的负面效果名
 var randomSkillName: String = ""         // 被封印的技能名
 var chaosTime: Int = 0                   // 封印时长（分钟）
 
 var runeFengyin: Int = 1                 // 是否触发符石封印 0是 1否
 var runeTianmi: Int = 1                  // 是否触发符石甜蜜 0是 1否
 
 /// 魅力值转为 Int64
 func getLongCharm() -> Int64 {
     return Int64(charm ?? 0.0)
 }
 
 /// 获取突破技能效果描述（包含符石描述）
 func getBreakSkillHitDesc() -> String {
     let skillName = GameUtils.getSkillName(skillType: skillType)
     let runeDesc = getRuneStoneHitDesc() + getRuneStoneHitDesc2(skillName: skillName)
     
     let desc: String
     switch skillType {
     case .skill1:
         desc = (triggerBreak == 0) ? "，被打出破财效果元宝-\(yuanbao)" : ""
     case .skill5:
         let name = negativeEffectName ?? ""
         desc = (triggerBreak == 0) ? "，产生净化效果解除身上的\(name)负面效果" : ""
     case .skill2:
         desc = (triggerBreak == 0) ? "，被打出封印效果\(randomSkillName)技能被封印\(chaosTime)分钟" : ""
     case .skill3:
         desc = (triggerBreak == 0) ? "，被打出致盲效果下一次使用技能必能失败" : ""
     default:
         desc = ""
     }
     return runeDesc + desc
 }
 
 /// 获取符石甜蜜效果描述
 private func getRuneStoneHitDesc() -> String {
     return runeTianmi == 0 ? "，恋爱指数+1" : ""
 }
 
 /// 获取符石封印效果描述
 private func getRuneStoneHitDesc2(skillName: String) -> String {
     return runeFengyin == 0 ? "，\(skillName)被封印5分钟" : ""
 }
 */
