//
//  LWUserManger.swift
//  YINDONG
//
//  Created by jj on 2025/2/27.
//

import UIKit
import DeviceKit

let kUserPhoneKey = "UserPhoneKey"

// 房间设置相关的键
private let kAnnouncementDisplayKey = "AnnouncementDisplayKey"
private let kGiftEffectTypeKey = "GiftEffectTypeKey"

// 公告显示类型枚举
enum AnnouncementDisplayType: Int, CaseIterable {
    case showAll = 0           // 展示所有全服公告
    case showRoomOnly = 1      // 只展示本房间全服公告
    
    var title: String {
        switch self {
        case .showAll:
            return "展示所有全服公告"
        case .showRoomOnly:
            return "只展示本房间全服公告"
        }
    }
}

// 礼物特效类型枚举
enum GiftEffectType: Int, CaseIterable {
    case showAll = 0           // 展示所有礼物特效
    case showExpensive = 1     // 只展示达到500钻石的礼物特效
    case showNone = 2          // 所有礼物特效都不展示
    
    var title: String {
        switch self {
        case .showAll:
            return "展示所有礼物特效"
        case .showExpensive:
            return "只展示达到500钻石的礼物特效"
        case .showNone:
            return "所有礼物特效都不展示"
        }
    }
}


/// 获取游戏账户信息的类型
enum UserAccountInfoType: Int {
    case all              = 0   // 所有账户信息
    case currencyOnly     = 1   // 货币相关（钻石、元宝、积分）
    case itemOnly         = 2   // 道具相关（绿、紫、防爆、突破石）
    
    // 单项货币
    case diamond          = 10  // 钻石
    case ingot            = 11  // 元宝

    // 单项道具
    case greenCrystal     = 20  // 绿晶石
    case purpleCrystal    = 21  // 紫晶石
    case antiBreakStone   = 22  // 防爆石
    case breakthroughStone = 23 // 突破石
    case userPower = 30 // 最新战力
}

var kuser: LWUserModel {
    return LWUserManger.shared.currentUserInfo ?? LWUserModel()
}

var appCodelw: Bool {
    return false//LWUserManger.shared.isAppCode == "Y"
}

class LWUserManger {
    
    var isAppCode: String = "N"
    
    private let kUserInfoKey = "UserInfoKey"
    
    private var currentUser: LWUserModel?
    private let userDefaults = UserDefaults.standard
    
    static let shared = LWUserManger()
    
    var rechargeModel: UserRechargeModel?
    
    var featureConfig: UserFeatureConfig?
    
    var auth: AuthData?
    
    var trtcModel: TRTCInfoModel?
    
    var userTitles: [UserTitleModel] = []
    
    ///用户贵族信息
    var nobInfo: UserNobleInfo?
    
    var myAttCount: Int = 0
    
    //一些关键信息
    var infoCollectionModel: UserInfoCollectionModel? {
        return infoRelay.value
    }
    
    let infoRelay = BehaviorRelay<UserInfoCollectionModel?>(value: nil)

    // MARK: - 房间设置相关属性
    
    /// 公告显示类型
    var announcementDisplayType: AnnouncementDisplayType {
        get {
            let rawValue = userDefaults.integer(forKey: kAnnouncementDisplayKey)
            return AnnouncementDisplayType(rawValue: rawValue) ?? .showAll
        }
        set {
            userDefaults.set(newValue.rawValue, forKey: kAnnouncementDisplayKey)
            userDefaults.synchronize()
        }
    }
    
    /// 礼物特效类型
    var giftEffectType: GiftEffectType {
        get {
            let rawValue = userDefaults.integer(forKey: kGiftEffectTypeKey)
            return GiftEffectType(rawValue: rawValue) ?? .showAll
        }
        set {
            userDefaults.set(newValue.rawValue, forKey: kGiftEffectTypeKey)
            userDefaults.synchronize()
        }
    }
    
    // MARK: - 房间设置相关方法
    
    /// 设置公告显示类型
    func setAnnouncementDisplay(_ type: AnnouncementDisplayType) {
        announcementDisplayType = type
    }
    
    /// 设置礼物特效类型
    func setGiftEffectType(_ type: GiftEffectType) {
        giftEffectType = type
    }
    
    // 保存用户信息
    func saveUser(user: LWUserModel?) {
        guard let user = user else { return }
        
        currentUser = user
        // 将用户信息转换为 JSON 字符串并保存
        if let userJsonString = user.toJSONString() {
            userDefaults.set(userJsonString, forKey: kUserInfoKey)
            userDefaults.synchronize()
        }
    }
    
    // 保存用户手机号
    static func savePhoneNumber(_ phoneNumber: String?) {
        UserDefaults.standard.set(phoneNumber, forKey: kUserPhoneKey)
    }
    
    // 获取用户手机号
    var phoneNumber: String? {
        return UserDefaults.standard.string(forKey: kUserPhoneKey)
    }
    
    // 更新用户信息
    func updateUser(_ update: (inout LWUserModel) -> Void) {
        if var user = currentUser {
            update(&user)
            saveUser(user: user)
        }
    }
    
    // 获取当前用户信息
    var currentUserInfo: LWUserModel? {
        if let user = currentUser {
            return user
        }
        
        // 如果内存中没有，尝试从本地加载
        if let userJson = userDefaults.string(forKey: kUserInfoKey),
           let user = LWUserModel.deserialize(from: userJson) {
            currentUser = user
            return user
        }
        return nil
    }
    
    
    // 保存 Token
    static func saveToken(_ token: String) {
        UserDefaults.standard.set(token, forKey: "userToken")
        UserDefaults.standard.synchronize() // 确保数据写入
    }
    
    // 清除 Token
    static func clearToken() {
        UserDefaults.standard.removeObject(forKey: "userToken")
        UserDefaults.standard.synchronize()
    }
    
    // 获取 Token（可选）
    static func getToken() -> String? {
        return UserDefaults.standard.string(forKey: "userToken")
    }
    
    var mainWindow: UIWindow? {
        return UIApplication.shared.delegate?.window ?? nil
    }
    
    func initializeRootViewController() {
        
        guard let window = mainWindow else { return }
        
        if let token = LWUserManger.getToken(), token.count > 0 {
            
            NetworkUtility.request(target: .appVersion) { result in
                if result.isError { return }
                LWUserManger.shared.isAppCode = result.dataJson?.rawString() ?? ""
                
                
                NetworkUtility.request(target: .userInfo, model: LWUserModel.self) { result in
                    if result.isError { return }
                    LWUserManger.shared.saveUser(user: result.model)
                    self.configureRootViewController(MainTabBarVC(), animated: true, in: window)
                    T_IMHelper.shared.loginToChat()
                }
                
            }
            
        } else {
            configureRootViewController(NavigationVC(rootViewController: LoginVC()), animated: true, in: window)
        }
    }
    
    func configureRootViewController(_ rootViewController: UIViewController, animated: Bool, in window: UIWindow) {
        if animated {
            let transition = CATransition()
            transition.subtype = .fromLeft
            transition.type = .fade
            transition.duration = 0.3
            transition.isRemovedOnCompletion = true
            window.layer.add(transition, forKey: nil)
        }
        window.rootViewController = rootViewController
    }
    
    func logoutUser(shouldReinitialize: Bool = true, isNet: Bool = false) {
        if isNet {
            NetworkUtility.request(target: .loginOut) { result in
                if result.isError { return }
                
                
            }
        }
        currentUser = nil
        T_IMHelper.shared.loginOut()
        LWUserManger.clearToken()
        QuickManger.shared.clear()
        if shouldReinitialize {
            initializeRootViewController()
        }
        ///注销所有的定时任务
        TickHub.shared.unregisterAll()
    }
    
    
    ///关注用户
    func userFollowAction(isFollow: Bool, imid: Int, callBack: (() ->Void)?) {
        
        if isFollow {
            let alert = MessagePopupView(title: "提示", content: "确定不再关注对方吗？")
            alert.confirmButton.setTitle("不再关注", for: .normal)
            alert.onAction = { isc, vv in
                if isc { return }
                
                NetworkUtility.request(target: .userCancelFollow(["imId": imid])) { result in
                    if result.isError { return }
                    callBack?()
                    ProgressHUDManager.showTextMessage("已取消关注")
                    NotificationCenter.default.post(name: .delFollowNotification, object: ["imid": imid])
                    
                    self.getUserBaseCount()
                }
                
            }
            alert.show()
            return
        }
        
        NetworkUtility.request(target: .userFollow(["imId": imid])) { result in
            if result.isError { return }
            callBack?()
            ProgressHUDManager.showTextMessage("关注成功")
            NotificationCenter.default.post(name: .followNotification, object: ["imid": imid])
            self.getUserBaseCount()
        }
        
    }
    
    func userBlackAction(isBlack: Bool, imid: Int, callBack: (() ->Void)?) {
        if isBlack {
            let alert = MessagePopupView(title: "温馨提示", content: "拉黑后将取消关注，不再收到对方发来的消息，可在\"设置-黑名单\"中解除，确定拉黑？")
            alert.onAction = { isC, vv in
                if isC { return }
                
                NetworkUtility.request(target: .addBlack(["imNumber": imid])) { reuslt in
                    if reuslt.isError { return }
                    ProgressHUDManager.showTextMessage("已拉黑")
                    callBack?()
                    
                    T_IMHelper.shared.sessionRemove(conversationID: "c2c_\(imid)") {
                        NotificationCenter.default.post(name: .addBlackNotification, object: imid)
                    }
                }
            }
            
            alert.show()
            
            return
        }
        
        
        NetworkUtility.request(target: .removeBlack(["imNumber": imid])) { reuslt in
            if reuslt.isError { return }
            ProgressHUDManager.showTextMessage("已移除黑名单")
            callBack?()
        }
        
        
    }
    
    ///获取用户 IM 在线状态
    static func getOnline(imid: Int, callBack: ((Bool) ->Void)?) {
        NetworkUtility.request(target: .userOnline(["imNumber": imid])) { result in
            if result.isError { return }
            let isOnline = result.dataJson?.boolValue ?? false
            callBack?(isOnline)
        }
    }
    
    
    static func appInfoUpload() {
        var dict: [String: Any] = [:]
        dict["channel"] = AppTool.appChannel
        dict["appVersion"] = AppTool.appVersion
        dict["equipmentModel"] = Device.current.description
        dict["systemVersion"] = "14"
        dict["deviceId"] = TwKey.fetchDeviceID()
        dict["networkType"] = "wifi"
        
        NetworkUtility.request(target: .appInfoBegain(dict), completion: { result in
            if result.isError { return }
            TLog("app信息已经上传")
        })
    }
    
    ///获取一些其他的信息
    func getFeature(callBack: (() -> Void)? = nil) {
        
        NetworkUtility.request(target: .getUserFunction, model: UserFeatureConfig.self, completion: { result in
            if result.isError { return }
            self.featureConfig = result.model
        })
        
        
        NetworkUtility.request(target: .getUserIdAuthenticationRecord, model: AuthData.self, completion: { result in
            if result.isError { return }
            self.auth = result.model
            callBack?()
        })
        
    }
    
    func getSignTrtc() {
        
        NetworkUtility.request(target: .getTRTCSign, model: TRTCInfoModel.self) { result in
            if result.isError { return }
            self.trtcModel = result.model
        }
        
    }
    
    ///获取用户装扮信息
    func getUserDis() {
        
        NetworkUtility.request(target: .userDisguise, model: UserDisguiseModel.self) { result in
            if result.isError { return }
            UserDisguiseModel.save(result.model)
        }
        
    }
    
    ///获取用户称号列表
    func getUserTitleList() {
        
        NetworkUtility.request(target: .userTitleList, model: UserTitleModel.self, isList: true) { result in
            if result.isError { return }
            self.userTitles = result.modelArr
        }
    }
    
    ///获取用户贵族信息
    func getUserNob() {
        NetworkUtility.request(target: .userNob, model: UserNobleInfo.self) { result in
            if result.isError { return }
            self.nobInfo = result.model
        }
    }
    
    ///获取最新的战力
    func getUserPower() {
    
        getAccountInfo(type: .userPower) { m in
            self.myAttCount = m?.fightingPoints ?? 0
            NotificationCenter.default.post(name: .skillAllJumpNotification, object: nil)
        }
    }
    
    // 获取账户信息
    func getAccountInfo(type: UserAccountInfoType, callBack: ((_ m: UserRechargeModel?) -> Void)?) {
        
        NetworkUtility.request(target: .getRechargeInfo(["type": type.rawValue]), model: UserRechargeModel.self) { result in
            if result.isError { return }
            
            if type == .currencyOnly {
                LWUserManger.shared.rechargeModel = result.model
            }
            callBack?(result.model)
        }
    }
    
    // 获取关注 和 粉丝
    func getUserBaseCount() {
        
        NetworkUtility.request(target: .userCountInfo, model: UserInfoCollectionModel.self) { result in
            if result.isError { return }
            self.infoRelay.accept(result.model)
            
            //同步刷新一下
            self.getDyCount()
        }
    }
    
    // 获取动态数量
    func getDyCount(callBack: ((_ m: Int) -> Void)? = nil, imId: String? = kuser.imNumber.string) {
        NetworkUtility.request(target: .getDynamicCount(["type": 1, "imId": imId ?? ""])) { result in
            if result.isError { return }
            
            let count = result.dataJson?.intValue ?? 0
            if imId == nil {
                self.infoCollectionModel?.dyCount = count
                self.infoRelay.accept(self.infoCollectionModel)
            }
            
            callBack?(count)
        }
    }
    
}


struct TRTCInfoModel: SmartCodable {
    var appID: String?
    var genSig: String?
}
